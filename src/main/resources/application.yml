server:
  compression:
    enabled: true
    min-response-size: 1024
    mime-types:
      - image/png
      - text/html
      - application/javascript
      - text/css
      - application/octet-stream
      - application/json
  port: 8080
  tomcat:
    accept-count: 1000
    min-spare-threads: 10
    max-threads: 1024
    max-connections: 65535
  servlet:
    context-path: /lcs
    session:
      timeout: 10s

logging:
  config: classpath:logback-spring.xml
  file:
    path: /vdb/apilog/fund-financial-planner-api
  level:
    com.alibaba.druid: error

spring:
  profiles:
    active: @spring_profile@
  servlet:
    multipart:
      # 上传单个文件默认最大大小为20MB
      max-file-size: 20MB
  datasource:
    dynamic:
      primary: master
      strict: false
      druid:
        initial-size: 3
        min-idle: 1
        max-active: 100
        max-wait: 1000
        keep-alive: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 30000
        test-while-idle: true
        validation-query: select 1
        pool-prepared-statements: false
        fail-fast: true

mybatis-plus:
  mapper-locations: classpath:/mapper/*.xml
  global-config:
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    log-impl:  org.apache.ibatis.logging.slf4j.Slf4jImpl