package ttfund.web.financialplannerapi.service;

import com.ttfund.web.core.model.ApiResponse;
import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import ttfund.web.financialplannerapi.model.reponse.*;
import ttfund.web.financialplannerapi.model.request.*;

import java.util.List;

public interface IContractService {
    /**
     * 查签约状态
     * @param passportId 用户通行证id
     * @param institutionId 签约机构id
     * @param financialPlannerId 理财师id
     * @param result 用户是否签约，已签约返回当前理财师id，未签约返回分配签约理财师的基本信息
     * @return
     */
    ApiResponse<ContractStatusResponse, Object> status(String passportId, String institutionId, String financialPlannerId, ApiResponse<ContractStatusResponse, Object> result);

    /**
     * 签约
     * @param request
     * @param result
     * @return
     */
    ApiResponse<Boolean, Object> contract(ContractRequest request, ApiResponse<Boolean, Object> result);

    /**
     * 解约
     * @param request 服务解约请求信息
     * @param result 用于传递解约结果或错误信息
     * @return 解约是否成功
     */
    ApiResponse<Boolean, Object> cancelContract(CancelContractRequest request, ApiResponse<Boolean, Object> result);

    /**
     * 更换理财师
     * @param request
     * @param result
     * @return
     */
    ApiResponse<ChangePlannerResponse, Object> change(ChangePlannerRequest request, ApiResponse<ChangePlannerResponse, Object> result);

    /**
     * 根据用户通行证查绑定的理财师
     * @param passportId
     * @param result
     * @return
     */
    ApiResponse<List<UserPlannerResponse>, Object> getPlannerByUser(String passportId, ApiResponse<List<UserPlannerResponse>, Object> result);

    /**
     * 查询是否已解约
     * @param request
     * @return true=存在签约关系，false=已解约
     */
    ApiResponse<Boolean, Object> isCancel(QueryContractRequest request);

    /**
     * 查询理财师悬浮窗
     * @param request LcsWindowRequest
     * @return LcsWindowResponse
     */
    ApiResponse<List<LcsWindowResponse>, Object> getLcsWindow(LcsWindowRequest request);
    /**
     * 根据用户通行证查所有绑定的理财师
     * @param passportId
     * @param result
     * @return
     */
    ApiResponse<List<UserPlannerResponse>, Object> getAllPlannerByUser(String passportId, ApiResponse<List<UserPlannerResponse>, Object> result);

    /**
     * 根据通行证为用户绑定理财师并返回绑定理财师信息
     * @param request 请求参数
     * @param result 绑定理财师信息
     * @return
     */
    ApiResponse<BindStatusResponse, Object> bindPlanner(AuthBaseRequest request, ApiResponse<BindStatusResponse, Object> result);

    ApiResponse<UserTTPlannerResponse, Object> getTTPlannerByUser(AuthBaseRequest request);

    ApiResponse<List<TTFundUserContractResponse>, Object> batchTTFundUserContract(TTFundUserContractRequest request);

    ApiResponse<UserPlannerInfoResponse, Object> contractInstWithCache(QueryContractRequest request);

    /**
     * 查询理财师悬浮窗(配置后台)
     * @param request LcsWindowRequest
     * @return LcsWindowResponse
     */
    ApiResponse<List<LcsWindowResponse>, Object> getLcsConfigWindow(LcsWindowRequest request);
}
