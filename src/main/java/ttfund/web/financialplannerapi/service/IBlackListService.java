package ttfund.web.financialplannerapi.service;

import ttfund.web.financialplannerapi.exception.BusinessException;
import ttfund.web.financialplannerapi.model.reponse.AddBlackListResponse;
import ttfund.web.financialplannerapi.model.reponse.PlannerBlackListResponse;
import ttfund.web.financialplannerapi.model.reponse.RemoveBlackListResponse;
import ttfund.web.financialplannerapi.model.request.AddBlackListRequest;
import ttfund.web.financialplannerapi.model.request.PlannerBlackListRequest;
import ttfund.web.financialplannerapi.model.request.RemoveBlackListRequest;

public interface IBlackListService {


    AddBlackListResponse addToBlackList(AddBlackListRequest request) throws BusinessException;

    RemoveBlackListResponse removeBlackList(RemoveBlackListRequest request) throws BusinessException;

    PlannerBlackListResponse getBlackList(PlannerBlackListRequest request) throws BusinessException;
}
