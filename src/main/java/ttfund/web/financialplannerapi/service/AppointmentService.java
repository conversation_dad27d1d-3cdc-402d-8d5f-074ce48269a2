package ttfund.web.financialplannerapi.service;

import com.ttfund.web.core.model.ApiResponse;
import ttfund.web.financialplannerapi.model.reponse.AppointmentAvailableResponse;
import ttfund.web.financialplannerapi.model.reponse.AppointmentInfoResponse;
import ttfund.web.financialplannerapi.model.request.AddAppointmentRequest;
import ttfund.web.financialplannerapi.model.request.AppointmentQueryRequest;

/**
 * <AUTHOR>
 * @date 2022/8/22
 * @Description 电话预约服务
 */
public interface AppointmentService {
    /**
     * 查询预约信息
     * @param params AppointmentQueryRequest 查询参数
     * @return ApiResponse<AppointmentInfoResponse, Object>
     */
    ApiResponse<AppointmentInfoResponse, Object> queryAppointmentInfo(AppointmentQueryRequest params);

    /**
     * 查询可预约时段
     * @param params AppointmentQueryRequest
     * @return ApiResponse<AppointmentAvailableResponse, Object>
     */
    ApiResponse<AppointmentAvailableResponse, Object> queryCanBeAppointment(AppointmentQueryRequest params);

    /**
     * 新增或修改预约
     * @param request AddAppointmentRequest
     * @return ApiResponse<Boolean, Object>
     */
    ApiResponse<Boolean, Object> addOrUpdateAppointment(AddAppointmentRequest request);

    /**
     * 取消预约
     * @param request AppointmentQueryRequest
     * @return ApiResponse<Boolean, Object>
     */
    ApiResponse<Boolean, Object> cancelAppointment(AppointmentQueryRequest request);
}
