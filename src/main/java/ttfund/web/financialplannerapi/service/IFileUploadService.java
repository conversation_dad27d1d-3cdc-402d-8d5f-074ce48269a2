package ttfund.web.financialplannerapi.service;

import com.ttfund.web.core.model.ApiResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * @InterfaceName IFileUploadService
 * @Description
 * <AUTHOR>
 * @Date 2022/5/25 9:28
 * @Vserion 1.0.0
 */
public interface IFileUploadService {

    /**
     * @Description： 上传图片到ftp
     * <AUTHOR>
     * @Date 2022/5/25 10:02
     * @param type 0：上半身头像；1：反馈意见图片
     * @param img 图片
     * @return
     */
    ApiResponse<String, Object> uploadFTP(int type, MultipartFile img, String passportId);
}
