package ttfund.web.financialplannerapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import ttfund.web.financialplannerapi.model.bo.sys.UserInfoPageReqBO;
import ttfund.web.financialplannerapi.model.bo.sys.UserInfoResBO;
import ttfund.web.financialplannerapi.model.po.sys.UserInfoPO;
import ttfund.web.financialplannerapi.model.reponse.PageCountListResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserInfoService extends IService<UserInfoPO> {

    /**
     * 获取列表(单表)
     * @param param 参数
     * @return 查询结果
     */
    PageCountListResponse<UserInfoResBO> listByPure(UserInfoPageReqBO param);


    /**
     * 根据通信中id查用户信息
     * @param passportId 用户通行证id
     * @return 查询结果
     */
    UserInfoResBO infoByPassportId(String passportId);
}
