package ttfund.web.financialplannerapi.service;

import ttfund.web.financialplannerapi.exception.BusinessException;
import ttfund.web.financialplannerapi.model.request.EvaluateAddRequest;
import ttfund.web.financialplannerapi.model.request.FeedBackAddRequest;

public interface IEvaluateService {

    Boolean userEvaluate(EvaluateAddRequest request) throws BusinessException;

    Boolean userFeedBack(FeedBackAddRequest request) throws BusinessException;
}
