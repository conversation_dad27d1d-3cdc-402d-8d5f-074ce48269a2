package ttfund.web.financialplannerapi.service.impl;

import com.google.common.hash.Hashing;
import com.ttfund.web.core.constant.ResultCode;
import com.ttfund.web.core.model.ApiResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import ttfund.web.financialplannerapi.config.FTPConfig;
import ttfund.web.financialplannerapi.service.IFileUploadService;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;

/**
 * @ClassName FileUploadServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/25 9:30
 * @Vserion 1.0.0
 */
@Service
public class FileUploadServiceImpl implements IFileUploadService {

    private static final Logger logger = LoggerFactory.getLogger(FileUploadServiceImpl.class);

    @Resource
    private FTPConfig ftpConfig;

    @Override
    public ApiResponse<String, Object> uploadFTP(int type, MultipartFile img, String passportId) {
        FTPClient ftpClient = null;
        try {
            ApiResponse<String, Object> result = new ApiResponse<>();
            if (null == img) {
                this.assembleErrorReturn(result, ResultCode.errorcode501, "上传图片为空");
                return result;
            }

            // 校验图片的大小和格式
            this.checkIsPicOrSize(img, result);
            if (0 != result.getErrorCode()) {
                return result;
            }

            // 1、创建FTP客户端
            ftpClient = new FTPClient();
            try {
                ftpClient.connect(ftpConfig.ftp_ip, ftpConfig.ftp_port);
//            logger.info(String.format("ftp_id=%s, ftp_port=%s", ftpConfig.ftp_ip, ftpConfig.ftp_port));
                boolean login = ftpClient.login(ftpConfig.ftp_username, ftpConfig.ftp_password);
//            logger.info(String.format("ftp_username=%s, ftp_password=%s", ftpConfig.ftp_username, ftpConfig.ftp_password));
                if (!login) {
                    this.assembleErrorReturn(result, ResultCode.errorcode501, "ftp连接失败，用户名或密码错误");
                    return result;
                }
            } catch (Exception e) {
                this.assembleErrorReturn(result, ResultCode.errorcode501, "ftp连接失败");
                logger.error(e.getMessage(),e);
                return result;
            }

            // 2、设置文件传输路径
            String path = ftpConfig.ftp_prefixPath;
            if (!path.startsWith("/")) {
                path = "/" + path;
            }
            if (!path.endsWith("/")) {
                path = path + "/";
            }

            // 3、设置图片名称
            String picName = "";
            try {
                picName = Hashing.md5().newHasher().putBytes(img.getBytes()).hash().toString();
            } catch (Exception e) {
                logger.error("获取图片hashcode失败~~~");
                this.assembleErrorReturn(result, ResultCode.errorcode501, "上传图片失败");
                logger.error(e.getMessage(),e);
                return result;
            }
            if (0 == type) {
                // 上传半身像图片
                picName = "upper_body_avatar_" + picName;
            } else {
                // 上传意见反馈图片
                picName = "feedback_" + picName;
            }

            // 4、开启被动模式：防止linux某些端口未开放，由linux主动开放端口进行文件传输
            ftpClient.enterLocalPassiveMode();
            try {
                // 5、设置二进制文件传输模式：保证传输内容正确
                ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
                // 6、创建多级目录，同时切换至最里层目录
                boolean makeDirectory = this.makeDir(ftpClient, path);
                if (!makeDirectory) {
                    this.assembleErrorReturn(result, ResultCode.errorcode501, "创建目录失败");
                    return result;
                }
            } catch (Exception e) {
                this.assembleErrorReturn(result, ResultCode.errorcode501, "创建目录失败");
                logger.error(e.getMessage(),e);
                return result;
            }

            // 7、上传文件到ftp
            String imgName = "";
            try {
                InputStream inputStream = img.getInputStream();
                String[] uploadImgName = img.getOriginalFilename().split("\\.");
                imgName = picName + "." + uploadImgName[uploadImgName.length - 1].toLowerCase(); // 格式：passportId.jpg、passportId.png ...
                boolean storeFile = ftpClient.storeFile(imgName, inputStream);
                if (!storeFile) {
                    this.assembleErrorReturn(result, ResultCode.errorcode501, "上传图片到ftp失败");
                    logger.error("上传图片到ftp失败");
                    return result;
                }
            } catch (Exception e) {
                this.assembleErrorReturn(result, ResultCode.errorcode501, "上传图片到ftp失败");
                logger.error(e.getMessage(),e);
                return result;
            }

            // 上传成功
            String prefixUrl = ftpConfig.ftp_access_url;
            if (!ftpConfig.ftp_access_url.endsWith("/")) {
                prefixUrl = prefixUrl + "/";
            }
            result.setData(prefixUrl + imgName);
            return result;
        } finally {
            if (null != ftpClient) {
                try {
                    ftpClient.logout();
                    if (ftpClient.isConnected()) {
                        ftpClient.disconnect();
                    }
                } catch (Exception e) {
                    logger.error(e.getMessage(),e);
                }
            }
        }
    }

    /**
     * @Description： 处理错误返回信息
     * <AUTHOR>
     * @Date 2022/5/25 10:06
     * @param result result
     * @param errorCode errorCode
     * @param msg msg
     * @return
     */
    private void assembleErrorReturn(ApiResponse<String, Object> result, int errorCode, String msg) {
        result.setData(null);
        result.setErrorCode(errorCode);
        result.setFirstError(msg);
    }

    /**
     * @Description： ftp创建多级目录
     * <AUTHOR>
     * @Date 2022/5/25 10:35
     * @param ftpClient ftpClient
     * @param dirPath dirPath
     * @return
     */
    private boolean makeDir(FTPClient ftpClient, String dirPath) throws IOException {
        String[] paths = dirPath.split("/");
        boolean isMakeSuccess = false;
        boolean changeSuccess = true;
        for (String path : paths) {
            if (StringUtils.isEmpty(path)) {
                continue;
            }
            // 切换目录，判断当前目录是否存在
            changeSuccess = ftpClient.changeWorkingDirectory(path);
            if (!changeSuccess) {
                // 切换失败，目录不存在
                isMakeSuccess = ftpClient.makeDirectory(path);
                ftpClient.changeWorkingDirectory(path);
            }
        }
        return isMakeSuccess || changeSuccess;
    }

    /**
     * @Description： 校验是否为图片格式和图片大小(图片格式: jpg, png)
     * <AUTHOR>
     * @Date 2022/6/14 13:16
     * @param img img
     * @param result result
     * @return
     */
    private void checkIsPicOrSize(MultipartFile img, ApiResponse<String, Object> result) {
        // 1、校验图片大小
        if (img.getSize() > 200 * 1024) {
            this.assembleErrorReturn(result, ResultCode.errorcode501, "图片大于200kb");
            return;
        }

        // 2、校验图片格式--校验后缀
        String imgName = img.getOriginalFilename().toLowerCase();
        if (!(imgName.endsWith(".jpg") || imgName.endsWith(".png") || imgName.endsWith(".jpeg"))) {
            this.assembleErrorReturn(result, ResultCode.errorcode501, "图片格式必须为jpg或png");
        }

        // 2、校验图片格式--校验二进制
//        try {
//            StringBuilder sb = new StringBuilder();
//            byte[] src = img.getBytes();
//            if (src.length < 1) {
//                this.assembleErrorReturn(result, ResultCode.errorcode501, "上传图片为空");
//                return;
//            }
//            for (byte v : src) {
//                String hv = Integer.toHexString(v & 0xFF);
//                if (hv.length() < 2) {
//                    sb.append(0);
//                }
//                sb.append(hv.toUpperCase());
//            }
//            String picStr = sb.toString();
//            if (!(picStr.startsWith("FFD8FF") || picStr.startsWith("89504E47"))) {
//                this.assembleErrorReturn(result, ResultCode.errorcode501, "图片格式必须为jpg或png");
//            }
//        } catch (IOException e) {
//            logger.error("校验图片格式部分出现异常~~");
//            this.assembleErrorReturn(result, ResultCode.errorcode501, "图片格式必须为jpg或png");
//        }
    }
}
