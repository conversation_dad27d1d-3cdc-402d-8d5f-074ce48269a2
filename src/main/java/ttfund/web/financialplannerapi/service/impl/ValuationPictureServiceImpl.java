package ttfund.web.financialplannerapi.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import ttfund.web.financialplannerapi.constant.AppConfigConstant;
import ttfund.web.financialplannerapi.data.WhiteListMysql;
import ttfund.web.financialplannerapi.model.request.ValuationPictureRequest;
import ttfund.web.financialplannerapi.service.LoginService;
import ttfund.web.financialplannerapi.service.ValuationPictureService;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;

@Slf4j
@Service
public class ValuationPictureServiceImpl implements ValuationPictureService {

    @Value("${" + AppConfigConstant.VALUATION_PICTURE_BASEURL + ":http://api.evalue.web:81/charts/%s/%s.png%s}")
    public String base;

    @Value("${" + AppConfigConstant.VALUATION_PICTURE_BASEURL_PIC7 + ":}")
    private String pic7;
    @Value("${" + AppConfigConstant.VALUATION_PICTURE_BASEURL_PIC8 + ":}")
    private String pic8;
    @Value("${" + AppConfigConstant.VALUATION_PICTURE_BASEURL_PIC9 + ":}")
    private String pic9;
    @Value("${" + AppConfigConstant.VALUATION_PICTURE_BASEURL_PIC10 + ":}")
    private String pic10;


    @Autowired
    private WhiteListMysql whiteListMysql;

    @Autowired
    LoginService loginService;

    @Override
    public void getValuationPicture(ValuationPictureRequest request, HttpServletResponse response) {
        Boolean ifLogin = loginService.LoginValidCache(request, null, null);
        Boolean ifIn = whiteListMysql.selectByPassportIdAndSource(request.getPassportid(), request.getSource());
        String picStr = null;
        Boolean ifdefualt = false;

        if (!ifLogin || StringUtils.isEmpty(request.getCode()) || !ifIn) {
            ifdefualt = true;
        }
        switch (request.getType()) {
            case "pic7":
                picStr = pic7;
                break;
            case "pic8":
                picStr = pic8;
                break;
            case "pic9":
                picStr = pic9;
                break;
            case "pic10":
                picStr = pic10;
                break;
            default:
                picStr = pic7;
                break;
        }
        byte[] imageBytes = Base64.getDecoder().decode(picStr);
        if (!ifdefualt) {
            HttpURLConnection urlConnection = null;
            ByteArrayOutputStream outputStream = null;
            try {
                String urlStr = String.format(base, request.getType(), request.getCode(),
                        org.apache.commons.lang3.StringUtils.isEmpty(request.getParam()) ? "" : "?" + URLEncoder.encode(request.getParam(), "UTF-8"));
                URL url = new URL(urlStr);
                urlConnection = (HttpURLConnection) url.openConnection();
                urlConnection.setConnectTimeout(1000);
                urlConnection.setReadTimeout(60000);
                urlConnection.setRequestMethod("GET");
                urlConnection.setDoInput(true);
                urlConnection.setDoOutput(true);
                urlConnection.connect();
                InputStream inputStream = urlConnection.getInputStream();
                outputStream = new ByteArrayOutputStream();
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                imageBytes = outputStream.toByteArray();
            } catch (FileNotFoundException e) {
                log.error("Valuation picture File not found: " + e.getMessage(), e);
            } catch (Exception e) {
                log.error("Error processing valuation picture request: " + e.getMessage(), e);
            } finally {
                if (urlConnection != null) {
                    urlConnection.disconnect();
                }
                if (outputStream!=null) {
                    try {
                        outputStream.close();
                    } catch (IOException e) {
                        log.error("Error close outputStream:" + e.getMessage(), e);
                    }
                }
            }
        }
        response.setContentType("image/png");
        response.setContentLength(imageBytes.length);
        try {
            response.getOutputStream().write(imageBytes);
        } catch (IOException e) {
            log.error("Error processing valuation picture request: " + e.getMessage(), e);
        }
    }
//    留着后续改图片用
//    public static void main(String[] args) {
//        String imagePath = "C:\\Users\\<USER>\\Desktop\\none-pic10.png";
//        String base64Image = encodeImageToBase64(imagePath);
//        System.out.println(base64Image);
//    }
//
//    public static String encodeImageToBase64(String imagePath) {
//        try {
//            Path path = Paths.get(imagePath);
//            byte[] imageBytes = Files.readAllBytes(path);
//            return Base64.getEncoder().encodeToString(imageBytes);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return null;
//        }
//    }
}
