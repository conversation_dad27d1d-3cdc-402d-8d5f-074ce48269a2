package ttfund.web.financialplannerapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import ttfund.web.financialplannerapi.converter.sys.UserInfoConverter;
import ttfund.web.financialplannerapi.mapper.UserInfoMapper;
import ttfund.web.financialplannerapi.model.bo.sys.UserInfoPageReqBO;
import ttfund.web.financialplannerapi.model.bo.sys.UserInfoResBO;
import ttfund.web.financialplannerapi.model.po.sys.UserInfoPO;
import ttfund.web.financialplannerapi.model.reponse.PageCountListResponse;
import ttfund.web.financialplannerapi.mybatis.datasource.Read;
import ttfund.web.financialplannerapi.mybatis.generator.QueryWrapperGenerator;
import ttfund.web.financialplannerapi.service.UserInfoService;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfoPO> implements UserInfoService {

    private final UserInfoMapper mapper;
    private final QueryWrapperGenerator<UserInfoPO> qwg = new QueryWrapperGenerator<>(UserInfoPO.class);


    @Read
    @Override
    public PageCountListResponse<UserInfoResBO> listByPure(UserInfoPageReqBO param) {
        param.init();
        QueryWrapper<UserInfoPO> wrapper = qwg.generate(param);
        Page<UserInfoPO> page = new Page<>(param.getPage(),param.getSize());
        mapper.selectPage(page,wrapper);
        List<UserInfoPO> records = page.getRecords();
        List<UserInfoResBO> list = records.stream().map(UserInfoConverter::toRes).peek(UserInfoResBO::initQwIds).collect(Collectors.toList());
        return PageCountListResponse.of(list, page.getTotal());
    }

    @Read
    @Override
    public UserInfoResBO infoByPassportId(String passportId) {
        UserInfoPO entity = this.lambdaQuery().eq(UserInfoPO::getPassportId, passportId).last("limit 1").one();
        if(Objects.isNull(entity)) {
            return null;
        }

        return UserInfoConverter.toRes(entity);
    }

}
