package ttfund.web.financialplannerapi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrBuilder;
import com.alibaba.fastjson.JSON;
import com.ttfund.web.base.helper.CommonHelper;
import com.ttfund.web.core.annotation.AopCache;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.financialplannerapi.config.App;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class UserRoleMysqlImpl {

    @Autowired
    private App app;

    private static final Logger logger = LoggerFactory.getLogger(UserRoleMysqlImpl.class);

    private static final Map<String,String> INSTITUTION_ID_NAME_MAP = new ConcurrentHashMap<>(20);
    /**
     * 查询角色
     *
     * @param passportId
     */
    @AopCache(fieldorder = {0}, cachetype = 3,cache1expire = 1000 * 60 * 5,cache2expire = 1000 * 60 * 10)
    public Map<String, Object> selectRole(String passportId) {

        Map<String, Object> result = new HashMap();
        //根据通行证查用户权限
        String sql = "SELECT u.passport_id ,r.id role,r.name roleName,u.institution_id, r.* FROM t_role r " +
                "JOIN t_user_role u ON r.id=u.role_id WHERE u.passport_id=? AND r.deleted=0 AND u.deleted=0;";
        List<Object> param = new ArrayList<>();
        param.add(passportId);
        List<Map> maps = app.lcsMysqlRead.executeQuery(sql, param);
        if (!maps.isEmpty()) {
            result = maps.get(0);
        }
        return result;
    }

}
