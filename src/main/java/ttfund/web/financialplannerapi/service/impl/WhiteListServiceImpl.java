package ttfund.web.financialplannerapi.service.impl;

import com.ttfund.web.core.model.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ttfund.web.financialplannerapi.config.AppConfig;
import ttfund.web.financialplannerapi.constant.AppConfigConstant;
import ttfund.web.financialplannerapi.data.WhiteListMysql;
import ttfund.web.financialplannerapi.service.WhiteListService;

@Service
public class WhiteListServiceImpl implements WhiteListService {
    @Autowired
    private WhiteListMysql whiteListMysql;

    @Autowired
    private AppConfig appConfig;

    @Override
    public ApiResponse<Boolean, Object> verifyUserInWhiteList(String passportId, int type) {
        ApiResponse<Boolean, Object> response = new ApiResponse<>();
        response.setData(whiteListMysql.userInWhiteList(passportId, AppConfigConstant.TTFUND_INSTITUTIONID, type) || whiteListMysql.userInWhiteList(passportId, appConfig.TEST_INSTITUTIONID, type));
        return response;
    }
}
