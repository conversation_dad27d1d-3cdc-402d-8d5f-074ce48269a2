package ttfund.web.financialplannerapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ttfund.web.base.Constant;
import com.ttfund.web.base.helper.DateHelper;
import com.ttfund.web.core.annotation.AopCache;
import com.ttfund.web.core.constant.CoreConstant;
import com.ttfund.web.core.constant.ResultCode;
import com.ttfund.web.core.model.ApiResponse;
import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.financialplannerapi.config.App;
import ttfund.web.financialplannerapi.config.AppConfig;
import ttfund.web.financialplannerapi.constant.*;
import ttfund.web.financialplannerapi.converter.contract.ContractRecordConverter;
import ttfund.web.financialplannerapi.data.AppointmentMysqlService;
import ttfund.web.financialplannerapi.data.UserNickName;
import ttfund.web.financialplannerapi.data.impl.*;
import ttfund.web.financialplannerapi.model.AgreementConfig;
import ttfund.web.financialplannerapi.model.bo.EntranceBO;
import ttfund.web.financialplannerapi.model.po.mysqlpo.ContractRecordPO;
import ttfund.web.financialplannerapi.model.po.mysqlpo.UserRolePO;
import ttfund.web.financialplannerapi.model.reponse.*;
import ttfund.web.financialplannerapi.model.request.*;
import ttfund.web.financialplannerapi.register.RequestLogAspectEnhance;
import ttfund.web.financialplannerapi.service.IContractService;
import ttfund.web.financialplannerapi.util.CommonUtil;
import ttfund.web.financialplannerapi.util.RedisLockUtil;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ContractServiceImpl implements IContractService {

    private static final Logger logger = LoggerFactory.getLogger(ContractServiceImpl.class);
    private static final String REMIND_DEVICE_ID = "remind_";

    @Autowired
    private AppConfig config;

    @Autowired
    ContractMysqlImpl contractMysql;

    @Autowired
    FinancialPlannerMysqlImpl financialPlannerMysql;

    @Autowired
    private UserRoleMysql userRoleMysql;

    @Autowired
    private App app;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private AppointmentMysqlService appointmentMysqlService;

    @Autowired
    private RedisLockUtil lockUtil;

    @Autowired
    private PhoneBindApiImpl phoneBindApi;

    @Autowired
    private UserNickName userNickNameApi;

    @Autowired
    private UserInfoAPIService userInfoAPIService;

    @Autowired
    private PassportBindApiImpl passportBindApi;

    @Autowired
    private IMApiService imApiService;

    @Autowired
    private WhiteListMysqlImpl whiteListMysql;

    @Resource
    private TaskChangeStatusApi taskChangeStatusApi;

    @Resource
    private InstitutionConfigApiImpl institutionConfigApi;

    @Resource
    private PriceApiImpl priceApi;

    @Override
    public ApiResponse<ContractStatusResponse, Object> status(String passportId, String institutionId, String financialPlannerId, ApiResponse<ContractStatusResponse, Object> result) {
        ContractStatusResponse response = new ContractStatusResponse();
        if (StringUtils.isNotEmpty(financialPlannerId)) {
            //聊天列表入口过来
            institutionId = userRoleMysql.getInstitutionIdByPassId(financialPlannerId);
            if (StringUtils.isEmpty(institutionId)) {
                setErrorInfo(result, ErrorCodeEnum.NOT_FINANCIAL_PLANNER);
                return result;
            }
        }
        //1.查签约或绑定状态
        List<ContractRecordPO> contractRecordPOList = contractMysql.selectByInsAndPass(passportId, institutionId);
        boolean contract = checkContractStatus(contractRecordPOList);
        boolean bind = checkBindStatus(contractRecordPOList);
        InstitutionConfigInfo configInfo = institutionConfigApi.getInstitutionConfigInfo(institutionId);
        if (contract || bind) {
            //有签约或绑定 拿当前理财师信息
            response.setStatus(contract);
            response.setBind(bind);
            ContractRecordPO recordPO = contractRecordPOList.get(0);
            //用户是否在算法服务白名单中
            response.setWhiteList(isOwnInstitution(institutionId) && whiteListMysql.userInWhiteList(passportId, institutionId, 0));
            //是否需要更换理财师（原理财师身份确认）
            String plannerId = recordPO.getFinancialPlannerId();
            UserRolePO userRolePO = userRoleMysql.selectByInsAndPassId(institutionId, plannerId);
            //需要更换理财师的情况
            boolean needChangePlanner = userRolePO == null;
            response.setChange(needChangePlanner);
            if (!contract) {
                //已绑定未签约 则返回签约协议等配置
                response.setUserInfoNotes(config.userInfoNotes);
                setAgreementInfo(response, configInfo);
                if (needChangePlanner) {
                    //绑定理财师已不存在
                    plannerId = contractMysql.getLeastBindPlannerId(institutionId, passportId);
                    userRolePO = userRoleMysql.selectByInsAndPassIdAndStutus(institutionId, plannerId);
                }
            }
            response.setFinancialPlanner(userRolePO);
            response.setFinancialPlannerId(plannerId);
        } else {
            //未签约或绑定 按分配规则拿理财师信息
            String plannerId;
            //2.先看有无历史签约
            //用户之前有签约则获取该理财师信息
            UserRolePO userRolePO = userRoleMysql.getLastContractPlannerInfo(passportId, institutionId);
            if (userRolePO == null || passportId.equals(userRolePO.getPassportId())) {
                //3.以前未签约过或将签约的理财师已不存在或签约自己，从数据库中找绑定人数最少的其他理财师
                plannerId = contractMysql.getLeastBindPlannerId(institutionId, passportId);
                //没有理财师可用啦
                if (StringUtils.isBlank(plannerId)) {
                    handleNoPlannerError(institutionId, result);
                    return result;
                }
                userRolePO = userRoleMysql.selectByInsAndPassIdAndStutus(institutionId, plannerId);
            }
            response.setFinancialPlannerId(userRolePO.getPassportId());
            response.setFinancialPlanner(userRolePO);
            response.setUserInfoNotes(config.userInfoNotes);
            setAgreementInfo(response,configInfo);
        }
        setShowPageInfo(response, configInfo);
        response.setOwnFlag(appConfig.self_manage_inst_list.contains(institutionId));
        Map<String, String> nickNameMap = userNickNameApi.getNickNameByPids(Collections.singletonList(response.getFinancialPlannerId()));
        if (!CollectionUtils.isEmpty(nickNameMap) && response.getFinancialPlanner() != null){
            response.getFinancialPlanner().setNickName(nickNameMap.get(response.getFinancialPlannerId()));
        }
        result.setData(response);
        return result;
    }

    private void setAgreementInfo(ContractStatusResponse response, InstitutionConfigInfo configInfo) {
        if (configInfo != null) {
            response.setServiceAgreementName("理财师服务协议");
            response.setRiskNotesName("风险揭示书");
            response.setUserInfoName("个人信息");
            response.setInvestmentAgreementName("投顾协议");

            response.setServiceAgreementLook(configInfo.getServiceUrl());
            response.setRiskNotesLook(configInfo.getRiskUrl());
            response.setUserInfoLook(configInfo.getUserUrl());
            if (!"80001124".equals(configInfo.getInstitutionId())){
                response.setInvestmentAgreementLook(configInfo.getTgUrl());
            }
        }
    }

    private void setShowPageInfo(ContractStatusResponse response, InstitutionConfigInfo configInfo) {
        if (configInfo != null) {
            response.setLogoUrl(configInfo.getLogoUrl());
            response.setTitle(configInfo.getTitle());
            response.setInstitutionName(configInfo.getInstitutionName());
            response.setTopSwitch(configInfo.isTopSwitch());
            response.setTopContent(configInfo.getTopContent());
            response.setPopupSwitch(configInfo.isPopupSwitch());
            response.setPopupTitle(configInfo.getPopupTitle());
            response.setPopupContent(configInfo.getPopupContent());
        }
    }

    private void setUserNoticeInfo(ContractStatusResponse response, AgreementConfig base, AgreementConfig agreementConfig) {
        if (base != null) {
            String pdfUrl = base.getPdfUrl();
            response.setUserInfoName(base.getUserInfo().getName());
            if (agreementConfig != null) {
                response.setUserInfoLook(String.format(pdfUrl, agreementConfig.getUserInfo().getValue()));
            }
        }
    }

    private void handleNoPlannerError(String institutionId, ApiResponse<ContractStatusResponse, Object> result) {
        if (isOwnInstitution(institutionId)) {
            setErrorInfo(result, ErrorCodeEnum.LackFinancingManager);
        } else {
            setErrorInfo(result, ErrorCodeEnum.LackPlanner);
        }
    }

    @Override
    public ApiResponse<Boolean, Object> contract(ContractRequest request, ApiResponse<Boolean, Object> result) {
        String financialPlannerId = request.getFinancialPlannerId();
        String institutionId = request.getInstitutionId();
        String passportId = request.getPassportid();
        String lockKey = String.format(RedisConstant.CONTRACT_LOCK, passportId, institutionId);
        //用户加锁防止重复请求
        boolean lock = lockUtil.lock(app.rediswrite, lockKey, 0, 3L);
        if (!lock) {
            setErrorInfo(result, ErrorCodeEnum.LOCKERROR);
            return result;
        }
        try {
            //检查是否跟这个机构已经签约
            List<ContractRecordPO> contractRecordList = contractMysql.selectByInsAndPass(passportId, institutionId);
            boolean status = checkContractStatus(contractRecordList);
            if (status) {
                setErrorInfo(result, ErrorCodeEnum.Contracted);
                result.setData(true);
                return result;
            }
            boolean bind = checkBindStatus(contractRecordList);
            ContractRecordPO bindInfo = null;
            if (bind) {
                bindInfo = contractRecordList.get(0);
            }
            String passportPrefix = passportId.substring(Math.max(passportId.length() - 6, 0));
            String id = System.currentTimeMillis() + "_" + financialPlannerId.substring(Math.max(financialPlannerId.length() - 6, 0)) + "_" + passportPrefix;
            boolean insertSuccess = contractPlanner(request, id, bindInfo, result);
            result.setData(insertSuccess);
            if (result.getErrorCode() != 0) {
                return result;
            }
            //逻辑删除绑定记录失败，回滚签约记录
            if (!insertSuccess) {
                contractMysql.delContractRecord(id);
                logger.error("用户新增签约记录失败，passportId: {}，plannerId: {}", passportId, financialPlannerId);
            } else {
                //签约成功，发送邀请预约卡片信息
                imApiService.sendAppointmentInvitationMsg(request.getFinancialPlannerId(), request.getPassportid());
            }
            //删除原来绑定和签约的理财师缓存
            deleteRedisCache(passportId);
            // 更新任务状态 检查重新签约情况
            ChangeTaskStatusReq req = new ChangeTaskStatusReq();
            BeanUtils.copyProperties(request,req);
            req.setChangeType(TaskChangeTypeEnum.RESUME_CONTRACT.getType());
            req.setNewPlannerId(request.getFinancialPlannerId());
            req.setPassportid(request.getPassportid());
            req.setUserList(Arrays.asList(request.getPassportid()));
            req.setInstitutionId(request.getInstitutionId());
            taskChangeStatusApi.changeTaskStatus(req);
        } finally {
            app.rediswrite.del(lockKey);
        }
        return result;
    }

    /**
     * 签约理财师、处理各状态下的用户备注和标签并删除历史遗留绑定记录
     *
     * @return 签约成功
     */
    private boolean contractPlanner(ContractRequest request, String recordId, ContractRecordPO bindInfo, ApiResponse<Boolean, Object> result) {
        String passportId = request.getPassportid();
        String institutionId = request.getInstitutionId();
        String financialPlannerId = request.getFinancialPlannerId();
        UserRolePO plannerRole;
        if (bindInfo != null && financialPlannerId.equals(bindInfo.getFinancialPlannerId())) {
            // 已绑定用户无需理财师在分配池
            plannerRole = userRoleMysql.selectByInsAndPassId(institutionId, financialPlannerId);
        } else {
            plannerRole = userRoleMysql.selectByInsAndPassIdAndStutus(institutionId, financialPlannerId);
        }
        // 获取历史签约信息
        ContractRecordPO lastContractRecord = contractMysql.getLastContractRecord(passportId, institutionId, null);
        if (plannerRole == null) {
            // 签约理财师已不存在
            if (appConfig.self_manage_inst_list.contains(institutionId)) {
                setErrorInfo(result, ErrorCodeEnum.FinancingManagerChanged);
            } else {
                setErrorInfo(result, ErrorCodeEnum.PlannerChanged);
            }
            return false;
        }
        ContractRecordPO writeModel = new ContractRecordPO();
        writeModel.setContractStatus(ContractStatusEnum.ContractWaiting.getStatus());
        if (lastContractRecord != null) {
            // 有历史签约
            writeModel.setContractStatus(ContractStatusEnum.ReContract.getStatus());
            if (financialPlannerId.equals(lastContractRecord.getFinancialPlannerId())) {
                // 是同一个理财师则把以前的备注和标签拿过来
                writeModel.setMark(lastContractRecord.getMark());
                writeModel.setCommunityAttribute(lastContractRecord.getCommunityAttribute());
            } else {
                ContractRecordPO plannerContractHistory = contractMysql.getLastContractRecord(passportId, institutionId, financialPlannerId);
                //历史签约携带备注及标签
                if (plannerContractHistory != null){
                    writeModel.setMark(plannerContractHistory.getMark());
                    writeModel.setCommunityAttribute(plannerContractHistory.getCommunityAttribute());
                }
            }
        }
        if (bindInfo != null && financialPlannerId.equals(bindInfo.getFinancialPlannerId())) {
            // 有绑定记录则带入绑定备注和标签
            writeModel.setMark(bindInfo.getMark());
            writeModel.setCommunityAttribute(bindInfo.getCommunityAttribute());
        }
        boolean insertSuccess = contractMysql.contractOne(recordId, financialPlannerId, institutionId, passportId, request.getDeviceid(), writeModel);
        if (insertSuccess && bindInfo != null) {
            insertSuccess = contractMysql.delBindRecord(bindInfo.getId());
        }
        return insertSuccess;
    }

    @Override
    public ApiResponse<ChangePlannerResponse, Object> change(ChangePlannerRequest request, ApiResponse<ChangePlannerResponse, Object> result) {
        ChangePlannerResponse response = new ChangePlannerResponse();
        String passportId = request.getPassportid();
        //单用户加锁
        List<ContractRecordPO> contractRecordPOs;
        String key = String.format(RedisConstant.CHNAGE, passportId);
        boolean lock = lockUtil.lock(app.rediswrite, key, 0, 5L);
        if (!lock) {
            setErrorInfo(result, ErrorCodeEnum.LOCKERROR);
            return result;
        }
        String institutionId = request.getInstitutionId();
        ContractRecordPO contractRecordPO = new ContractRecordPO();
        try {
            //1.查原有管户关系
            //2. 如果有预约状态的电话预约就不能更换理财师
            if (!canChangeOrCancel(passportId, institutionId, 0, contractRecordPO, result)) {
                return result;
            }
            Date contractTime = contractRecordPO.getContractTime();
            //3.拟绑定新的管户关系 if 可用往下，不可用则提示（更换理财师不分配之前历史管护理财师，白名单除外）
            UserRolePO userRolePO;
            if (config.whitelist_passportId != null && config.whitelist_passportId.containsKey(passportId)) {
                userRolePO = financialPlannerMysql.selectOne(contractRecordPO.getInstitutionId(), passportId);
            } else {
                userRolePO = financialPlannerMysql.selectOneWithoutHistory(contractRecordPO.getInstitutionId(), passportId);
            }
            if (userRolePO == null) {
                //没有理财师可用拉
                if (isOwnInstitution(institutionId)) {
                    setErrorInfo(result, ErrorCodeEnum.LackFinancingManager);
                } else {
                    setErrorInfo(result, ErrorCodeEnum.LackPlanner);
                }
                return result;
            }
            //4.查用户跟拟定理财师之前有无签约过，有的话把备注、标签拿过来
            ContractRecordPO contractRecordPOld = contractMysql.selectByPassAndFpass(passportId, userRolePO.getPassportId(), institutionId);
            contractRecordPOld = contractRecordPOld == null ? new ContractRecordPO() : contractRecordPOld;
            //签约时间一直带着走
            contractRecordPOld.setContractTime(contractTime);
            //绑定新关系
            long now = System.currentTimeMillis();
            String plannerId = userRolePO.getPassportId();
            String id = now + "_" + plannerId.substring(Math.max(plannerId.length() - 6, 0)) + "_" + passportId.substring(Math.max(passportId.length() - 6, 0));
            boolean changed = contractMysql.changePlanner(contractRecordPO.getId(), id, plannerId, userRolePO.getInstitutionId(), passportId, request.getDeviceid(), contractRecordPOld);
            response.setStatus(changed);
            if (changed) {
                //白名单替换为当前理财师
                whiteListMysql.updateListPlannerId(passportId, plannerId, institutionId);
                RequestLogAspectEnhance.sendKafkaLog.set(true);
                //解绑原来的中间号
                unbindLastRecord(contractRecordPO.getFinancialPlannerId(), passportId);
                response.setFinancialPlannerId(userRolePO.getPassportId());
                result.setData(response);
                //删除原来绑定理财师信息的缓存
                deleteRedisCache(passportId);
                //刷新理财师任务信息
                ChangeTaskStatusReq req = new ChangeTaskStatusReq();
                BeanUtils.copyProperties(request,req);
                req.setChangeType(TaskChangeTypeEnum.CHANGE_PLANNER.getType());
                req.setUserList(Arrays.asList(passportId));
                req.setNewPlannerId(plannerId);
                req.setInstitutionId(userRolePO.getInstitutionId());
                taskChangeStatusApi.changeTaskStatus(req);
            } else {
                result.setData(response);
                result.setErrorCode(ResultCode.errorcode501);
                result.setFirstError("更换理财师失败");
                logger.error("用户更换理财师失败，passportId: {}，plannerId: {}", passportId, plannerId);
            }
        } finally {
            //释放锁
            app.rediswrite.del(key);
        }
        return result;
    }

    @Override
    public ApiResponse<List<UserPlannerResponse>, Object> getPlannerByUser(String passportid, ApiResponse<List<UserPlannerResponse>, Object> result) {
        List<UserPlannerResponse> data = null;
        String redisKey = RedisConstant.CONTRACT_QUERY_PREFIX + passportid;
        String redisValue = app.redisread.get(redisKey);

        if (StringUtils.isNotBlank(redisValue)) {
            //缓存中存在签约信息
            data = JSON.parseArray(redisValue, UserPlannerResponse.class);
            result.setData(data);
            result.setExpansion(data != null);
        } else {
            List<ContractRecordPO> contractRecordPOS = contractMysql.selectByPassportId(passportid);
            if (!CollectionUtils.isEmpty(contractRecordPOS)) {
                Map<String, Integer> priorityMap = appConfig.INSTITUTION_PRIORITY;
                Map<String, String> nickNameMap = userNickNameApi.getNickNameByPids(contractRecordPOS.stream().map(ContractRecordPO::getFinancialPlannerId).collect(Collectors.toList()));
                data = new ArrayList<>();
                for (ContractRecordPO contractRecordPO : contractRecordPOS) {
                    UserPlannerResponse userPlannerResponse = new UserPlannerResponse();
                    String financialPlannerId = contractRecordPO.getFinancialPlannerId();
                    String institutionId = contractRecordPO.getInstitutionId();
                    userPlannerResponse.setInstitutionId(institutionId);
                    userPlannerResponse.setFinancialPlannerId(financialPlannerId);
                    userPlannerResponse.setImgUrl(contractRecordPO.getImgUrl());
                    userPlannerResponse.setInstitutionName(contractRecordPO.getInstitutionName());
                    userPlannerResponse.setFinancialPlannerName(nickNameMap.get(financialPlannerId));
                    if (!CollectionUtils.isEmpty(priorityMap)) {
                        userPlannerResponse.setPriority(priorityMap.getOrDefault(institutionId, 100));
                    }
                    data.add(userPlannerResponse);
                }
                if (!CollectionUtils.isEmpty(data)) {
                    data.sort((o1, o2) -> o1.getPriority() - o2.getPriority());
                    data = data.subList(0, Math.min(AppConfigConstant.MAX_PLANNER, data.size()));
                }
                result.setData(data);
                //过期时间设置为15分钟
                app.rediswrite.set(redisKey, JSON.toJSONString(data), Constant.CACHETIMEMINUTE5 * 3);
                result.setExpansion(true);
            } else {
                result.setExpansion(false);
            }
        }
        return result;
    }

    @Override
    public ApiResponse<List<UserPlannerResponse>, Object> getAllPlannerByUser(String passportId, ApiResponse<List<UserPlannerResponse>, Object> result) {
        List<UserPlannerResponse> data;
        String redisKey = RedisConstant.CONTRACT_QUERYALL_PREFIX + passportId;
        String redisValue = app.redisread.get(redisKey);
        if (StringUtils.isNotBlank(redisValue)) {
            //缓存中存在签约信息
            data = JSON.parseArray(redisValue, UserPlannerResponse.class);
            result.setData(data);
        } else {
            List<ContractRecordPO> allContractRecord = contractMysql.selectAllContractByPassportId(passportId);
            if (allContractRecord != null && !allContractRecord.isEmpty()) {
                data = new ArrayList<>();
                for (ContractRecordPO contractRecord : allContractRecord) {
                    UserPlannerResponse userPlannerResponse = new UserPlannerResponse();
                    userPlannerResponse.setFinancialPlannerId(contractRecord.getFinancialPlannerId());
                    userPlannerResponse.setInstitutionId(contractRecord.getInstitutionId());
                    data.add(userPlannerResponse);
                }
                result.setData(data);
                //过期时间设置为6小时
                app.rediswrite.set(redisKey, JSON.toJSONString(data), Constant.CACHETIMEHOUR1 * 6);
            }
        }
        return result;
    }

    @Override
    public ApiResponse<Boolean, Object> isCancel(QueryContractRequest request) {
        ApiResponse<Boolean, Object> response = new ApiResponse<>();
        if (StringUtils.isEmpty(request.getFinancialPlannerId()) || StringUtils.isEmpty(request.getInstitutionId())) {
            setErrorInfo(response, ErrorCodeEnum.LackParameter);
            return response;
        }
        boolean hasRecord = contractMysql.hasRecordByPassAndFpass(request.getPassportid(), request.getFinancialPlannerId(), request.getInstitutionId());
        response.setData(hasRecord);
        return response;
    }

    @Override
    public ApiResponse<List<LcsWindowResponse>, Object> getLcsConfigWindow(LcsWindowRequest request) {
        if (Lists.newArrayList(0,1,2,3).contains(request.getPageFlag())){
            request.setPageFlag(4);
        }
        ApiResponse<List<LcsWindowResponse>, Object> response = new ApiResponse<>();
        if (CollectionUtils.isEmpty(request.getCode())) {
            return response;
        }
        List<String> codes = new ArrayList<>(request.getCode());
        String pageFlag = String.valueOf(request.getPageFlag());
        // 从缓存中获取结果
        List<LcsWindowResponse> responseList = new ArrayList<>();
        String key = String.format(RedisConstant.LCS_WINDOW_KEY, request.getPassportid(), pageFlag);
        addResponseFromRedisCache(key, codes, responseList);
        response.setData(responseList);
        if (CollectionUtils.isEmpty(codes)) {
            //全部产品入口都在缓存中
            return response;
        }
        if (StringUtils.isEmpty(request.getUserid())) {
            // 获取交易号
            String customerNo = passportBindApi.getPassportBindInfo(request.getPassportid());
            if (customerNo == null) {
                logger.info("passportId {}:未找到交易账号", request.getPassportid());
                setErrorInfo(response, ErrorCodeEnum.NoCustomerNo);
                return response;
            }
            request.setUserid(customerNo);
        }
        // 读取配置缓存
        Map<String, String> configCacheMap = app.redisread.hgetAll(RedisConstant.LCS_INPUT_CONFIG_KEY);
        if (CollectionUtils.isEmpty(configCacheMap)) {
            //未获取到产品入口配置信息
            setErrorInfo(response, ErrorCodeEnum.NOTFOUNDWINDOWCONFIG);
            return response;
        }
        List<EntranceBO> configList = new ArrayList<>();
        Set<String> groupSet = new HashSet<>();
        generateConfigListAndGroupSet(codes, configCacheMap, configList, groupSet,request.getPageFlag());
        if (CollectionUtils.isEmpty(configList)) {
            return response;
        }
        // 接口查询用户所在用户组
        Set<String> userGroup = userInfoAPIService.getUserGroup(request.getUserid(), groupSet);
        // 获取现有理财师机构
        Map<String, String> validInstitutions = getValidInstitutions();
        Map<String, String> hideEntrance = new HashMap<>();
        Set<String> validInstitutionIds = validInstitutions.keySet();
        // 处理向用户展示的入口信息
        List<String> institutionIds = new ArrayList<>();
        for (EntranceBO entrance : configList) {
            if (showPageEntrance(entrance.getUserInfo(), entrance.getText(), userGroup, pageFlag)) {
                String institutionId = entrance.getCompanyId();
                String title = entrance.getTitle();
                String subtitle = entrance.getSubtitle();
                int type = entrance.getType();
                if (type == 1) {
                    institutionId = AppConfigConstant.TTFUND_INSTITUTIONID;
                }
                // 过滤无未开通理财师的机构
                if (!validInstitutionIds.contains(institutionId)) {
                    continue;
                }
                LcsWindowResponse data = new LcsWindowResponse();
                String url = String.format(appConfig.JUMP_LINK, institutionId);
                data.setInstitutionId(institutionId);
                data.setInstitutionName(validInstitutions.get(institutionId));
                data.setUrl(url);
                data.setCode(entrance.getPcode());
                if (StringUtils.isNotBlank(title)) {
                    data.setTitle(title);
                }
                if (StringUtils.isNotBlank(subtitle)) {
                    data.setSubtitle(subtitle);
                }
                institutionIds.add(institutionId);
                responseList.add(data);
            } else {
                hideEntrance.put(entrance.getPcode(), JSON.toJSONString(new EntranceBO()));
            }
        }
        if (!CollectionUtils.isEmpty(institutionIds)) {
            handlePlannerInfo(request.getPassportid(), institutionIds, responseList, validInstitutions);
        }
        response.setData(responseList);
        // 缓存时间设置为15分钟
        Map<String, String> responseMap = responseList.stream().collect(Collectors.toMap(LcsWindowResponse::getCode, JSON::toJSONString, (v1, v2) -> v1));
        responseMap.putAll(hideEntrance);
        if (!CollectionUtils.isEmpty(responseMap)) {
            app.rediswrite.hmset(key, responseMap);
            app.rediswrite.expire(key, Constant.CACHETIMEMINUTE5 * 3);
        }
        return response;
    }

    private void addResponseFromRedisCache(String key, List<String> codes, List<LcsWindowResponse> responseList) {
        Map<String, String> responseCacheMap = app.redisread.hgetAll(key);
        if (CollectionUtils.isEmpty(responseCacheMap)) {
            return;
        }
        Iterator<String> iterator = codes.iterator();
        while (iterator.hasNext()) {
            String code = iterator.next();
            if (responseCacheMap.containsKey(code)) {
                LcsWindowResponse lcsWindowResponse = JSON.parseObject(responseCacheMap.get(code), LcsWindowResponse.class);
                if (StringUtils.isNotBlank(lcsWindowResponse.getCode())) {
                    responseList.add(lcsWindowResponse);
                }
                iterator.remove();
            }
        }
    }

    private Map<String, String> getValidInstitutions() {
        Map<String, String> validInstitutions;
        validInstitutions = app.redisread.hgetAll(RedisConstant.LCS_INSTITUTION_LIST_KEY);
        if (CollectionUtils.isEmpty(validInstitutions)) {
            validInstitutions = userRoleMysql.getAllInstIdWithPlanner();
            app.rediswrite.hmset(RedisConstant.LCS_INSTITUTION_LIST_KEY, validInstitutions);
            app.rediswrite.expire(RedisConstant.LCS_INSTITUTION_LIST_KEY, Constant.CACHETIMEHOUR1);
        }
        return validInstitutions;
    }

    private void generateConfigListAndGroupSet(List<String> codes,
                                               Map<String, String> configCacheMap,
                                               List<EntranceBO> configList,
                                               Set<String> groupSet,
                                               int pageFlag) {
        for (String code : codes) {
            String entranceStr = configCacheMap.get((pageFlag==4 || pageFlag==5) ? code + "_" +pageFlag : code);
            if (StringUtils.isBlank(entranceStr)) {
                continue;
            }
            EntranceBO entrance = JSON.parseObject(entranceStr, EntranceBO.class);
            if (entrance != null) {
                //处理产品对应用户组
                String userGroupStr = entrance.getUserInfo();
                String[] split = userGroupStr.split(",");
                for (int i = 0; i < split.length; i++) {
                    split[i] = split[i].trim();
                }
                groupSet.addAll(Arrays.asList(split));
                configList.add(entrance);
            }
        }
    }

    private boolean showPageEntrance(String productGroupStr, String pageText, Set<String> userGroup, String pageFlag) {
        if (userGroup.isEmpty()) {
            return false;
        }
        boolean result = false;
        //先判断用户是否在产品对应用户组
        String[] productGroups = productGroupStr.split(",");
        for (String productGroup : productGroups) {
            if (!StringUtils.isBlank(productGroup) && userGroup.contains(productGroup)) {
                result = true;
                break;
            }
        }
        //再判断是否是展示页面
        if (result) {
            String[] productPageFlags = pageText.split(",");
            for (String productPageFlag : productPageFlags) {
                if (!StringUtils.isBlank(productPageFlag) && productPageFlag.equals(pageFlag)) {
                    return true;
                }
            }
        }
        return false;
    }

    private void handlePlannerInfo(String passportId, List<String> institutionIds, List<LcsWindowResponse> responseList, Map<String, String> validInstitutions) {
        //查询签约关系,获取全部理财师信息和昵称
        List<ContractRecordPO> plannerInfoList = contractMysql.getContractLcsInfoByPassAndIns(passportId, institutionIds);
        if (CollectionUtils.isEmpty(plannerInfoList)) {
            return;
        }
        Map<String, String> nickNameMap = userNickNameApi.getNickNameByPids(plannerInfoList.stream().map(ContractRecordPO::getFinancialPlannerId).collect(Collectors.toList()));
        Map<String, ContractRecordPO> plannerInfoMap = plannerInfoList.stream().collect(Collectors.toMap(ContractRecordPO::getInstitutionId, v -> v, (v1, v2) -> v1));
        for (LcsWindowResponse lcsWindowResponse : responseList) {
            ContractRecordPO plannerInfo = plannerInfoMap.get(lcsWindowResponse.getInstitutionId());
            if (plannerInfo != null) {
                String imgUrl = appConfig.LCS_DEFAULT_IMG + plannerInfo.getFinancialPlannerId() + "/120";
                lcsWindowResponse.setDefaultImg(imgUrl);
                lcsWindowResponse.setNickName(nickNameMap.get(plannerInfo.getFinancialPlannerId()));
            }
        }
    }

    /**
     * 检查是否签约
     *
     * @param contractRecordPOList
     * @return
     */
    private Boolean checkContractStatus(List<ContractRecordPO> contractRecordPOList) {
        boolean result = false;
        if (contractRecordPOList != null && !contractRecordPOList.isEmpty()) {
            ContractRecordPO contractRecordPO = contractRecordPOList.get(0);
            result = (contractRecordPO.getContractStatus() == ContractStatusEnum.NewContractOrChangePlanner.getStatus() || contractRecordPO.getContractStatus() == ContractStatusEnum.ReContract.getStatus())
                    && contractRecordPO.getDeleted() == 0;
        }
        return result;
    }

    /**
     * 检查是否绑定
     *
     * @param contractRecordPOList
     * @return
     */
    private Boolean checkBindStatus(List<ContractRecordPO> contractRecordPOList) {
        boolean result = false;
        if (contractRecordPOList != null && !contractRecordPOList.isEmpty()) {
            ContractRecordPO contractRecordPO = contractRecordPOList.get(0);
            result = (contractRecordPO.getContractStatus() == ContractStatusEnum.Binding.getStatus() || contractRecordPO.getContractStatus() == ContractStatusEnum.ContractWaiting.getStatus())
                    && contractRecordPO.getDeleted() == 0;
        }
        return result;
    }

    /**
     * 检查当前用户是否符合更换理财师或解约条件
     *
     * @param passportId     用户通行证Id
     * @param institutionId  当前机构Id
     * @param handleType     操作类型：0更换理财师，1解约
     * @param contractRecord 签约信息实体
     * @param apiResult      请求返回结果
     * @param <T>
     * @return
     */
    private <T> Boolean canChangeOrCancel(String passportId, String institutionId, int handleType, ContractRecordPO contractRecord, ApiResponse<T, Object> apiResult) {
        //1.查原有管户关系
        List<ContractRecordPO> contractRecordPOs = contractMysql.selectByInsAndPass(passportId, institutionId);
        if (contractRecordPOs == null || contractRecordPOs.isEmpty() ||
                (contractRecordPOs.get(0).getContractStatus() != ContractStatusEnum.NewContractOrChangePlanner.getStatus() &&
                        contractRecordPOs.get(0).getContractStatus() != ContractStatusEnum.ReContract.getStatus())) {
            //无签约记录或非签约状态无法解约或更换理财施
            setErrorInfo(apiResult, ErrorCodeEnum.NOTContract);
            return false;
        }
        BeanUtils.copyProperties(contractRecordPOs.get(0), contractRecord);
        //2. 如果有预约状态的电话预约就不能更换理财师
        Boolean hasRepeatAppointment = appointmentMysqlService.hasRepeatAppointment(passportId, institutionId,
                DateHelper.dateToStr(LocalDateTime.now(), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));
        if (hasRepeatAppointment) {
            if (handleType == 0) {
                if (isOwnInstitution(institutionId)) {
                    setErrorInfo(apiResult, ErrorCodeEnum.FinishCallFirst1);
                } else {
                    setErrorInfo(apiResult, ErrorCodeEnum.FinishCallFirst);
                }
            } else {
                setErrorInfo(apiResult, ErrorCodeEnum.FINISH_CALL_BEFORE_CANCEL);
            }
            return false;
        }
        return true;
    }

    @Override
    public ApiResponse<Boolean, Object> cancelContract(CancelContractRequest request, ApiResponse<Boolean, Object> result) {
        ContractRecordPO contractRecordPO = new ContractRecordPO();
        //1.查原有管户关系
        //2. 如果有预约状态的电话预约就不能解约
        if (!canChangeOrCancel(request.getPassportid(), request.getInstitutionId(), 1, contractRecordPO, result)) {
            return result;
        }
        contractRecordPO.setDeviceId(request.getDeviceid());
        //3. 删除管户关系，插入一条delete=1, contractStatus=1的记录留痕，并清理白名单
        boolean cancelSuccess = contractMysql.cancelContract(contractRecordPO.getId(), contractRecordPO, request.getPassportid(), contractRecordPO.getFinancialPlannerId(), request.getInstitutionId());
        if (cancelSuccess) {
            RequestLogAspectEnhance.sendKafkaLog.set(true);
            //4. 解绑原来的中间号
            unbindLastRecord(contractRecordPO.getFinancialPlannerId(), contractRecordPO.getPassportId());
            //5. 删除原来绑定理财师缓存
            deleteRedisCache(request.getPassportid());
        }
        ChangeTaskStatusReq req = new ChangeTaskStatusReq();
        BeanUtils.copyProperties(request,req);
        req.setNewPlannerId(contractRecordPO.getFinancialPlannerId());
        req.setChangeType(TaskChangeTypeEnum.BREAK_CONTRACT.getType());
        req.setUserList(Arrays.asList(request.getPassportid()));
        req.setInstitutionId(request.getInstitutionId());
        taskChangeStatusApi.changeTaskStatus(req);
        result.setData(cancelSuccess);
        return result;
    }

    private void unbindLastRecord(String financialPlannerId, String userPassportId) {
        String queryLastBindSql = "SELECT bind_id, create_time FROM t_bind_record WHERE financial_planner_id = ? and passport_id = ? ORDER BY create_time DESC LIMIT 1";
        List<Object> selectParams = new ArrayList<>();
        selectParams.add(financialPlannerId);
        selectParams.add(userPassportId);
        List<Map> maps = app.lcsMysqlRead.executeQuery(queryLastBindSql, selectParams);
        if (!maps.isEmpty()) {
            Date createTime = (Date) maps.get(0).get("create_time");
            if ((System.currentTimeMillis() - createTime.getTime()) / 1000 < appConfig.BIND_TIME_LENGTH) {
                String bindId = (String) maps.get(0).get("bind_id");
                phoneBindApi.unbind(bindId);
            }
        }
    }

    @Override
    public ApiResponse<BindStatusResponse, Object> bindPlanner(AuthBaseRequest request, ApiResponse<BindStatusResponse, Object> result) {
        String passportId = request.getPassportid();
        String institutionId = AppConfigConstant.TTFUND_INSTITUTIONID;
        if (config.whitelist_passportId != null && config.whitelist_passportId.containsKey(passportId)) {
            //测试账户走测试机构
            institutionId = appConfig.TEST_INSTITUTIONID;
        }
        ContractRecordPO bindRecord = contractMysql.getBindOrContractRecord(passportId, institutionId);
        BindStatusResponse bindStatus = new BindStatusResponse();
        result.setData(bindStatus);
        if (bindRecord != null) {
            bindStatus.setFinancialPlannerId(bindRecord.getFinancialPlannerId());
            return result;
        }
        //需要进行绑定
        String plannerId = "";
        ContractRecordPO lastContractRecord = contractMysql.getLastContractRecord(passportId, institutionId, null);
        //用户之前有签约则获取该理财师信息
        UserRolePO userRolePO = lastContractRecord == null ? null : userRoleMysql.selectByInsAndPassId(institutionId, lastContractRecord.getFinancialPlannerId());
        String attribute = null;
        String mark = null;
        if (userRolePO != null) {
            plannerId = userRolePO.getPassportId();
            attribute = lastContractRecord.getCommunityAttribute();
            mark = lastContractRecord.getMark();
        } else {
            //从数据库中找绑定人数最少的理财经理
            plannerId = contractMysql.getLeastBindPlannerId(institutionId, passportId);
        }
        if (StringUtils.isBlank(plannerId)) {
            //无可用理财经理
            setErrorInfo(result, ErrorCodeEnum.LackFinancingManager);
        } else if (contractMysql.addBindRecord(passportId, plannerId, institutionId, request.getDeviceid(), attribute, mark)) {
            //插入一条绑定记录
            bindStatus.setFinancialPlannerId(plannerId);
            //删除缓存
            String userAllPlannerCacheKey = RedisConstant.CONTRACT_QUERYALL_PREFIX + request.getPassportid();
            app.rediswrite.del(userAllPlannerCacheKey);
        } else {
            //插入记录失败
            setErrorInfo(result, ErrorCodeEnum.PlannerBindError);
        }
        return result;
    }

    @Override
    public ApiResponse<UserTTPlannerResponse, Object> getTTPlannerByUser(AuthBaseRequest request) {
        UserTTPlannerResponse data = null;
        String passportId = request.getPassportid();
        ContractRecordPO contractRecordPO = contractMysql.getBindOrContractRecord(passportId, AppConfigConstant.TTFUND_INSTITUTIONID);
        if (contractRecordPO != null) {
            String nickName = userNickNameApi.getNickNameByPid(contractRecordPO.getFinancialPlannerId());
            if (StringUtils.isEmpty(nickName)) {
                logger.error("获取理财师昵称失败,passportId:{},financialPlannerId:{}", passportId, contractRecordPO.getFinancialPlannerId());
                return ApiResponse.succeed(data);
            }
            data = new UserTTPlannerResponse();
            data.setImgUrl(contractRecordPO.getImgUrl());
            data.setFinancialPlannerName(nickName);
            data.setInstitutionName(contractRecordPO.getInstitutionName());
            data.setInstitutionId(contractRecordPO.getInstitutionId());
            data.setFinancialPlannerId(contractRecordPO.getFinancialPlannerId());
        }
        return ApiResponse.succeed(data);
    }

    @Override
    public ApiResponse<List<TTFundUserContractResponse>, Object> batchTTFundUserContract(TTFundUserContractRequest request) {
        ApiResponse<List<TTFundUserContractResponse>, Object> result = new ApiResponse<>();
        if (CollectionUtils.isEmpty(request.getUserPassportSet()) && CollectionUtils.isEmpty(request.getUserNeedContractPassportSet())) {
            result.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
            result.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
            return result;
        }
        List<TTFundUserContractResponse> dataResult = new ArrayList<>();
        result.setData(dataResult);
        Set<String> userPassportSet = Optional.ofNullable(request.getUserPassportSet()).orElse(new HashSet<>());
        Set<String> userNeedContractPassportSet =  Optional.ofNullable(request.getUserNeedContractPassportSet()).orElse(new HashSet<>());
        List<String> allUserList = new ArrayList<>(userPassportSet);
        allUserList.addAll(userNeedContractPassportSet);
        List<ContractRecordPO> userContractState =
                contractMysql.getUserContractState(allUserList, AppConfigConstant.TTFUND_INSTITUTIONID);
        Map<String, ContractRecordPO> userContractStateMap = userContractState.stream().collect(Collectors
                .toMap(ContractRecordPO::getPassportId,v -> v, (v1, v2) -> v1));

        Map<String,String> oaIdMap = userRoleMysql.getAllTTFundPlannerWithOa();
        Map<String,String> oaIdValidMap = userRoleMysql.getValidTTFundPlannerWithOa();
        for (String userId : userNeedContractPassportSet) {
            if (StringUtils.isEmpty(userId)) {
                continue;
            }

            ContractRecordPO recordPO = userContractStateMap.get(userId);
            //从未签约或绑定
            if (recordPO == null) {
                ApiResponse<Boolean, Object> contractResponse = new ApiResponse<>();
                ContractRequest contractRequest = new ContractRequest();
                contractRequest.setInstitutionId(AppConfigConstant.TTFUND_INSTITUTIONID);
                //随机选择理财师
                contractRequest.setFinancialPlannerId(CommonUtil.getRandomMapKey(oaIdValidMap));
                contractRequest.setDeviceid(REMIND_DEVICE_ID + MDC.get(CoreConstant.logtraceid));
                contractRequest.setPassportid(userId);
                ApiResponse<Boolean, Object> contractResult = contract(contractRequest, contractResponse);
                if (Boolean.TRUE.equals(contractResult.getData())){
                    addTTFundUserContractResponse(userId,contractRequest.getFinancialPlannerId(),oaIdMap,dataResult);
                }
                continue;
            }
            // 已签约或已绑定
            if (recordPO.getDeleted() == 0){
                addTTFundUserContractResponse(userId,recordPO.getFinancialPlannerId(),oaIdMap,dataResult);
                continue;
            }
            // 已经解约
            if (recordPO.getDeleted() == 1){
                //校验旧理财师是否符合分配条件
                String plannerId = oaIdValidMap.containsKey(recordPO.getFinancialPlannerId()) ? recordPO.getFinancialPlannerId() : CommonUtil.getRandomMapKey(oaIdValidMap);

                ApiResponse<Boolean, Object> contractResponse = new ApiResponse<>();
                ContractRequest contractRequest = new ContractRequest();
                contractRequest.setInstitutionId(AppConfigConstant.TTFUND_INSTITUTIONID);
                contractRequest.setFinancialPlannerId(plannerId);
                contractRequest.setPassportid(userId);
                contractRequest.setDeviceid(REMIND_DEVICE_ID + MDC.get(CoreConstant.logtraceid));
                ApiResponse<Boolean, Object> contractResult = contract(contractRequest, contractResponse);
                if (Boolean.TRUE.equals(contractResult.getData())){
                    addTTFundUserContractResponse(userId,plannerId,oaIdMap,dataResult);
                }
            }
        }
        for (String userId : userPassportSet) {
            ContractRecordPO recordPO = userContractStateMap.get(userId);
            if (StringUtils.isEmpty(userId)){
                continue;
            }
            // 已签约或已绑定
            if (recordPO != null && recordPO.getDeleted() == 0){
                addTTFundUserContractResponse(userId,recordPO.getFinancialPlannerId(),oaIdMap,dataResult);
            }
        }
        return result;
    }

    @Override
    @AopCache(cachetype = 3,cache1expire = 2 * 60 * 1000,cache2expire = 5 * 60 * 1000,fieldlist={"institutionId","passportid"})
    public ApiResponse<UserPlannerInfoResponse, Object> contractInstWithCache(QueryContractRequest request) {
        ApiResponse<UserPlannerInfoResponse, Object> response = new ApiResponse<>();
        if (StringUtils.isEmpty(request.getInstitutionId())) {
            setErrorInfo(response, ErrorCodeEnum.LackParameter);
            return response;
        }
        Map<String, String> validInstitutions = getValidInstitutions();
        String instName = validInstitutions.get(request.getInstitutionId());
        if (StringUtils.isEmpty(instName)) {
            return response;
        }
        UserPlannerInfoResponse data = new UserPlannerInfoResponse();
        data.setInstitutionId(request.getInstitutionId());
        data.setInstitutionName(instName);

        ContractRecordPO contractRecord = contractMysql.checkContractByInst(request.getPassportid(), request.getInstitutionId());
        if (contractRecord != null && StringUtils.isNotEmpty(contractRecord.getFinancialPlannerId())){
            String plannerId = contractRecord.getFinancialPlannerId();
            String nickName = userNickNameApi.getNickNameByPid(plannerId);
            if (StringUtils.isEmpty(nickName)) {
                logger.error("获取理财师昵称失败,passportId:{},financialPlannerId:{}", request.getPassportid(),plannerId);
                return ApiResponse.succeed(null);
            }
            data.setFinancialPlannerId(plannerId);
            data.setFinancialPlannerNickName(nickName);
            data.setImgUrl( appConfig.LCS_DEFAULT_IMG + plannerId + "/120");
        }
        response.setData(data);
        return response;
    }

    @Override
    public ApiResponse<List<LcsWindowResponse>, Object> getLcsWindow(LcsWindowRequest request) {
        ApiResponse<List<LcsWindowResponse>, Object> lcsConfigWindowResult = Optional.ofNullable(getLcsConfigWindow(request)).orElse(ApiResponse.succeed());

        if (Boolean.TRUE.toString().equals(request.getForceGetLcsInfo())){
            Set<String> alreadyDoneCode = Optional.ofNullable(lcsConfigWindowResult.getData())
                    .map(list ->
                            list.stream()
                                    .map(LcsWindowResponse::getCode)
                                    .collect(Collectors.toSet()))
                    .orElse(new HashSet<>());
            //筛选待额外处理的code
            Set<String> tempCodeSet = new HashSet<>(request.getCode());
            tempCodeSet.removeAll(alreadyDoneCode);
            if (CollectionUtils.isEmpty(tempCodeSet)){
                return lcsConfigWindowResult;
            }
            Map<String, String> optimalGDLCMap = priceApi.checkIfGDLCOptimal(tempCodeSet);
            List<LcsWindowResponse> resultData = Optional.ofNullable(lcsConfigWindowResult.getData()).orElse(new ArrayList<>());
            // 判断是否为优选产品
            for (String code : tempCodeSet) {
                if (!"1".equals(optimalGDLCMap.get(code))){
                    continue;
                }
                ApiResponse<UserTTPlannerResponse, Object> plannerRes = getTTPlannerByUser(request);
                UserTTPlannerResponse data = plannerRes.getData();
                if (data == null){
                    LcsWindowResponse lcsWindowResponse = new LcsWindowResponse();
                    lcsWindowResponse.setInstitutionId("100031");
                    lcsWindowResponse.setInstitutionName("天天基金");
                    lcsWindowResponse.setUrl(String.format(appConfig.WINDOW_JUMP_LINK, lcsWindowResponse.getInstitutionId(),"",getAutoReply(code)));
                    lcsWindowResponse.setCode(code);
                    resultData.add(lcsWindowResponse);
                } else {
                    LcsWindowResponse lcsWindowResponse = ContractRecordConverter.ttFundPlannerResponse2LcsWindow(data);
                    lcsWindowResponse.setDefaultImg(appConfig.LCS_DEFAULT_IMG + data.getFinancialPlannerId() + "/120");
                    lcsWindowResponse.setUrl(String.format(appConfig.WINDOW_JUMP_LINK, data.getInstitutionId(),data.getFinancialPlannerId(),getAutoReply(code)));
                    lcsWindowResponse.setCode(code);
                    resultData.add(lcsWindowResponse);
                }
            }
            return ApiResponse.succeed(resultData);
        }
        return lcsConfigWindowResult;
    }

    private static String getAutoReply(String code) {
        HashMap<Object, Object> map = new HashMap<>();
        map.put("type","preferred_product");
        map.put("fundCode",code);
        try {
            return URLEncoder.encode(JSON.toJSONString(map), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            logger.error("url编码失败");
        }
        return "";
    }



    private void addTTFundUserContractResponse(String userId,String plannerId,Map<String,String> oaIdMap, List<TTFundUserContractResponse> dataResult){
        // 获取所有自营理财师的oa号
        if (!oaIdMap.containsKey(plannerId)){
            logger.warn("理财师未添加对应的oa号{}",plannerId);
        }
        dataResult.add(new TTFundUserContractResponse(userId,plannerId,oaIdMap.get(plannerId)));
    }


    private void deleteRedisCache(String passportId) {
        String userPlannerCacheKey = RedisConstant.CONTRACT_QUERY_PREFIX + passportId;
        String userAllPlannerCacheKey = RedisConstant.CONTRACT_QUERYALL_PREFIX + passportId;
        String windowKey1 = String.format(RedisConstant.LCS_WINDOW_KEY, passportId, 4);
        String windowKey2 = String.format(RedisConstant.LCS_WINDOW_KEY, passportId, 5);
        app.rediswrite.del(userPlannerCacheKey);
        app.rediswrite.del(userAllPlannerCacheKey);
        app.rediswrite.del(windowKey1);
        app.rediswrite.del(windowKey2);
    }

    private void setErrorInfo(ApiResponse<?, ?> response, ErrorCodeEnum errorType) {
        response.setErrorCode(errorType.getCode());
        response.setFirstError(errorType.getMsg());
    }

    private boolean isOwnInstitution(String institutionId) {
        return AppConfigConstant.TTFUND_INSTITUTIONID.equals(institutionId) || appConfig.TEST_INSTITUTIONID.equals(institutionId);
    }

}
