package ttfund.web.financialplannerapi.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import ttfund.web.financialplannerapi.constant.ErrorCodeEnum;
import ttfund.web.financialplannerapi.data.impl.BlackListMysqlImpl;
import ttfund.web.financialplannerapi.exception.BusinessException;
import ttfund.web.financialplannerapi.model.po.mysqlpo.ContractRecordPO;
import ttfund.web.financialplannerapi.model.reponse.AddBlackListResponse;
import ttfund.web.financialplannerapi.model.reponse.PlannerBlackListResponse;
import ttfund.web.financialplannerapi.model.reponse.RemoveBlackListResponse;
import ttfund.web.financialplannerapi.model.request.AddBlackListRequest;
import ttfund.web.financialplannerapi.model.request.PlannerBlackListRequest;
import ttfund.web.financialplannerapi.model.request.RemoveBlackListRequest;
import ttfund.web.financialplannerapi.service.IBlackListService;

import javax.annotation.Resource;
import java.util.*;


@Service
public class BlackListServiceImpl implements IBlackListService {


    @Resource
    BlackListMysqlImpl blackListMysqlImpl;


    @Override
    public AddBlackListResponse addToBlackList(AddBlackListRequest request) throws BusinessException {
        AddBlackListResponse response = new AddBlackListResponse();
        if (request==null ||StringUtils.isEmpty(request.getPlannerid())
                ||StringUtils.isEmpty(request.getPassportid())){
            throw new BusinessException(ErrorCodeEnum.LackParameter.getMsg(),ErrorCodeEnum.LackParameter.getCode());
        }
        String plannerid = request.getPlannerid();
        String userPassport = request.getPassportid();
        List<ContractRecordPO> contractRecords = blackListMysqlImpl.getContractRecord(plannerid, userPassport,0);
        if (contractRecords.size()>0){
//            //获取机构下所有没被拉黑的理财师
//            List<String> unBlackPlanners = blackListMysqlImpl.getUnBlackPlanners(userPassport, institutionid);
//            if (unBlackPlanners.isEmpty()){
//                throw new BusinessException(ErrorCodeEnum.LackPlanner.getMsg(),ErrorCodeEnum.LackPlanner.getCode());
//            }
//            //随机选择一位
//            String newPlannerId  = unBlackPlanners.get(new Random().nextInt(unBlackPlanners.size()));
//            for (ContractRecordPO contractRecord : contractRecords) {
//                ContractRecordPO copyRecord = JSON.parseObject(JSON.toJSONString(contractRecord), ContractRecordPO.class);
//                copyRecord.setId(UUID.randomUUID().toString().replace("-", ""));
//                copyRecord.setFinancialPlannerId(newPlannerId);
//                combine.add(copyRecord);
////            contractRecord.setContractTime(DateHelper.getNowDate());
//            }
            response.setAddsuccess(blackListMysqlImpl.updateContractRecordStatus(contractRecords,true));
//            response.setNewplannerid(newPlannerId);
        }
        return response;
    }

    @Override
    public RemoveBlackListResponse removeBlackList(RemoveBlackListRequest request) throws BusinessException {
        RemoveBlackListResponse response = new RemoveBlackListResponse();
        if (request==null||StringUtils.isEmpty(request.getPassportid())||StringUtils.isEmpty(request.getPlannerid())){
            throw new BusinessException(ErrorCodeEnum.LackParameter.getMsg(),ErrorCodeEnum.LackParameter.getCode());
        }
        String plannerid = request.getPlannerid();
        String userPassport = request.getPassportid();
        List<ContractRecordPO> contractRecords = blackListMysqlImpl.getContractRecord(plannerid, userPassport,2);
        if (contractRecords.size()>0) {
            response.setRemoveSuccess(blackListMysqlImpl.updateContractRecordStatus(contractRecords,false));
        }
        return response;
    }

    @Override
    public PlannerBlackListResponse getBlackList(PlannerBlackListRequest request) throws BusinessException {
        PlannerBlackListResponse response = new PlannerBlackListResponse();
        if (request==null || StringUtils.isEmpty(request.getPassportid())){
            throw new BusinessException(ErrorCodeEnum.LackParameter.getMsg(),ErrorCodeEnum.LackParameter.getCode());
        }
        String userPassport = request.getPassportid();
        response.setBlackPlannerList(blackListMysqlImpl.getAllBlackPlannerId(userPassport));
        return response;
    }


}
