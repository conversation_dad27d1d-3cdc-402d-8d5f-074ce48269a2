package ttfund.web.financialplannerapi.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import ttfund.web.financialplannerapi.data.impl.ContractMysqlImpl;
import ttfund.web.financialplannerapi.service.UserAuthValidService;
import ttfund.web.financialplannerapi.util.other.StringUtils;
import ttfund.web.financialplannerapi.util.security.SecurityUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAuthValidServiceImpl implements UserAuthValidService {

    private final ContractMysqlImpl contractMysql;

    @Override
    public String getUserPassportIdByAuth(String userPassportId) {
        // 如果是用户，则直接取用户自己的数据
        if (SecurityUtils.isCustomer()) {
            return SecurityUtils.getPassportId();
        }

        // 如果用户通行证id为空，默认取当前用户的数据
        if (StringUtils.isEmpty(userPassportId)) {
            return SecurityUtils.getPassportId();
        }

        // 非普通用户查询自身，直接返回
        if (userPassportId.equals(SecurityUtils.getPassportId())) {
            return userPassportId;
        }

        // 如果为总监权限账号，则校验用户是否和机构签约
        if (SecurityUtils.isPlannerAdmin()) {
            contractMysql.validContract(userPassportId, SecurityUtils.getInstitutionId(), "");
            return userPassportId;
        }

        // 如果为理财师权限账号，则校验用户是否和理财师签约
        if (SecurityUtils.isPlanner()) {
            contractMysql.validContract(userPassportId, SecurityUtils.getInstitutionId(), SecurityUtils.getPassportId());
            return userPassportId;
        }
        return userPassportId;
    }
}
