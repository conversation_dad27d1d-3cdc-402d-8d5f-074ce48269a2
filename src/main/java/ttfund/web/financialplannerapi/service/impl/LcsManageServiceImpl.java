package ttfund.web.financialplannerapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.ttfund.web.core.model.ApiResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.financialplannerapi.config.App;
import ttfund.web.financialplannerapi.model.reponse.LcsManageInfoResponse;
import ttfund.web.financialplannerapi.service.ILcsManageService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @ClassName LcsManageServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/25 8:39
 * @Vserion 1.0.0
 */
@Service
public class LcsManageServiceImpl implements ILcsManageService {

    @Resource
    private App app;

    @Override
    public ApiResponse<List<LcsManageInfoResponse>, Object> queryAll() {
        ApiResponse<List<LcsManageInfoResponse>, Object> result = new ApiResponse<>();
        List<LcsManageInfoResponse> list = null;

        // 查询
        String sql = "select passport_id, institution_id from t_user_role where deleted = 0 and role_id = 4";
        List<Map> maps = app.lcsMysqlRead.executeQuery(sql, null);
        if (!CollectionUtils.isEmpty(maps)) {
            list = JSON.parseArray(JSON.toJSONString(maps), LcsManageInfoResponse.class);
        }

        // 封装参数
        result.setData(list);
        return result;
    }
}
