package ttfund.web.financialplannerapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.ttfund.web.base.Constant;
import com.ttfund.web.base.helper.DateHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.ttfund.web.core.constant.ResultCode;
import com.ttfund.web.core.model.ApiResponse;
import com.ttfund.web.core.utils.AesUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.financialplannerapi.config.App;
import ttfund.web.financialplannerapi.config.AppConfig;
import ttfund.web.financialplannerapi.constant.*;
import ttfund.web.financialplannerapi.data.AppointmentMysqlService;
import ttfund.web.financialplannerapi.data.impl.*;
import ttfund.web.financialplannerapi.model.api.PriceApiResponse;
import ttfund.web.financialplannerapi.model.bo.UserInfoBO;
import ttfund.web.financialplannerapi.model.po.mysqlpo.UserRolePO;
import ttfund.web.financialplannerapi.model.reponse.*;
import ttfund.web.financialplannerapi.model.request.AddAppointmentRequest;
import ttfund.web.financialplannerapi.model.request.AppointmentQueryRequest;
import ttfund.web.financialplannerapi.service.AppointmentService;
import ttfund.web.financialplannerapi.util.CommonUtil;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/22
 * @Description
 */
@Service
public class AppointmentServiceImpl implements AppointmentService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AppointmentServiceImpl.class);

    @Value("${" + AppConfigConstant.LCS_AUTH_KEY + "}")
    private String lcsAuthKey;

    @Value("${" + AppConfigConstant.PHONE_PREFIX + "}")
    private String phonePrefix;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private AppointmentMysqlService appointmentMysqlService;

    @Autowired
    private PassportBindApiImpl passportBindApi;

    @Autowired
    private App app;

    @Autowired
    private UserRoleMysql userRoleMysql;

    @Autowired
    private ContractMysqlImpl contractMysql;

    @Autowired
    private UserInfoAPIService userInfoAPIService;

    @Autowired
    private LcsMysqlServiceImpl lcsMysqlService;

    @Autowired
    private PriceApiImpl priceApi;

    @Autowired
    private IMApiService imApiService;

    @Resource
    private InstitutionConfigApiImpl institutionConfigApi;

    @Override
    public ApiResponse<AppointmentInfoResponse, Object> queryAppointmentInfo(AppointmentQueryRequest params) {
        ApiResponse<AppointmentInfoResponse, Object> res = new ApiResponse<>();
        AppointmentInfoResponse result = new AppointmentInfoResponse();

        if (StringUtils.isEmpty(params.getFinancialPlannerId()) || StringUtils.isEmpty(params.getInstitutionId())) {
            res.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
            res.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
            return res;
        }
        // 用户通行证
        String passportId = params.getPassportid();
        String financialPlannerId = params.getFinancialPlannerId();
        // 当前时间
        String now = DateHelper.dateToStr(LocalDateTime.now(), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
        // 查询预约记录
        AppointmentInfo appointment = appointmentMysqlService.queryAppointmentByParams(passportId, financialPlannerId, params.getInstitutionId(), now);
        // 获取交易号
        String customerNo = passportBindApi.getPassportBindInfo(passportId);
        if (customerNo == null) {
            LOGGER.info("{}无交易账号", passportId);
            res.setErrorCode(ErrorCodeEnum.NoCustomerNo.getCode());
            res.setFirstError(ErrorCodeEnum.NoCustomerNo.getMsg());
            return res;
        }
        // 根据交易账号查询用户信息
        UserBaseInfoResponse userBaseInfoResponse = userInfoAPIService.getUserBaseInfo(customerNo);
        // 用户信息查询错误
        if (userBaseInfoResponse == null || !userBaseInfoResponse.getSucceed() || userBaseInfoResponse.getResult() == null || StringUtils.isEmpty(userBaseInfoResponse.getResult().getCustomerName())) {
            res.setErrorCode(ErrorCodeEnum.UserBaseInfoQueryERROR.getCode());
            res.setFirstError(ErrorCodeEnum.UserBaseInfoQueryERROR.getMsg());
            return res;
        }
        // 用户信息设置脱敏
        String customerName = userBaseInfoResponse.getResult().getCustomerName();
        String newCustomerName = "*" + customerName.substring(1);
        userBaseInfoResponse.getResult().setCustomerName(newCustomerName);

        // 查理财师信息
        LcsPersonalInfoResponse lcsInfo = lcsMysqlService.querySingle(params.getFinancialPlannerId(), "query");
        // 理财师判空
        if (null == lcsInfo) {
            res.setErrorCode(ErrorCodeEnum.PlannerChanged.getCode());
            res.setFirstError(ErrorCodeEnum.PlannerChanged.getMsg());
            return res;
        }
        // 判断是否加V
        Integer isV = getVByPassportId(params.getFinancialPlannerId(), res);
        lcsInfo.setV(isV != 0);
        InstitutionConfigInfo configInfo = institutionConfigApi.getInstitutionConfigInfo(params.getInstitutionId());
        result.setInstitutionName(configInfo.getInstitutionName());
        result.setLogoUrl(configInfo.getLogoUrl());
        result.setTitle(configInfo.getTitle());
        if (appointment == null) {
            // 不存在预约记录，则查询默认电话号码
            DecryptMobileResponse decryptMobileResponse = userInfoAPIService.decryptMobile(userBaseInfoResponse.getResult().getEncryptMobileTel());
            // 电话号码解密查询失败
            if (decryptMobileResponse == null || !decryptMobileResponse.getSuccess() || StringUtils.isEmpty(decryptMobileResponse.getResult())) {
                res.setErrorCode(ErrorCodeEnum.DecryptMobileQueryERROR.getCode());
                res.setFirstError(ErrorCodeEnum.DecryptMobileQueryERROR.getMsg());
                return res;
            }
            // 电话号码加密
            String encryptMobile = null;
            try {
                encryptMobile = AesUtils.Encrypt(AesConst.MODE, AesConst.PACK, lcsAuthKey, AesConst.SIV, decryptMobileResponse.getResult());
            } catch (Exception e) {
                LOGGER.error("AesUtils encrypt error, msg: ", e);
                res.setFirstError(ErrorCodeEnum.SystemERROR.getMsg());
                res.setErrorCode(ErrorCodeEnum.SystemERROR.getCode());
                return res;
            }
            // 加密后的电话
            result.setEncryptMobileTel(encryptMobile);
            // 设置用户信息
            result.setCustomerName(userBaseInfoResponse.getResult().getCustomerName());
            result.setSex(userBaseInfoResponse.getResult().getSex());
            // 电话掩码
            String maskMobile = getMaskTel(decryptMobileResponse.getResult());
            result.setMaskMobileTel(maskMobile);
            result.setLcsPersonalInfoResponse(lcsInfo);
            res.setData(result);
            return res;
        }
        // 存在预约记录
        // 电话号码解密
        String decryptPhone = null;
        try {
            decryptPhone = AesUtils.Decrypt(AesConst.MODE, AesConst.PACK, lcsAuthKey, AesConst.SIV, appointment.getPhone());
        } catch (Exception e) {
            LOGGER.error("AesUtils decrypt error, msg: ", e);
            res.setFirstError(ErrorCodeEnum.SystemERROR.getMsg());
            res.setErrorCode(ErrorCodeEnum.SystemERROR.getCode());
            return res;
        }
        // 设置格式化预约时间
        BeanUtils.copyProperties(appointment, result);
        LocalDateTime localDateTime = DateHelper.date2LocalDateTime(appointment.getAppointment());
        result.setAppointment(DateHelper.dateToStr(localDateTime, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));
        result.setEncryptMobileTel(appointment.getPhone());
        // 设置用户信息返回
        result.setCustomerName(userBaseInfoResponse.getResult().getCustomerName());
        result.setSex(userBaseInfoResponse.getResult().getSex());
        // 手机号设置掩码
        result.setMaskMobileTel(getMaskTel(decryptPhone));

        result.setLcsPersonalInfoResponse(lcsInfo);
        res.setData(result);
        return res;
    }

    @Override
    public ApiResponse<AppointmentAvailableResponse, Object> queryCanBeAppointment(AppointmentQueryRequest params) {
        ApiResponse<AppointmentAvailableResponse, Object> res = new ApiResponse<>();
        // 参数校验
        if (params == null || StringUtils.isEmpty(params.getFinancialPlannerId()) || StringUtils.isEmpty(params.getInstitutionId())) {
            res.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
            res.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
            return res;
        }
        AppointmentAvailableResponse result = getCanBeAppointment(params.getFinancialPlannerId(), params.getInstitutionId(), params.getPassportid());
        if (result == null) {
            res.setErrorCode(ErrorCodeEnum.TradeDayApiError.getCode());
            res.setFirstError(ErrorCodeEnum.TradeDayApiError.getMsg());
            return res;
        }
        res.setData(result);
        return res;
    }

    /**
     * 获取30天可预约的时段的预约详情
     *
     * @param financialPlannerId 理财师通行证id
     * @param passportId         用户通行证id
     * @return 返回30天可交易时间的预约状态
     */
    private AppointmentAvailableResponse getCanBeAppointment(String financialPlannerId, String institutionId, String passportId) {
        // 30天自然日
        List<String> tradeDays = create30Days(1);
        if (CollectionUtils.isEmpty(tradeDays)){
            return null;
        }
        String begin = tradeDays.get(0);
        String end = tradeDays.get(tradeDays.size() - 1);
        // 理财师30个交易日的预约情况
        List<AppointmentInfo> lcsAppointments = appointmentMysqlService.queryAppointmentInfoByPassportIdAndTradeDays(financialPlannerId, institutionId,
                null, begin, end);
        // 查询用户30个交易日的预约情况
        List<AppointmentInfo> userAppointments = appointmentMysqlService.queryAppointmentInfoByPassportIdAndTradeDays(null, null,
                passportId, begin, end);
        return filterAvailableAppointment(tradeDays, lcsAppointments, userAppointments, financialPlannerId);
    }

    @Override
    public ApiResponse<Boolean, Object> addOrUpdateAppointment(AddAppointmentRequest request) {
        ApiResponse<Boolean, Object> res = new ApiResponse<>();
        boolean success = false;
        // 并发预约锁
        String lockKey = String.format(RedisConstant.APPOINT_USER_LOCK, request.getPassportid());
        // 用户并发锁
        Long incr = app.rediswrite.incrBy(lockKey, 1L, 10L);
        if (incr != 1L) {
            res.setErrorCode(ErrorCodeEnum.LOCKERROR.getCode());
            res.setFirstError(ErrorCodeEnum.LOCKERROR.getMsg());
            return res;
        }
        int appointmentStatus = -1;
        String appointId = request.getId();
        try {
            if (request.getId() == null) {
                // 校验参数
                if (checkParams(res, request, null)) {
                    return res;
                }
                // 查询用户信息
                // 获取交易号
                String customerNo = passportBindApi.getPassportBindInfo(request.getPassportid());
                if (customerNo == null) {
                    LOGGER.info("{}无交易账号", request.getPassportid());
                    res.setErrorCode(ErrorCodeEnum.NoCustomerNo.getCode());
                    res.setFirstError(ErrorCodeEnum.NoCustomerNo.getMsg());
                    return res;
                }

                // 根据交易账号查询用户信息
                UserBaseInfoResponse userBaseInfoResponse = userInfoAPIService.getUserBaseInfo(customerNo);
                // 用户信息查询错误
                if (userBaseInfoResponse == null || !userBaseInfoResponse.getSucceed()) {
                    res.setErrorCode(ErrorCodeEnum.UserBaseInfoQueryERROR.getCode());
                    res.setFirstError(ErrorCodeEnum.UserBaseInfoQueryERROR.getMsg());
                    return res;
                }
                // 新增
                // 预约主键id，时间戳 _ 用户通行证id
                String id = System.currentTimeMillis() / 1000 + "_" + request.getPassportid();

                // 校验重复预约 是否与当前机构下的理财师存在还未回访的预约
                boolean hasRepeatAppointment = appointmentMysqlService.hasRepeatAppointment(request.getPassportid(), request.getInstitutionId(),
                        DateHelper.dateToStr(LocalDateTime.now(), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));
                if (hasRepeatAppointment) {
                    res.setErrorCode(ErrorCodeEnum.RepeatAddAppointment.getCode());
                    res.setFirstError(ErrorCodeEnum.RepeatAddAppointment.getMsg());
                    return res;
                }

                // 判断当前预约时间是否满员
                boolean checked = checkAppointmentFull(request, res);
                if (!checked) {
                    //无法预约
                    return res;
                }
                success = appointmentMysqlService.addOrUpdateAppointment(id, request.getAppointment(),
                        request.getPhone(), request.getInstitutionId(), request.getFinancialPlannerId(), request.getPassportid(),
                        userBaseInfoResponse.getResult().getCustomerName(), userBaseInfoResponse.getResult().getSex(), "ADD");
                // 数据插入失败 回滚缓存
                if (!success) {
                    String key = String.format(RedisConstant.APPOINT_COUNT, request.getFinancialPlannerId(), request.getAppointment());
                    appointRedisRollBack(key, request.getPassportid());
                } else {
                    //更新IM消息 redis写入新来的预约信息
                    appointmentStatus = AppointmentConst.APPOINTMENT_SUCCESS;
                    appointId = id;
                    String redisKey = String.format(RedisConstant.LCS_APPOINTMENT_MSG_KEY, request.getFinancialPlannerId());
                    app.rediswrite.hSet(redisKey, request.getPassportid(), "1");
                    app.rediswrite.expire(redisKey, Constant.CACHETIMEHOUR24);
                }
            } else {
                // 更新
                // 校验记录和更新权限
                AppointmentInfo appointment = appointmentMysqlService.queryAppointmentById(request.getId());
                if (appointmentRecordCheck(res, request.getPassportid(), appointment) == null) {
                    return res;
                }
                // 校验参数
                if (checkParams(res, request, appointment)) {
                    return res;
                }
                // 如果预约时间修改，则进行满员判断 否则跳过
                boolean hasChangeTime = appointment.getAppointment().getTime() != DateHelper.stringToDate2(request.getAppointment(), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS).getTime();
                if (hasChangeTime) {
                    // 判断当前预约时间是否满员
                    boolean checked = checkAppointmentFull(request, res);
                    if (!checked) {
                        //无预约名额
                        return res;
                    }
                }
                success = appointmentMysqlService.addOrUpdateAppointment(request.getId(), request.getAppointment(),
                        request.getPhone(), request.getInstitutionId(), request.getFinancialPlannerId(), request.getPassportid(),
                        null, null, "UPDATE");
                // 数据插入失败 回滚缓存
                if (!success && hasChangeTime) {
                    String key = String.format(RedisConstant.APPOINT_COUNT, request.getFinancialPlannerId(), request.getAppointment());
                    appointRedisRollBack(key, request.getPassportid());
                }
                // 更新成功且更改过时间 修改之前预约时段的理财师预约人数计数 redis写入修改预约信息
                if (success && hasChangeTime) {
                    Date date = appointment.getAppointment();
                    String dateStr = DateHelper.dateToStr(date, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
                    String key = String.format(RedisConstant.APPOINT_COUNT, appointment.getFinancialPlannerId(), dateStr);
                    appointRedisRollBack(key, request.getPassportid());
                    appointmentStatus = AppointmentConst.APPOINTMENT_UPDATE;
                    String redisKey = String.format(RedisConstant.LCS_APPOINTMENT_MSG_KEY, request.getFinancialPlannerId());
                    app.rediswrite.hSet(redisKey, request.getPassportid(), "1");
                    app.rediswrite.expire(redisKey, Constant.CACHETIMEHOUR24);
                }
            }
            if (!success) {
                res.setErrorCode(ResultCode.errorcode501);
                res.setFirstError("预约失败");
                return res;
            }
            imApiService.sendAppointInfoMsg(request.getPassportid(), request.getFinancialPlannerId(), appointId, request.getAppointment(), appointmentStatus);
            return res;
        } finally {
            app.rediswrite.del(lockKey);
        }
    }

    /**
     * 校验预约时段是否满员
     *
     * @param request
     * @param res
     * @return
     */
    private Boolean checkAppointmentFull(AddAppointmentRequest request, ApiResponse<Boolean, Object> res) {
        Date date = strToDate(res, request.getAppointment());
        if (date == null) {
            return false;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        String dateKey = DateHelper.dateToStr(date, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        String key = String.format(RedisConstant.APPOINT_COUNT, request.getFinancialPlannerId(), dateKey);
        // 加锁
        String lockKey = String.format(RedisConstant.APPOINT_LOCK, request.getAppointment(), request.getFinancialPlannerId());
        Long isLock = app.rediswrite.incrBy(lockKey, 1L, 10L);
        if (isLock != 1L) {
            res.setErrorCode(ErrorCodeEnum.LOCKERROR.getCode());
            res.setFirstError(ErrorCodeEnum.LOCKERROR.getMsg());
            return false;
        }
        try {
            Map<String, String> map = app.redisread.hgetAll(key);
            // 判断是否需要初始化, 上午下午分开初始化
            if (map == null || map.size() == 0) {
                map = initAppointmentMap(hour);
            }
            // 排序
            List<Map.Entry<String, String>> list = new ArrayList<>(map.entrySet());
            list.sort((o1, o2) -> {
                String key1 = o1.getKey();
                String key2 = o2.getKey();
                try {
                    SimpleDateFormat format = new SimpleDateFormat("HH:mm:ss");
                    Date date1 = format.parse(key1);
                    Date date2 = format.parse(key2);
                    if (date1.getTime() == date2.getTime()) {
                        return 0;
                    }
                    return date1.after(date2) ? 1 : -1;
                } catch (Exception e) {
                    LOGGER.error("redis获取时间格式转换错误");
                }
                return 0;
            });
            // 根据当前服务器时间计算预约时间段的起始时间点
            String appointmentTime = startAppointmentTime(request.getAppointment());
            if (appointmentTime == null) {
                return false;
            }
            boolean isFirstEntry = false;
            for (Map.Entry<String, String> entry : list) {
                if (entry.getKey().equals(appointmentTime)) {
                    isFirstEntry = true;
                }
                if (isFirstEntry && "0".equals(entry.getValue())) {
                    entry.setValue(request.getPassportid());
                    app.rediswrite.hmset(key, map);
                    // 设置过期时间
                    long expireTime = calculateExpireTime(request.getAppointment());
                    app.rediswrite.expire(key, expireTime);
                    return true;
                }
            }
            res.setErrorCode(ErrorCodeEnum.AppointmentFullERROR.getCode());
            res.setFirstError(ErrorCodeEnum.AppointmentFullERROR.getMsg());
            return false;
        } finally {
            app.rediswrite.del(lockKey);
        }
    }

    @Override
    public ApiResponse<Boolean, Object> cancelAppointment(AppointmentQueryRequest request) {
        ApiResponse<Boolean, Object> res = new ApiResponse<>();
        // 并发预约锁,避免同一用户多端同时操作
        String lockKey = String.format(RedisConstant.APPOINT_USER_LOCK, request.getPassportid());
        Long incr = app.rediswrite.incrBy(lockKey, 1L, 10L);
        if (incr != 1L) {
            res.setErrorCode(ErrorCodeEnum.LOCKERROR.getCode());
            res.setFirstError(ErrorCodeEnum.LOCKERROR.getMsg());
            return res;
        }
        // 参数校验
        try {
            if (StringUtils.isEmpty(request.getId())) {
                res.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
                res.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
                return res;
            }
            AppointmentInfo appointment = appointmentMysqlService.queryAppointmentById(request.getId());
            AppointmentInfo appointmentResponse = appointmentRecordCheck(res, request.getPassportid(), appointment);
            if (appointmentResponse == null) {
                return res;
            }
            boolean success = appointmentMysqlService.deleteAppointment(request.getId());
            if (success) {
                // 回滚redis中预约记录情况
                Date date = appointmentResponse.getAppointment();
                String dateStr = DateHelper.dateToStr(date, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
                String key = String.format(RedisConstant.APPOINT_COUNT, request.getFinancialPlannerId(), dateStr);
                // 回滚
                appointRedisRollBack(key, request.getPassportid());
                String redisKey = String.format(RedisConstant.LCS_APPOINTMENT_MSG_KEY, request.getFinancialPlannerId());
                app.rediswrite.hdel(redisKey, request.getPassportid());
                // 发送IM消息
                imApiService.sendAppointInfoMsg(request.getPassportid(),request.getFinancialPlannerId(),request.getId(),dateStr,AppointmentConst.APPOINTMENT_CANCEL);
            } else {
                res.setErrorCode(ResultCode.errorcode501);
                res.setFirstError("取消预约失败");
            }
            return res;
        } finally {
            app.rediswrite.del(lockKey);
        }
    }

    /**
     * 校验预约记录可否操作
     *
     * @param res
     * @param passportId
     * @param appointment 已存在的预约记录
     * @return
     */
    private AppointmentInfo appointmentRecordCheck(ApiResponse<Boolean, Object> res, String passportId, AppointmentInfo appointment) {
        if (appointment == null) {
            res.setErrorCode(ErrorCodeEnum.AppointmentNullERROR.getCode());
            res.setFirstError(ErrorCodeEnum.AppointmentNullERROR.getMsg());
            return null;
        }
        if (!appointment.getPassportId().equals(passportId)) {
            // 操作人非预约所有者
            res.setErrorCode(ErrorCodeEnum.OperationError.getCode());
            res.setFirstError(ErrorCodeEnum.OperationError.getMsg());
            return null;
        }
        // 预约已过期或者当前正处于预约时间段，不可进行预约
        if (System.currentTimeMillis() > appointment.getAppointment().getTime()) {
            res.setErrorCode(ErrorCodeEnum.AppointmentUnableOperate.getCode());
            res.setFirstError(ErrorCodeEnum.AppointmentUnableOperate.getMsg());
            return null;
        }
        Calendar appointmentCalendar = Calendar.getInstance();
        Calendar now = Calendar.getInstance();
        appointmentCalendar.setTime(appointment.getAppointment());
        now.setTime(new Date());

        if (appointmentCalendar.get(Calendar.HOUR_OF_DAY) < 12) {
            // 上午 当前时间和预约时间差不超过两个半小时，则当前时间正处于预约时间段内
            double time = (appointmentCalendar.getTime().getTime() - now.getTime().getTime()) / 3600000d;
            if (time <= 2.5) {
                res.setErrorCode(ErrorCodeEnum.AppointmentUnableOperate.getCode());
                res.setFirstError(ErrorCodeEnum.AppointmentUnableOperate.getMsg());
                return null;
            }
        } else {
            // 下午 当前时间和预约时间差不超过5个小时，则当前时间正处于预约时间段内
            double time = (appointmentCalendar.getTime().getTime() - now.getTime().getTime()) / 3600000d;
            if (time <= 5.0) {
                res.setErrorCode(ErrorCodeEnum.AppointmentUnableOperate.getCode());
                res.setFirstError(ErrorCodeEnum.AppointmentUnableOperate.getMsg());
                return null;
            }
        }
        return appointment;
    }

    /**
     * 检查参数
     *
     * @param res
     * @param request
     * @param oldAppointment 已存在的预约记录
     * @return 有错误返回true
     */
    private boolean checkParams(ApiResponse<Boolean, Object> res, AddAppointmentRequest request, AppointmentInfo oldAppointment) {
        if (request == null || request.getAppointment() == null || request.getPhone() == null
                || request.getFinancialPlannerId() == null || request.getInstitutionId() == null) {
            res.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
            res.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
            return true;
        }
        // 手机号校验
        try {
            String phone = AesUtils.Decrypt(AesConst.MODE, AesConst.PACK, lcsAuthKey, AesConst.SIV, request.getPhone());
            if (phone == null) {
                res.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
                res.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
                return true;
            }
            if (!CommonUtil.checkMobile(phone)) {
                res.setErrorCode(ErrorCodeEnum.MobileFormatError.getCode());
                res.setFirstError(ErrorCodeEnum.MobileFormatError.getMsg());
                return true;
            }
        } catch (Exception e) {
            LOGGER.error("电话号码解密失败");
            return true;
        }
        // 校验理财师id和机构id是否匹配,且为理财师角色
        UserRolePO userRolePO = userRoleMysql.selectByInsAndPassId(request.getInstitutionId(), request.getFinancialPlannerId());
        if (userRolePO == null) {
            res.setErrorCode(ErrorCodeEnum.LcsMatchingERROR.getCode());
            res.setFirstError(ErrorCodeEnum.LcsMatchingERROR.getMsg());
            return true;
        }
        // 判断理财师是否与用户签约或绑定
        boolean hasContract = contractMysql.hasRecordByPassAndFpass(request.getPassportid(), request.getFinancialPlannerId(),request.getInstitutionId());
        if (!hasContract) {
            res.setErrorCode(ErrorCodeEnum.LcsAppointmentERROR.getCode());
            res.setFirstError(ErrorCodeEnum.LcsAppointmentERROR.getMsg());
            return true;
        }
        Date date = strToDate(res, request.getAppointment());
        if (date == null) {
            return true;
        }
        // 日期校验
        if (!checkDateFormat(request.getAppointment())) {
            res.setErrorCode(ErrorCodeEnum.DateFormatERROR.getCode());
            res.setFirstError(ErrorCodeEnum.DateFormatERROR.getMsg());
            return true;
        }
        if (date.getTime() < System.currentTimeMillis()) {
            res.setErrorCode(ErrorCodeEnum.AppointmentERROR.getCode());
            res.setFirstError(ErrorCodeEnum.AppointmentERROR.getMsg());
            return true;
        }
        if (StringUtils.isNotEmpty(request.getId())) {
            // 更新时，提交更新的预约时间不变则无需进行后续判断
            if (oldAppointment == null) {
                res.setErrorCode(ErrorCodeEnum.AppointmentNullERROR.getCode());
                res.setFirstError(ErrorCodeEnum.AppointmentNullERROR.getMsg());
                return true;
            }
            Date updateDate = strToDate(res, request.getAppointment());
            if (updateDate == null) {
                // 时间转换失败
                return true;
            }
            if (updateDate.getTime() == oldAppointment.getAppointment().getTime()) {
                // 预约时间没变化
                return false;
            }
        }
        // 检查预约时间是否为可选时间
        // 30天可选时间
        AppointmentAvailableResponse canBeAppointment = getCanBeAppointment(request.getFinancialPlannerId(), request.getInstitutionId(), request.getPassportid());
        if (canBeAppointment == null) {
            res.setErrorCode(ErrorCodeEnum.TradeDayApiError.getCode());
            res.setFirstError(ErrorCodeEnum.TradeDayApiError.getMsg());
            return true;
        }
        Map<String, List<Integer>> appointmentMap = canBeAppointment.getAppointment();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        String day = DateHelper.dateToStr(date, DateHelper.FORMAT_YYYY_MM_DD);
        if (appointmentMap.containsKey(day)) {
            if (calendar.get(Calendar.HOUR_OF_DAY) < 12) {
                //上午
                Integer status = appointmentMap.get(day).get(0);
                ErrorCodeEnum errorCodeEnum = checkStatus(status);
                if (errorCodeEnum != null) {
                    res.setErrorCode(errorCodeEnum.getCode());
                    res.setFirstError(errorCodeEnum.getMsg());
                    return true;
                }
                return false;
            }
            if (calendar.get(Calendar.HOUR_OF_DAY) > 12) {
                //下午
                Integer status = appointmentMap.get(day).get(1);
                ErrorCodeEnum errorCodeEnum = checkStatus(status);
                if (errorCodeEnum != null) {
                    res.setErrorCode(errorCodeEnum.getCode());
                    res.setFirstError(errorCodeEnum.getMsg());
                    return true;
                }
                return false;
            }
        }

        // 2.0.0版本默认只能预约1个月内的时间，超出时间不可进行预约
        res.setErrorCode(ErrorCodeEnum.AppointmentERROR.getCode());
        res.setFirstError(ErrorCodeEnum.AppointmentERROR.getMsg());
        return true;
    }

    /**
     * 预约时间判断结果，匹配返回提示语
     *
     * @param dailyStatus
     * @return
     */
    private ErrorCodeEnum checkStatus(Integer dailyStatus) {
        if (dailyStatus == AppointmentStateConst.APPOINTMENT_DATE_ERROR) {
            return ErrorCodeEnum.AppointmentERROR;
        }
        if (dailyStatus == AppointmentStateConst.F_USER_APPOINTMENT_FULL) {
            return ErrorCodeEnum.AppointmentFullERROR;
        }
        if (dailyStatus == AppointmentStateConst.USER_APPOINTMENT_REPEAT) {
            return ErrorCodeEnum.AppointmentUnavailable;
        }
        if (dailyStatus == AppointmentStateConst.APPOINTMENT_NOT_ENOUGH) {
            return ErrorCodeEnum.NotEnoughTime;
        }
        return null;
    }

    /**
     * 预约时间参数是否为固定值校验
     *
     * @param appointment
     * @return
     */
    private boolean checkDateFormat(String appointment) {
        String[] split = appointment.split(" ");
        if (split.length != 2) {
            return false;
        }
        return AppointmentConst.APPOINTMENT_FORENOON.equals(split[1]) || AppointmentConst.APPOINTMENT_AFTERNOON.equals(split[1]);
    }

    /**
     * 日期字符串转Date
     *
     * @param res
     * @param date
     * @return
     */
    private Date strToDate(ApiResponse<Boolean, Object> res, String date) {
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date parse = null;
        // 校验时间格式和日期合理性
        try {
            format.setLenient(false);
            parse = format.parse(date);
        } catch (ParseException e) {
            res.setErrorCode(ErrorCodeEnum.DateFormatERROR.getCode());
            res.setFirstError(ErrorCodeEnum.DateFormatERROR.getMsg());
            return null;
        }
        return parse;
    }

    /**
     * 过滤不可预约时间
     *
     * @param tradeDays
     * @param financialPlannerId
     * @return
     */
    private AppointmentAvailableResponse filterAvailableAppointment(List<String> tradeDays, List<AppointmentInfo> lcsAppointments,
                                                                    List<AppointmentInfo> userAppointments, String financialPlannerId) {
        AppointmentAvailableResponse res = new AppointmentAvailableResponse();
        Set<String> tradeDaySet = tradeDays.stream().map(str -> str.split(" ")[0]).collect(Collectors.toSet());
        Map<String, List<Integer>> map = unavailableAppointment(tradeDaySet, lcsAppointments, userAppointments, financialPlannerId);
        for (String day : tradeDaySet) {
            if (map.containsKey(day)) {
                continue;
            }
            List<Integer> dailyStatus = new ArrayList<>();
            dailyStatus.add(AppointmentStateConst.APPOINTMENT_AVAILABLE);
            dailyStatus.add(AppointmentStateConst.APPOINTMENT_AVAILABLE);
            map.put(day, dailyStatus);
        }
        res.setAppointment(map);
        return res;
    }

    /**
     * 检查理财师预约冲突，并设置时间段是否可选状态
     *
     * @param lcsAppointments
     * @return
     */
    private Map<String, List<Integer>> unavailableAppointment(Set<String> tradeDaySet, List<AppointmentInfo> lcsAppointments,
                                                              List<AppointmentInfo> userAppointments, String financialPlannerId) {
        Map<String, List<Integer>> map = new TreeMap<>();
        // 设置理财师的预约情况
        setAppointmentStatus(lcsAppointments, map, "lcs");
        // 设置用户的预约情况
        setAppointmentStatus(userAppointments, map, "user");
        // 设置当前的预约情况过滤
        setNowDayStatus(map, financialPlannerId, tradeDaySet);
        return map;
    }

//    private void setNotTradeDayStatus(List<String> tradeDays, Map<String, List<Integer>> map) {
//        PriceApiResponse tradeDay = priceApi.getTradeDay(new Date(), 30, true, true);
//        if (null == tradeDay) {
//            LOGGER.error("行情交易日查询为null");
//            return;
//        }
//        Set<String> tradeSet = new HashSet<>();
//        for (PriceApiTradeDay day : tradeDay.getData()) {
//            tradeSet.add(day.getDATETIME());
//        }
//        for (String day : tradeDays) {
//            if (tradeSet.contains(day)) {
//                continue;
//            }
//            String key = day.split(" ")[0];
//            ArrayList<Integer> status = new ArrayList<>();
//            status.add(AppointmentStateConst.APPOINTMENT_DATE_ERROR);
//            status.add(AppointmentStateConst.APPOINTMENT_DATE_ERROR);
//            map.put(key, status);
//        }
//    }

    private void setNowDayStatus(Map<String, List<Integer>> map, String financialPlannerId, Set<String> tradeDaySet) {
        Date now = new Date();
        String key = DateHelper.dateToStr(now, DateHelper.FORMAT_YYYY_MM_DD);
        List<Integer> nowDayStatus = null;
        if (!map.containsKey(key)) {
            // 无预约信息的情况
            if (!tradeDaySet.contains(key)) {
                // 今天为非交易日，无需处理
                return;
            }
            // 今天为交易日，默认全时段可预约
            nowDayStatus = new ArrayList<>(2);
            nowDayStatus.add(AppointmentStateConst.APPOINTMENT_AVAILABLE);
            nowDayStatus.add(AppointmentStateConst.APPOINTMENT_AVAILABLE);
            map.put(key, nowDayStatus);
        } else {
            nowDayStatus = map.get(key);
        }
        String appointmentForenoon = key + " " + AppointmentConst.APPOINTMENT_FORENOON;
        String appointmentAfternoon = key + " " + AppointmentConst.APPOINTMENT_AFTERNOON;

        Date forenoon = DateHelper.stringToDate2(appointmentForenoon, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
        Date afternoon = DateHelper.stringToDate2(appointmentAfternoon, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);

        // 理财师当前时段预约情况判断
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);

        String redisKey;
        Map<String, String> appointMap;
        String appointmentTime;
        if (calendar.get(Calendar.HOUR_OF_DAY) < 12) {
            redisKey = String.format(RedisConstant.APPOINT_COUNT, financialPlannerId, appointmentForenoon);
            appointMap = app.redisread.hgetAll(redisKey);
            appointmentTime = appointmentForenoon;
        } else {
            redisKey = String.format(RedisConstant.APPOINT_COUNT, financialPlannerId, appointmentAfternoon);
            appointMap = app.redisread.hgetAll(redisKey);
            appointmentTime = appointmentAfternoon;
        }
        // 判断是否需要初始化, 上午下午分开初始化
        if (appointMap == null || appointMap.size() == 0) {
            appointMap = initAppointmentMap(calendar.get(Calendar.HOUR_OF_DAY));
        }
        // redis查询map按照key排序
        List<Map.Entry<String, String>> list = new ArrayList<>(appointMap.entrySet());
        list.sort((o1, o2) -> {
            String key1 = o1.getKey();
            String key2 = o2.getKey();
            try {
                SimpleDateFormat format = new SimpleDateFormat("HH:mm:ss");
                Date date1 = format.parse(key1);
                Date date2 = format.parse(key2);
                if (date1.getTime() == date2.getTime()) {
                    return 0;
                }
                return date1.after(date2) ? 1 : -1;
            } catch (Exception e) {
                LOGGER.error("redis获取时间格式转换错误");
            }
            return 0;
        });

        boolean canBeAppoint = false;
        // 上午状态是否为可用，不可用状态不能进行状态复写
        boolean morningAvailable = nowDayStatus.get(0) == AppointmentStateConst.APPOINTMENT_AVAILABLE;
        boolean afternoonAvailable = nowDayStatus.get(1) == AppointmentStateConst.APPOINTMENT_AVAILABLE;
        String firstTime = startAppointmentTime(appointmentTime);
        if (firstTime != null) {
            boolean isFirstEntry = false;
            for (Map.Entry<String, String> entry : list) {
                if (entry.getKey().equals(firstTime)) {
                    isFirstEntry = true;
                }
                if (isFirstEntry && "0".equals(entry.getValue())) {
                    canBeAppoint = true;
                }
            }
        }
        // 当前时段已满或不能预约
        if (!canBeAppoint) {
            if (appointmentTime.equals(appointmentForenoon) && morningAvailable) {
                nowDayStatus.set(0, AppointmentStateConst.F_USER_APPOINTMENT_FULL);
            } else {
                if (afternoonAvailable) {
                    nowDayStatus.set(1, AppointmentStateConst.F_USER_APPOINTMENT_FULL);
                }
            }
        }

        // 超过上午、或者距离上午时间段截止时间不足30分钟
        if (now.getTime() >= forenoon.getTime()) {
            if (morningAvailable) {
                nowDayStatus.set(0, AppointmentStateConst.APPOINTMENT_DATE_ERROR);
            }
        } else if ((forenoon.getTime() - now.getTime()) / 1000 < 1800) {
            if (morningAvailable) {
                nowDayStatus.set(0, AppointmentStateConst.APPOINTMENT_NOT_ENOUGH);
            }
        }
        if (now.getTime() >= afternoon.getTime()) {
            if (afternoonAvailable) {
                nowDayStatus.set(1, AppointmentStateConst.APPOINTMENT_DATE_ERROR);
            }
        } else if ((afternoon.getTime() - now.getTime()) / 1000 < 1800) {
            if (afternoonAvailable) {
                nowDayStatus.set(1, AppointmentStateConst.APPOINTMENT_NOT_ENOUGH);
            }
        }
    }

    /**
     * 根据理财师和用户设置预约时间是否可用的状态
     * 理财师通过人数判断设置，用户通过有无预约记录判断设置
     *
     * @param appointments
     * @param map
     * @param type
     */
    private void setAppointmentStatus(List<AppointmentInfo> appointments, Map<String, List<Integer>> map, String type) {
        // key为预约时间段 value为该时段的预约个数
        Map<String, Integer> countMap = new HashMap<>();
        // 统计各时段的未处理预约个数
        for (AppointmentInfo a : appointments) {
            if (a.getStatus() != 0 && !"lcs".equals(type)) {
                continue;
            }
            String key = DateHelper.dateToStr(a.getAppointment(), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
            countMap.put(key, countMap.getOrDefault(key, 0) + 1);
        }
        // 根据各时段的预约个数 分理财师和用户进行判断
        for (Map.Entry<String, Integer> entry : countMap.entrySet()) {
            LocalDateTime time = DateHelper.stringToDate(entry.getKey(), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
            String resultMapKey = DateHelper.dateToStr(time, DateHelper.FORMAT_YYYY_MM_DD);
            List<Integer> dailyStatus = new ArrayList<>(2);
            dailyStatus.add(AppointmentStateConst.APPOINTMENT_AVAILABLE);
            dailyStatus.add(AppointmentStateConst.APPOINTMENT_AVAILABLE);
            if (map.containsKey(resultMapKey)) {
                dailyStatus = map.get(resultMapKey);
            }
            if (time.getHour() < 12) {
                if (dailyStatus.get(0) != AppointmentStateConst.APPOINTMENT_AVAILABLE) {
                    // 非可用状态已被设置，直接跳过判断，避免覆盖
                    continue;
                }
                // 上午
                if ("lcs".equals(type)) {
                    // 理财师
                    if (entry.getValue() >= 5) {
                        dailyStatus.set(0, AppointmentStateConst.F_USER_APPOINTMENT_FULL);
                    }
                } else {
                    //用户
                    dailyStatus.set(0, AppointmentStateConst.USER_APPOINTMENT_REPEAT);
                }
            } else {
                if (dailyStatus.get(1) != AppointmentStateConst.APPOINTMENT_AVAILABLE) {
                    // 非可用状态已被设置，直接跳过判断，避免覆盖
                    continue;
                }
                // 下午
                if ("lcs".equals(type)) {
                    // 理财师
                    if (entry.getValue() >= 10) {
                        dailyStatus.set(1, AppointmentStateConst.F_USER_APPOINTMENT_FULL);
                    }
                } else {
                    //用户
                    dailyStatus.set(1, AppointmentStateConst.USER_APPOINTMENT_REPEAT);
                }
            }
            map.put(resultMapKey, dailyStatus);
        }
    }

    /**
     *
     * @param type 0=30天自然日 1:30天交易日
     * @return 预约日期
     */

    private List<String> create30Days(int type) {
        List<String> res = new ArrayList<>();
        if (type == 0) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            // 时间归零
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            while (res.size() < 30) {
                Date time = calendar.getTime();
                res.add(DateHelper.dateToStr(time, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));
                calendar.add(Calendar.DATE, 1);
            }
        } else {
            // 获取30个交易日
            PriceApiResponse tradeDay = priceApi.getTradeDay(new Date(), 60, true, true);
            if (null == tradeDay) {
                LOGGER.error("行情交易日查询为null");
                return res;
            }
            for (int i = 0; i < Math.min(tradeDay.getData().length,30); i++) {
                res.add(tradeDay.getData()[i].getDATETIME());
            }
        }
        return res;
    }

    /**
     * 用户预留手机号设置掩码
     */
    private String getMaskTel(String mobile) {
        return mobile.substring(0, 3) + "****" + mobile.substring(7);
    }

    /**
     * @param passportId 通行证id
     * @return
     * @Description： 根据通行证id获取v
     * <AUTHOR>
     * @Date 2022/5/31 17:25
     */
    public Integer getVByPassportId(String passportId, ApiResponse<AppointmentInfoResponse, Object> result) {
        String params = "followuids=" + passportId;
        String html = HttpHelper.requestGet(appConfig.user_basic_info, params, 3000);
        if (StringUtils.isNotEmpty(html)) {
            ApiResponse apiResponse = JSON.parseObject(html, ApiResponse.class);
            Object data = apiResponse.getData();
            if (!Objects.isNull(data)) {
                List<UserInfoBO> userInfoBOS = JSON.parseArray(JSON.toJSONString(data), UserInfoBO.class);
                if (!CollectionUtils.isEmpty(userInfoBOS)) {
                    for (UserInfoBO userInfoBO : userInfoBOS) {
                        if (passportId.equals(userInfoBO.getPassportId())) {
                            return userInfoBO.getUserV();
                        }
                    }
                    // 如果没有找到对应的通行证ID
                    result.setFirstError(ErrorCodeEnum.ErrorCode501.getMsg());
                    result.setErrorCode(ErrorCodeEnum.ErrorCode501.getCode());
                } else {
                    result.setFirstError(ErrorCodeEnum.ErrorCode501.getMsg());
                    result.setErrorCode(ErrorCodeEnum.ErrorCode501.getCode());
                }
            } else {
                result.setFirstError(ErrorCodeEnum.ErrorCode501.getMsg());
                result.setErrorCode(ErrorCodeEnum.ErrorCode501.getCode());
            }
        } else {
            result.setFirstError("加V认证请求超时");
            result.setErrorCode(ErrorCodeEnum.ErrorCode501.getCode());
        }
        // 如果上面不成功，默认当前通行证id不加V
        return 0;
    }

    /**
     * 初始化预约map
     *
     * @param hour 24小时制
     * @return
     */
    private Map<String, String> initAppointmentMap(int hour) {
        Map<String, String> map = new HashMap<>();
        if (hour < 12) {
            // 上午
            map.put("09:00:00", "0");
            map.put("09:30:00", "0");
            map.put("10:00:00", "0");
            map.put("10:30:00", "0");
            map.put("11:00:00", "0");
            return map;
        }
        // 下午
        map.put("13:00:00", "0");
        map.put("13:30:00", "0");
        map.put("14:00:00", "0");
        map.put("14:30:00", "0");
        map.put("15:00:00", "0");
        map.put("15:30:00", "0");
        map.put("16:00:00", "0");
        map.put("16:30:00", "0");
        map.put("17:00:00", "0");
        map.put("17:30:00", "0");
        return map;
    }

    /**
     * 当前服务器时间计算下一个预约时间起始点
     *
     * @return 下一个预约时间起始点 HH:mm:ss
     */
    private String startAppointmentTime(String appointment) {
        Date appointTime = DateHelper.stringToDate2(appointment, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
        Calendar appointCalendar = Calendar.getInstance();
        appointCalendar.setTime(appointTime);

        //服务器当前时间
        Date nowDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(nowDate);

        // 当前时间不在于预约时间内, true 表示不在
        boolean isAppointTime = false;
        long mistiming = appointTime.getTime() - nowDate.getTime();
        if (appointCalendar.get(Calendar.HOUR_OF_DAY) < 12) {
            // 上午
            isAppointTime = mistiming > 5 * 60 * 30 * 1000;
        } else {
            isAppointTime = mistiming > 10 * 60 * 30 * 1000;
        }

        if (isAppointTime) {
            // 当前不在预约时间内，返回时间为预约时间段的开始时间 上午则为09:00:00 下午为13:00:00
            return appointCalendar.get(Calendar.HOUR_OF_DAY) < 12 ? "09:00:00" : "13:00:00";
        }

        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minutes = calendar.get(Calendar.MINUTE);
        int second = calendar.get(Calendar.SECOND);
        if ((minutes > 0 || second > 0) && (minutes < 30)) {
            minutes = 30;
            second = 0;
        } else if ((minutes > 30 || second > 0)) {
            hour += 1;
            minutes = 0;
            second = 0;
        }
        calendar.set(Calendar.MINUTE, minutes);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.SECOND, second);
        String dateToStr = DateHelper.dateToStr(calendar.getTime(), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
        if (dateToStr == null) {
            LOGGER.error("计算下一个预约时间点时间格式转换错误");
            return null;
        }
        String[] s = dateToStr.split(" ");
        return s[1];
    }

    /**
     * 回滚redis预约情况
     *
     * @param mapKey     预约记录map的key
     * @param passportId 用户通行证id
     */
    private void appointRedisRollBack(String mapKey, String passportId) {
        Map<String, String> map = app.redisread.hgetAll(mapKey);
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (entry.getValue().equals(passportId)) {
                entry.setValue("0");
                String result = app.rediswrite.hmset(mapKey, map);
                if (!"OK".equals(result)) {
                    LOGGER.error("预约redis回滚失败");
                }
            }
        }
    }

    /**
     * 计算key的过期时间，均以预约当天23点59分过期
     *
     * @param appointTime 预约时间 yyyy-MM-dd HH:mm:ss
     * @return 秒
     */
    private long calculateExpireTime(String appointTime) {
        Date now = new Date();
        Date date = DateHelper.stringToDate2(appointTime, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (calendar.get(Calendar.HOUR_OF_DAY) < 12) {
            return (date.getTime() - now.getTime()) / 1000 + 45000;
        }
        return (date.getTime() - now.getTime()) / 1000 + 21600;
    }
}
