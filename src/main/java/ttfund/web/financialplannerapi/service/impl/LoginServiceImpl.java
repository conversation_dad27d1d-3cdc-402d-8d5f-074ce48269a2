package ttfund.web.financialplannerapi.service.impl;

import com.ttfund.web.base.Constant;
import com.ttfund.web.core.dataapi.EMPassportApiImpl;
import com.ttfund.web.core.dataconstant.UserRedisConstant;
import com.ttfund.web.core.model.apimodel.empassportapimodel.EMBaseResponse;
import com.ttfund.web.core.model.apimodel.empassportapimodel.EMVerifyUserForInternalResponse;
import com.ttfund.web.core.model.baserequest.RequestBase;
import com.ttfund.web.core.service.DataCacheImpl;
import com.ttfund.web.core.utils.MD5Utils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import ttfund.web.financialplannerapi.service.LoginService;

import javax.annotation.Resource;

@Service
public class LoginServiceImpl implements LoginService {

    @Resource
    DataCacheImpl dataCache;

    @Resource
    EMPassportApiImpl emPassportApi;

    public Boolean LoginValidCache(RequestBase request, String port, String ip) {
        Boolean result = false;
        if (request != null && StringUtils.isNotEmpty(request.getPassportid())) {
            String tokenmd5 = MD5Utils.stringToMD5(request.getPassportctoken() + request.getPassportutoken());
            String key = String.format(UserRedisConstant.emfund_tokenvalid_passport_uid_tokenmd5, request.getPassportid(), tokenmd5);
            String cachevalue = dataCache.get(key);
            if (!StringUtils.equals(cachevalue, request.getPassportid())) {
                EMBaseResponse<EMVerifyUserForInternalResponse> res =null;
                if (StringUtils.isAllEmpty(port, ip)) {
                    res = emPassportApi.VerifyUserForInternal(request);
                } else {
                    res = emPassportApi.VerifyUserForInternal(request, port, ip);
                }
                if (res != null && "0".equals(res.ReturnCode) && res.Data != null && StringUtils.equals(request.getPassportid(), res.Data.UID)) {
                    cachevalue = request.getPassportid();
                    dataCache.set(key, cachevalue, Constant.CACHETIMEHOUR3);
                }
            }
            result = StringUtils.equals(cachevalue, request.getPassportid());
        }
        return result;
    }
}
