package ttfund.web.financialplannerapi.service.impl;

import com.ttfund.web.core.model.ApiResponse;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ttfund.web.financialplannerapi.data.impl.*;
import ttfund.web.financialplannerapi.model.api.*;
import ttfund.web.financialplannerapi.model.reponse.*;
import ttfund.web.financialplannerapi.service.FinanceDiagService;
import ttfund.web.financialplannerapi.util.other.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Service
public class FinanceDiagServiceImpl implements FinanceDiagService {
    @Autowired
    FinancialPlannerApiImpl financialPlannerApi;
    @Autowired
    FundIndexApiImpl fundIndexApi;
    @Autowired
    AssetDiagnosisV2ApiImpl assetDiagnosisV2Api;
    @Autowired
    FundBondApiImpl fundBondApi;

    @Autowired
    InvestStyleApiImpl investStyleApi;
    @Autowired
    IndustrySourcesApiImpl industrySourcesApi;
    @Autowired
    BoundSourcesApiImpl boundSourcesApi;
    @Autowired
    IndustryAnalysisApiImpl industryAnalysisApi;
    @Autowired
    IntervalProfitDetailApiImpl intervalProfitDetailApi;

    @Override
    public ApiResponse<UserHoldResponse, Object> getUserHold(String customerNo, String subAccountNo) {
        UserHoldResponse response = new UserHoldResponse();
        // 调用 getAssetDiagnosisV2 接口获取基金/组合资产诊断分析数据
        AssetDiagnosisV2Response assetDiagnosisV2Response = assetDiagnosisV2Api.getAssetDiagnosisV2(customerNo, subAccountNo);

        if (assetDiagnosisV2Response == null || assetDiagnosisV2Response.getFundTypeLevel1List() == null) {
            return ApiResponse.succeed(response);
        }

        // 收集所有基金代码
        Set<String> fundCodes = getCode(assetDiagnosisV2Response);

        // 将 fundCode 作为参数调用 getFinancialPlanner 方法获取 概念主题和收益/防守能力数据
        FinancialPlannerResponse financialPlannerResponse = financialPlannerApi.getFinancialPlanner(fundCodes);

        // 将 FinancialPlannerResponse 数据按基金代码分组，后续可通过 fundCode 直接获取对应的数据
        Map<String, FinancialPlannerResponse.FinancialPlannerAssetDiagnosis> fundCodeToDiagnosisMap = financialPlannerResponse.getData().stream()
                .collect(Collectors.toMap(
                        FinancialPlannerResponse.FinancialPlannerAssetDiagnosis::getFundCode,
                        diag -> diag
                ));

        // 解析 assetDiagnosisV2Response 与 financialPlannerResponse 中的数据填充至 UserHoldResponse
        List<UserHoldResponse.FundTypeLevel1> level1List = new ArrayList<>();
        for (AssetDiagnosisV2Response.FundTypeLevel1 level1 : assetDiagnosisV2Response.getFundTypeLevel1List()) {
            UserHoldResponse.FundTypeLevel1 userLevel1 = new UserHoldResponse.FundTypeLevel1();
            userLevel1.setFundTypeName(level1.getFundTypeName());
            userLevel1.setAsset(level1.getAsset());
            userLevel1.setPercentage(level1.getPercentage());
            // 根据基金类型名称映射类型代码
            userLevel1.setType(mapFundTypeNameToType(level1.getFundTypeName()));

            List<UserHoldResponse.FundTypeLevel2> level2List = new ArrayList<>();
            if (level1.getFundTypeLevel2List() != null) {
                for (AssetDiagnosisV2Response.FundTypeLevel2 level2 : level1.getFundTypeLevel2List()) {
                    UserHoldResponse.FundTypeLevel2 userLevel2 = new UserHoldResponse.FundTypeLevel2();
                    userLevel2.setFundTypeName(level2.getFundTypeName());
                    userLevel2.setAsset(level2.getAsset());
                    userLevel2.setPercentage(level2.getPercentage());

                    List<UserHoldResponse.Fund> fundList = new ArrayList<>();
                    if (level2.getFundList() != null) {
                        for (AssetDiagnosisV2Response.Fund fund : level2.getFundList()) {
                            UserHoldResponse.Fund userFund = new UserHoldResponse.Fund();
                            userFund.setFundCode(fund.getFundCode());
                            userFund.setFundName(fund.getFundName());
                            userFund.setRatio(fund.getRatio());
                            userFund.setAsset(fund.getAsset());
                            userFund.setLabels(fund.getLabels());
                            userFund.setHoldProfit(fund.getHoldProfit());
                            userFund.setConstantProfit(fund.getConstantProfit());

                            // 填充 FinancialPlannerResponse 中的数据
                            FinancialPlannerResponse.FinancialPlannerAssetDiagnosis diag = fundCodeToDiagnosisMap.get(fund.getFundCode());
                            if (diag != null) {
                                userFund.setProfitability(diag.getProfitability());
                                userFund.setProfitabilityProportion(diag.getProfitabilityProportion());
                                userFund.setDefensiveAbility(diag.getDefensiveAbility());
                                userFund.setDefensiveAbilityProportion(diag.getDefensiveAbilityProportion());
                                userFund.setRelateThemes(diag.getRelateThemes());
                                userFund.setExcessIndexY(diag.getExcessIndexY());
                                userFund.setTrkError1(diag.getTrkError1());
                                userFund.setProfitY(diag.getProfitY());
                            }

                            fundList.add(userFund);
                        }
                    }
                    userLevel2.setFundList(fundList);
                    level2List.add(userLevel2);
                }
            }
            userLevel1.setFundTypeLevel2List(level2List);
            level1List.add(userLevel1);
        }

        response.setFundTypeLevel1List(level1List);

        return ApiResponse.succeed(response);
    }

    @NotNull
    private Set<String> getCode(AssetDiagnosisV2Response assetDiagnosisV2Response) {
        Set<String> fundCodes = new HashSet<>();
        for (AssetDiagnosisV2Response.FundTypeLevel1 level1 : assetDiagnosisV2Response.getFundTypeLevel1List()) {
            if (level1.getFundTypeLevel2List() != null) {
                for (AssetDiagnosisV2Response.FundTypeLevel2 level2 : level1.getFundTypeLevel2List()) {
                    if (level2.getFundList() != null) {
                        for (AssetDiagnosisV2Response.Fund fund : level2.getFundList()) {
                            fundCodes.add(fund.getFundCode());
                        }
                    }
                }
            }
        }
        return fundCodes;
    }


    /**
     * 获取持仓基金分类
     * request: MineFundTypeRequest
     *
     * @return ApiResponse
     */
    @Override
    public ApiResponse<MineFundTypeResponse, Object> getMineFundType(String customerNo, String subAccountNo) {
        AssetDiagnosisV2Response assetDiagnosisV2Response = assetDiagnosisV2Api.getAssetDiagnosisV2(customerNo, subAccountNo);
        MineFundTypeResponse mineFundTypeResponse = new MineFundTypeResponse();
        List<MineFundTypeResponse.MineFundType> mineFundTypes = new ArrayList<>();
        if (Objects.nonNull(assetDiagnosisV2Response) && Objects.nonNull(assetDiagnosisV2Response.getFundTypeLevel1List())) {
            for (AssetDiagnosisV2Response.FundTypeLevel1 fundTypeLevel1 : assetDiagnosisV2Response.getFundTypeLevel1List()) {
                MineFundTypeResponse.MineFundType mineFundType = new MineFundTypeResponse.MineFundType();
                mineFundType.setFundTypeName(fundTypeLevel1.getFundTypeName());
                mineFundType.setPercentage(fundTypeLevel1.getPercentage());
                // 根据基金类型名称映射类型代码
                mineFundType.setType(mapFundTypeNameToType(fundTypeLevel1.getFundTypeName()));
                mineFundTypes.add(mineFundType);
            }
        }

        mineFundTypeResponse.setMineFundTypes(mineFundTypes);
        return ApiResponse.succeed(mineFundTypeResponse);
    }


    /**
     * 获取收益表现
     * request: IncomeRequest
     *
     * @return ApiResponse
     */
    @Override
    public ApiResponse<IncomeResponse, Object> getIncome(String customerNo, String subAccountNo, String indexCode, String range) {
        IncomeResponse incomeResponse = new IncomeResponse();

        List<IncomeResponse.myEarn> myEarnList = new ArrayList<>();

        // 如果 subAccountNo 不为空，通过调用 getIntervalProfitDetail 获取 MyEarnList
        if (StringUtils.isNotEmpty(subAccountNo)) {
            IntervalProfitDetailResponse intervalProfitDetailResponse = intervalProfitDetailApi.getIntervalProfitDetail(customerNo, subAccountNo);
            if (intervalProfitDetailResponse != null && intervalProfitDetailResponse.getGraphSpotList() != null
                    && !intervalProfitDetailResponse.getGraphSpotList().isEmpty()) {
                // 按日期排序
                intervalProfitDetailResponse.getGraphSpotList().sort(Comparator.comparing(IntervalProfitDetailResponse.GraphSpot::getNavDate));

                // 计算一年前的日期
                LocalDate oneYearAgo = LocalDate.now().minusYears(1);
                DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                for (IntervalProfitDetailResponse.GraphSpot graphSpot : intervalProfitDetailResponse.getGraphSpotList()) {
                    if (graphSpot.getAccountNav() != null && graphSpot.getAccountNav().getNavDate() != null) {
                        try {
                            // 解析日期格式 "2024-06-12 00:00:00"
                            LocalDate navDate = LocalDate.parse(graphSpot.getAccountNav().getNavDate(), inputFormatter);
                            // 只保留近一年的数据
                            if (navDate.isAfter(oneYearAgo) || navDate.isEqual(oneYearAgo)) {
                                IncomeResponse.myEarn myEarn = new IncomeResponse.myEarn();
                                // 转换日期格式为 "2024-06-01"
                                myEarn.setDate(navDate.format(outputFormatter));
                                // 处理 totalRate 可能为 null 的情况
                                myEarn.setYield(String.valueOf(graphSpot.getAccountNav().getTotalRate()));
                                myEarnList.add(myEarn);
                            }
                        } catch (DateTimeParseException e) {
                            // 日期解析失败，跳过该记录
                        }
                    }
                }
                // 总收益率取最后一天的收益率
                if (!myEarnList.isEmpty()) {
                    IncomeResponse.myEarn lastMyEarn = myEarnList.get(myEarnList.size() - 1);
                    incomeResponse.setProfitRate(lastMyEarn.getYield());
                } else {
                    incomeResponse.setProfitRate("0");
                }
            } else {
                incomeResponse.setProfitRate("0");
            }
        } else {
            // 原有逻辑：调用 getAssetDiagnosisV2 获取每日收益
            AssetDiagnosisV2Response assetDiagnosisV2Response = assetDiagnosisV2Api.getAssetDiagnosisV2(customerNo, subAccountNo);
            if (assetDiagnosisV2Response != null && assetDiagnosisV2Response.getDailyProfits() != null
                    && !assetDiagnosisV2Response.getDailyProfits().isEmpty()) {
                // 按日期排序
                assetDiagnosisV2Response.getDailyProfits().sort(Comparator.comparing(AssetDiagnosisV2Response.DailyProfit::getDate));

                // 计算一年前的日期
                LocalDate oneYearAgo = LocalDate.now().minusYears(1);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                for (AssetDiagnosisV2Response.DailyProfit dailyProfit : assetDiagnosisV2Response.getDailyProfits()) {
                    if (dailyProfit.getDate() != null) {
                        try {
                            // 解析日期格式 "2024-06-01"
                            LocalDate profitDate = LocalDate.parse(dailyProfit.getDate(), formatter);
                            // 只保留近一年的数据
                            if (profitDate.isAfter(oneYearAgo) || profitDate.isEqual(oneYearAgo)) {
                                IncomeResponse.myEarn myEarn = new IncomeResponse.myEarn();
                                myEarn.setDate(dailyProfit.getDate());
                                myEarn.setYield(dailyProfit.getProfitRate());
                                myEarnList.add(myEarn);
                            }
                        } catch (DateTimeParseException e) {
                            // 日期解析失败，跳过该记录
                        }
                    }
                }
                // 总收益率取最后一天的收益率
                if (!myEarnList.isEmpty()) {
                    IncomeResponse.myEarn lastMyEarn = myEarnList.get(myEarnList.size() - 1);
                    incomeResponse.setProfitRate(lastMyEarn.getYield());
                } else {
                    incomeResponse.setProfitRate("0");
                }
            } else {
                incomeResponse.setProfitRate("0");
            }
        }
        incomeResponse.setMyEarnList(myEarnList);

        // 调用 getFundIndex 获取基准收益
        FundIndexResponse fundIndexResponse = fundIndexApi.getFundIndex(indexCode, range);
        List<IncomeResponse.baseEarn> baseEarnList = new ArrayList<>();
        if (fundIndexResponse != null && fundIndexResponse.getData() != null
                && !fundIndexResponse.getData().isEmpty()) {
            // 按日期排序
            fundIndexResponse.getData().sort(Comparator.comparing(FundIndexResponse.FundIndexAccV2Response::getPDATE));
            for (FundIndexResponse.FundIndexAccV2Response indexResponse : fundIndexResponse.getData()) {
                IncomeResponse.baseEarn baseEarn = new IncomeResponse.baseEarn();
                baseEarn.setDate(indexResponse.getPDATE());
                baseEarn.setYield(indexResponse.getINDEXYIELD());
                baseEarnList.add(baseEarn);
            }
            // 总收益率取最后一天的收益率
            IncomeResponse.baseEarn lastBaseEarn = baseEarnList.get(baseEarnList.size() - 1);
            incomeResponse.setBaseEarnings(lastBaseEarn.getYield());
        }
        incomeResponse.setBaseEarnList(baseEarnList);

        return ApiResponse.succeed(incomeResponse);
    }

    @Override
    public ApiResponse<HoldingPenetrationResponse, Object> getHoldingPenetration(String customerNo, String subAccountNo) {
        HoldingPenetrationResponse response = new HoldingPenetrationResponse();
        // 调用 getAssetDiagnosisV2 方法获取资产诊断信息
        AssetDiagnosisV2Response assetDiagnosisV2Response = assetDiagnosisV2Api.getAssetDiagnosisV2(customerNo, subAccountNo);

        if (assetDiagnosisV2Response != null && assetDiagnosisV2Response.getPieAssets() != null) {
            List<HoldingPenetrationResponse.HoldingPenetration> holdingPenetrations = getHoldingPenetrations(assetDiagnosisV2Response);
            // 将填充好的数据设置到响应对象中
            response.setHoldingPenetrations(holdingPenetrations);
        }

        return ApiResponse.succeed(response);
    }

    @NotNull
    private static List<HoldingPenetrationResponse.HoldingPenetration> getHoldingPenetrations(AssetDiagnosisV2Response assetDiagnosisV2Response) {
        List<HoldingPenetrationResponse.HoldingPenetration> holdingPenetrations = new ArrayList<>();
        // 遍历饼图资产分布列表
        for (AssetDiagnosisV2Response.PieAsset pieAsset : assetDiagnosisV2Response.getPieAssets()) {
            HoldingPenetrationResponse.HoldingPenetration holdingPenetration = new HoldingPenetrationResponse.HoldingPenetration();
            // 填充占比信息
            holdingPenetration.setProportion(pieAsset.getProportion());
            // 填充类型名称信息
            holdingPenetration.setTypeName(pieAsset.getTypeName());

            // 根据类型名称映射类型
            String typeName = pieAsset.getTypeName();
            String type = mapTypeNameToType(typeName);
            holdingPenetration.setType(type);

            holdingPenetrations.add(holdingPenetration);
        }
        return holdingPenetrations;
    }

    /**
     * 根据类型名称映射到类型代码
     *
     * @param typeName 类型名称
     * @return 类型代码
     */
    private static String mapTypeNameToType(String typeName) {
        if (typeName == null) {
            return "";
        }

        // 根据提供的映射关系进行映射
        switch (typeName) {
            case "股票成分":
                return "stock";
            case "债券成分":
                return "bond";
            case "货币成分":
                return "currency";
            case "商品及其他":
                return "commodity_other";
            default:
                // 如果没有匹配的映射，可以返回原始名称或空字符串
                return "";
        }
    }

    /**
     * 根据基金类型名称映射到类型代码
     *
     * @param fundTypeName 基金类型名称
     * @return 类型代码
     */
    private static String mapFundTypeNameToType(String fundTypeName) {
        if (fundTypeName == null) {
            return "";
        }

        // 根据基金分类映射关系进行映射
        switch (fundTypeName) {
            case "股票类":
                return "stock";
            case "债券类":
                return "bond";
            case "货币类":
                return "currency";
            case "商品及其他":
                return "commodity_other";
            default:
                // 如果没有匹配的映射，可以返回原始名称或空字符串
                return "";
        }
    }

    @Override
    public ApiResponse<PortfolioStyleResponse, Object> getPortfolioStyle(String customerNo, String subAccountNo) {
        PortfolioStyleResponse response = new PortfolioStyleResponse();
        // 调用 getAssetDiagnosisV2 方法获取资产诊断信息
        AssetDiagnosisV2Response assetDiagnosisV2Response = assetDiagnosisV2Api.getAssetDiagnosisV2(customerNo, subAccountNo);

        if (assetDiagnosisV2Response != null && assetDiagnosisV2Response.getInvestStyleRatios() != null) {
            List<PortfolioStyleResponse.PortfolioStyle> portfolioStyles = getPortfolioStyles(assetDiagnosisV2Response);
            // 将填充好的数据设置到响应对象中
            response.setPortfolioStyles(portfolioStyles);
            List<InvestStyleResult> investStyleResults = new ArrayList<>();

            // 定义 scaleType 和 style 列表 大盘 中盘 小盘   价值 成长 平衡
            List<String> scaleTypes = Arrays.asList("01", "02", "03");
            List<String> styles = Arrays.asList("01", "02", "03");

            // 创建线程池
            ExecutorService executorService = Executors.newFixedThreadPool(scaleTypes.size() * styles.size());
            List<Future<InvestStyleResult>> futures = new ArrayList<>();
            // 提交任务到线程池
            for (String scaleType : scaleTypes) {
                for (String style : styles) {
                    futures.add(executorService.submit(() -> {
                        // 获取股票持仓风格的成分基金
                        InvestStyleResponse investStyleResponse = investStyleApi.getInvestStyle(customerNo, subAccountNo, scaleType, style);
                        return new InvestStyleResult(scaleType, style, investStyleResponse);
                    }));
                }
            }

            // 收集结果
            for (Future<InvestStyleResult> future : futures) {
                try {
                    InvestStyleResult investStyleResult = future.get();
                    InvestStyleResponse investStyleResponse = investStyleResult.getInvestStyleResponse();
                    if (investStyleResponse != null && investStyleResponse.getData() != null) {
                        investStyleResults.add(investStyleResult);
                    }

                } catch (InterruptedException | ExecutionException e) {
                    Thread.currentThread().interrupt();
                    // 可以根据实际情况添加日志记录
                    e.printStackTrace();
                }
            }

            // 关闭线程池
            executorService.shutdown();

            // 填充数据到 PortfolioStyleResponse
            response.setInvestStyleResults(investStyleResults);


        }

        return ApiResponse.succeed(response);
    }

    @NotNull
    private static List<PortfolioStyleResponse.PortfolioStyle> getPortfolioStyles(AssetDiagnosisV2Response assetDiagnosisV2Response) {
        List<PortfolioStyleResponse.PortfolioStyle> portfolioStyles = new ArrayList<>();
        // 遍历投资风格比例列表
        for (AssetDiagnosisV2Response.InvestStyleRatio investStyleRatio : assetDiagnosisV2Response.getInvestStyleRatios()) {
            PortfolioStyleResponse.PortfolioStyle portfolioStyle = new PortfolioStyleResponse.PortfolioStyle();
            // 填充规模类型信息
            portfolioStyle.setScaleType(investStyleRatio.getScaleType());
            // 填充风格类型信息
            portfolioStyle.setStyle(investStyleRatio.getStyle());
            // 填充该风格的占比信息
            portfolioStyle.setRatio(investStyleRatio.getRatio());
            portfolioStyles.add(portfolioStyle);
        }
        return portfolioStyles;
    }

    @Override
    public ApiResponse<List<IndustryAnalysisResponse>, Object> getPortfolioIndustry(String customerNo, String subAccountNo) {
        // 调用 getAssetDiagnosisV2 方法获取资产诊断信息
        AssetDiagnosisV2Response assetDiagnosisV2Response = assetDiagnosisV2Api.getAssetDiagnosisV2(customerNo, subAccountNo);
        List<IndustryAnalysisResponse> industryAnalysisResponses = new ArrayList<>();

        if (assetDiagnosisV2Response != null && assetDiagnosisV2Response.getIndustryScoreRatios() != null) {
            List<ListIndustryResponse.Industry> industryList = new ArrayList<>();
            // 遍历行业评分及占比列表
            for (AssetDiagnosisV2Response.IndustryScoreRatio industryScoreRatio : assetDiagnosisV2Response.getIndustryScoreRatios()) {
                ListIndustryResponse.Industry industry = getIndustry(industryScoreRatio);
                industryList.add(industry);
            }

            // 筛选出占比大于35%的行业
            List<ListIndustryResponse.Industry> filteredIndustries = industryList.stream()
                    .filter(industry -> {
                        try {
                            double ratio = Double.parseDouble(industry.getRatio());
                            return ratio > 0.35;
                        } catch (NumberFormatException e) {
                            return false;
                        }
                    })
                    .collect(Collectors.toList());

            if (filteredIndustries.isEmpty()) {
                return ApiResponse.succeed(industryAnalysisResponses);
            }

            // 创建线程池
            ExecutorService executorService = Executors.newFixedThreadPool(Math.min(filteredIndustries.size(), 10));
            List<Future<IndustryAnalysisResponse>> futures = new ArrayList<>();

            // 提交任务到线程池
            for (ListIndustryResponse.Industry industry : filteredIndustries) {
                String industryCode = industry.getIndustryCode();
                futures.add(executorService.submit(() ->
                        // 获取股票持仓行业AI解读信息
                        industryAnalysisApi.getIndustryAnalysis(industryCode)
                ));
            }

            // 收集结果
            for (Future<IndustryAnalysisResponse> future : futures) {
                try {
                    IndustryAnalysisResponse response = future.get();
                    if (response != null) {
                        industryAnalysisResponses.add(response);
                    }
                } catch (InterruptedException | ExecutionException e) {
                    Thread.currentThread().interrupt();
                    e.printStackTrace();
                }
            }

            // 关闭线程池
            executorService.shutdown();
        }

        return ApiResponse.succeed(industryAnalysisResponses);
    }

    @Override
    public ApiResponse<ListIndustryResponse, Object> getListIndustry(String customerNo, String subAccountNo) {
        ListIndustryResponse response = new ListIndustryResponse();
        // 调用 getAssetDiagnosisV2 方法获取资产诊断信息
        AssetDiagnosisV2Response assetDiagnosisV2Response = assetDiagnosisV2Api.getAssetDiagnosisV2(customerNo, subAccountNo);

        if (assetDiagnosisV2Response != null && assetDiagnosisV2Response.getIndustryScoreRatios() != null) {
            List<ListIndustryResponse.Industry> industryList = new ArrayList<>();
            // 遍历行业评分及占比列表
            for (AssetDiagnosisV2Response.IndustryScoreRatio industryScoreRatio : assetDiagnosisV2Response.getIndustryScoreRatios()) {
                ListIndustryResponse.Industry industry = getIndustry(industryScoreRatio);
                industryList.add(industry);
            }
            // 将填充好的数据设置到响应对象中
            response.setIndustrys(industryList);

            List<ListIndustryResponse.Industry> industries = response.getIndustrys();
            if (industries == null || industries.isEmpty()) {
                return ApiResponse.succeed(response);
            }

            // 创建线程池，线程数量根据实际情况调整
            ExecutorService executorService = Executors.newFixedThreadPool(Math.min(industries.size(), 10));
            List<Future<IndustrySourcesResponse>> futures = new ArrayList<>();

            // 提交任务到线程池
            for (ListIndustryResponse.Industry industry : industries) {
                String industryCode = industry.getIndustryCode();
                // 获取行业列表信息
                futures.add(executorService.submit(() -> industrySourcesApi.getIndustrySources(customerNo, subAccountNo, industryCode)
                ));
            }
            List<IndustrySourcesResponse> investStyles = new ArrayList<>();
            // 收集结果
            for (Future<IndustrySourcesResponse> future : futures) {
                try {
                    IndustrySourcesResponse industrySourcesResponse = future.get();
                    if (industrySourcesResponse != null && industrySourcesResponse.getIndustrySources() != null) {
                        investStyles.add(industrySourcesResponse);
                    }

                } catch (InterruptedException | ExecutionException e) {
                    Thread.currentThread().interrupt();
                    // 可以根据实际情况添加日志记录
                    e.printStackTrace();
                }
            }

            // 关闭线程池
            executorService.shutdown();
            response.setIndustrySources(investStyles);
        }

        return ApiResponse.succeed(response);
    }

    @NotNull
    private static ListIndustryResponse.Industry getIndustry(AssetDiagnosisV2Response.IndustryScoreRatio industryScoreRatio) {
        ListIndustryResponse.Industry industry = new ListIndustryResponse.Industry();
        // 填充行业代码信息
        industry.setIndustryCode(industryScoreRatio.getIndustryCode());
        // 填充行业名称信息
        industry.setIndustryName(industryScoreRatio.getIndustryName());
        // 填充行业占比信息
        industry.setRatio(industryScoreRatio.getRatio());
        // 填充机会评分信息
        industry.setChanceScore(industryScoreRatio.getChanceScore());
        // 填充风险评分信息
        industry.setRiskScore(industryScoreRatio.getRiskScore());
        return industry;
    }


    @Override
    public ApiResponse<BondPenetrationResponse, Object> getBondPenetration(String customerNo, String subAccountNo) {
        BondPenetrationResponse response = new BondPenetrationResponse();

        // 获取资产诊断信息
        AssetDiagnosisV2Response assetDiagnosisV2Response = assetDiagnosisV2Api.getAssetDiagnosisV2(customerNo, subAccountNo);

        // 计算债券类型的加权占比
        Map<String, BigDecimal> bondTypeWeightedRatios = calculateBondTypeWeightedRatios(assetDiagnosisV2Response);
        // 归一化处理并创建债券穿透数据
        List<BondPenetrationResponse.BondPenetration> bondPenetrations = createNormalizedBondPenetrations(bondTypeWeightedRatios);
        response.setBondPenetrations(bondPenetrations);

        if (bondPenetrations.isEmpty()) {
            return ApiResponse.succeed(response);
        }

        // 获取债券来源数据
        List<BoundSourcesResponse> boundSources = getBoundSourcesData(customerNo, subAccountNo, bondTypeWeightedRatios);
        response.setBoundSources(boundSources);

        return ApiResponse.succeed(response);
    }

    /**
     * 计算债券类型的加权占比
     * @param assetDiagnosisV2Response 资产诊断响应
     * @return 债券类型代码到加权占比的映射
     */
    private Map<String, BigDecimal> calculateBondTypeWeightedRatios(AssetDiagnosisV2Response assetDiagnosisV2Response) {
        // 收集债券类的 fundCode 和对应的 ratio
        Map<String, BigDecimal> bondFundCodeToRatio = getStringBigDecimalMap(assetDiagnosisV2Response);
        Map<String, BigDecimal> bondTypeWeightedRatios = new HashMap<>();

        if (bondFundCodeToRatio.isEmpty()) {
            return bondTypeWeightedRatios;
        }

        // 计算债券类基金的总占比
        BigDecimal totalBondFundRatio = bondFundCodeToRatio.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 并发调用 getFundIndex 获取基金债券数据
        Map<String, FundBondResponse> fundBondResponses = getFundBondResponsesConcurrently(bondFundCodeToRatio.keySet());

        // 处理获取到的债券数据
        for (Map.Entry<String, BigDecimal> entry : bondFundCodeToRatio.entrySet()) {
            String fundCode = entry.getKey();
            BigDecimal fundRatio = entry.getValue();

            FundBondResponse fundBondResponse = fundBondResponses.get(fundCode);
            if (fundBondResponse != null && fundBondResponse.getData() != null) {
                processFundBondData(fundBondResponse, fundRatio, totalBondFundRatio, bondTypeWeightedRatios);
            }
        }

        return bondTypeWeightedRatios;
    }

    /**
     * 并发获取基金债券响应数据
     * @param fundCodes 基金代码集合
     * @return 基金代码到基金债券响应的映射
     */
    private Map<String, FundBondResponse> getFundBondResponsesConcurrently(Set<String> fundCodes) {
        Map<String, FundBondResponse> fundBondResponses = new ConcurrentHashMap<>();

        // 创建线程池，线程数量根据基金数量调整，最多10个线程
        ExecutorService executorService = Executors.newFixedThreadPool(Math.min(fundCodes.size(), 10));
        List<Future<Void>> futures = new ArrayList<>();

        // 提交任务到线程池
        for (String fundCode : fundCodes) {
            futures.add(executorService.submit(() -> {
                try {
                    FundBondResponse fundBondResponse = fundBondApi.getFundIndex(fundCode);
                    if (fundBondResponse != null) {
                        fundBondResponses.put(fundCode, fundBondResponse);
                    }
                } catch (Exception e) {
                    // 记录异常但不影响其他基金的处理
                    // 可以根据实际情况添加日志记录
                }
                return null;
            }));
        }

        // 等待所有任务完成
        for (Future<Void> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt();
                // 可以根据实际情况添加日志记录
            }
        }

        // 关闭线程池
        executorService.shutdown();
        return fundBondResponses;
    }

    /**
     * 处理单个基金的债券数据
     * @param fundBondResponse 基金债券响应
     * @param fundRatio 基金占比
     * @param totalBondFundRatio 债券类基金总占比
     * @param bondTypeWeightedRatios 债券类型加权占比映射
     */
    private void processFundBondData(FundBondResponse fundBondResponse, BigDecimal fundRatio,
                                   BigDecimal totalBondFundRatio, Map<String, BigDecimal> bondTypeWeightedRatios) {
        for (FundBondResponse.FundBondInvestDistriResponse investDistriResponse : fundBondResponse.getData()) {
            String bondTypeCode = investDistriResponse.getBONDTYPENEW();
            try {
                BigDecimal pctNv = new BigDecimal(investDistriResponse.getPCTNV());
                // 计算该基金在债券类基金中的权重
                BigDecimal fundWeightInBondCategory = totalBondFundRatio.compareTo(BigDecimal.ZERO) > 0
                    ? fundRatio.divide(totalBondFundRatio, 10, RoundingMode.HALF_UP)
                    : BigDecimal.ZERO;
                // 计算该债券类型的占比：基金在债券类中的权重 * 债券在基金中的占比
                BigDecimal weightedRatio = fundWeightInBondCategory.multiply(pctNv);

                bondTypeWeightedRatios.merge(bondTypeCode, weightedRatio, BigDecimal::add);
            } catch (NumberFormatException e) {
                // 如果解析失败，跳过该记录
                continue;
            }
        }
    }

    @NotNull
    private static Map<String, BigDecimal> getStringBigDecimalMap(AssetDiagnosisV2Response assetDiagnosisV2Response) {
        Map<String, BigDecimal> bondFundCodeToRatio = new HashMap<>();
        if (assetDiagnosisV2Response != null && assetDiagnosisV2Response.getFundTypeLevel1List() != null) {
            for (AssetDiagnosisV2Response.FundTypeLevel1 level1 : assetDiagnosisV2Response.getFundTypeLevel1List()) {
                if ("债券类".equals(level1.getFundTypeName()) && level1.getFundTypeLevel2List() != null) {
                    for (AssetDiagnosisV2Response.FundTypeLevel2 level2 : level1.getFundTypeLevel2List()) {
                        if (level2.getFundList() != null) {
                            for (AssetDiagnosisV2Response.Fund fund : level2.getFundList()) {
                                try {
                                    BigDecimal ratio = new BigDecimal(fund.getRatio());
                                    bondFundCodeToRatio.put(fund.getFundCode(), ratio);
                                } catch (NumberFormatException e) {
                                    // 如果解析失败，跳过该基金
                                    continue;
                                }
                            }
                        }
                    }
                }
            }
        }
        return bondFundCodeToRatio;
    }

    /**
     * 创建归一化的债券穿透数据
     * @param bondTypeWeightedRatios 债券类型加权占比映射
     * @return 归一化后的债券穿透数据列表
     */
    private List<BondPenetrationResponse.BondPenetration> createNormalizedBondPenetrations(
            Map<String, BigDecimal> bondTypeWeightedRatios) {

        List<BondPenetrationResponse.BondPenetration> bondPenetrations = new ArrayList<>();
        BigDecimal totalRatio = bondTypeWeightedRatios.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalRatio.compareTo(BigDecimal.ZERO) > 0) {
            // 找出占比最大的债券类型
            String maxBondTypeCode = findMaxBondTypeCode(bondTypeWeightedRatios);

            // 先计算其他类型的归一化比例
            BigDecimal otherTypesSum = calculateOtherTypesRatios(bondTypeWeightedRatios, totalRatio,
                                                               maxBondTypeCode, bondPenetrations);

            // 处理占比最大的类型
            if (maxBondTypeCode != null) {
                addMaxBondTypeData(maxBondTypeCode, otherTypesSum, bondPenetrations);
            }
        } else {
            // 如果总比例为0，所有类型都设为0
            addZeroRatioBondTypes(bondTypeWeightedRatios, bondPenetrations);
        }

        return bondPenetrations;
    }

    /**
     * 找出占比最大的债券类型
     * @param bondTypeWeightedRatios 债券类型加权占比映射
     * @return 占比最大的债券类型代码
     */
    private String findMaxBondTypeCode(Map<String, BigDecimal> bondTypeWeightedRatios) {
        String maxBondTypeCode = null;
        BigDecimal maxWeightedRatio = BigDecimal.ZERO;

        for (Map.Entry<String, BigDecimal> entry : bondTypeWeightedRatios.entrySet()) {
            if (entry.getValue().compareTo(maxWeightedRatio) > 0) {
                maxWeightedRatio = entry.getValue();
                maxBondTypeCode = entry.getKey();
            }
        }

        return maxBondTypeCode;
    }

    /**
     * 计算其他类型的归一化比例
     * @param bondTypeWeightedRatios 债券类型加权占比映射
     * @param totalRatio 总占比
     * @param maxBondTypeCode 占比最大的债券类型代码
     * @param bondPenetrations 债券穿透数据列表
     * @return 其他类型的总占比
     */
    private BigDecimal calculateOtherTypesRatios(Map<String, BigDecimal> bondTypeWeightedRatios,
                                               BigDecimal totalRatio, String maxBondTypeCode,
                                               List<BondPenetrationResponse.BondPenetration> bondPenetrations) {
        BigDecimal otherTypesSum = BigDecimal.ZERO;

        for (Map.Entry<String, BigDecimal> entry : bondTypeWeightedRatios.entrySet()) {
            String bondTypeCode = entry.getKey();
            BigDecimal weightedRatio = entry.getValue();

            if (!bondTypeCode.equals(maxBondTypeCode)) {
                // 其他类型正常计算归一化比例
                BigDecimal normalizedRatio = weightedRatio.divide(totalRatio, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                BigDecimal finalRatio = normalizedRatio.setScale(2, RoundingMode.HALF_UP);

                BondPenetrationResponse.BondPenetration bondPenetration = createBondPenetration(
                        bondTypeCode, finalRatio.toString());
                bondPenetrations.add(bondPenetration);
                otherTypesSum = otherTypesSum.add(finalRatio);
            }
        }

        return otherTypesSum;
    }

    /**
     * 添加占比最大的债券类型数据
     * @param maxBondTypeCode 占比最大的债券类型代码
     * @param otherTypesSum 其他类型的总占比
     * @param bondPenetrations 债券穿透数据列表
     */
    private void addMaxBondTypeData(String maxBondTypeCode, BigDecimal otherTypesSum,
                                  List<BondPenetrationResponse.BondPenetration> bondPenetrations) {
        BigDecimal maxTypeRatio = new BigDecimal("100").subtract(otherTypesSum);
        // 确保不为负数
        if (maxTypeRatio.compareTo(BigDecimal.ZERO) < 0) {
            maxTypeRatio = BigDecimal.ZERO;
        }

        BondPenetrationResponse.BondPenetration maxBondPenetration = createBondPenetration(
                maxBondTypeCode, maxTypeRatio.setScale(2, RoundingMode.HALF_UP).toString());
        bondPenetrations.add(maxBondPenetration);
    }

    /**
     * 添加零占比的债券类型数据
     * @param bondTypeWeightedRatios 债券类型加权占比映射
     * @param bondPenetrations 债券穿透数据列表
     */
    private void addZeroRatioBondTypes(Map<String, BigDecimal> bondTypeWeightedRatios,
                                     List<BondPenetrationResponse.BondPenetration> bondPenetrations) {
        for (Map.Entry<String, BigDecimal> entry : bondTypeWeightedRatios.entrySet()) {
            BondPenetrationResponse.BondPenetration bondPenetration = createBondPenetration(
                    entry.getKey(), "0.00");
            bondPenetrations.add(bondPenetration);
        }
    }

    /**
     * 创建债券穿透数据对象
     * @param bondTypeCode 债券类型代码
     * @param weightedRatio 加权占比
     * @return 债券穿透数据对象
     */
    private BondPenetrationResponse.BondPenetration createBondPenetration(String bondTypeCode, String weightedRatio) {
        BondPenetrationResponse.BondPenetration bondPenetration = new BondPenetrationResponse.BondPenetration();
        bondPenetration.setBondTypeCode(bondTypeCode);
        bondPenetration.setBondTypeName(mapBondTypeCodeToName(bondTypeCode));
        bondPenetration.setWeightedRatio(weightedRatio);
        return bondPenetration;
    }

    /**
     * 获取债券来源数据
     * @param customerNo 客户号
     * @param subAccountNo 子账户号
     * @param bondTypeWeightedRatios 债券类型加权占比映射
     * @return 债券来源数据列表
     */
    private List<BoundSourcesResponse> getBoundSourcesData(String customerNo, String subAccountNo,
                                                         Map<String, BigDecimal> bondTypeWeightedRatios) {
        // 创建线程池，线程数量根据实际情况调整
        ExecutorService executorService = Executors.newFixedThreadPool(Math.min(bondTypeWeightedRatios.size(), 10));
        List<Future<BoundSourcesResponse>> futures = new ArrayList<>();

        // 提交任务到线程池
        for (String bondTypeCode : bondTypeWeightedRatios.keySet()) {
            futures.add(executorService.submit(() ->
                    // 获取指定债券占比的成分基金
                    boundSourcesApi.getBoundSources(customerNo, subAccountNo, bondTypeCode)
            ));
        }

        List<BoundSourcesResponse> boundSources = new ArrayList<>();
        // 收集结果
        for (Future<BoundSourcesResponse> future : futures) {
            try {
                BoundSourcesResponse boundSourcesResponse = future.get();
                if (boundSourcesResponse != null) {
                    boundSources.add(boundSourcesResponse);
                }
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt();
            }
        }

        // 关闭线程池
        executorService.shutdown();
        return boundSources;
    }

    /**
     * 将债券类型代码映射为债券类型名称
     *
     * @param bondTypeCode 债券类型代码
     * @return 债券类型名称
     */
    private static String mapBondTypeCodeToName(String bondTypeCode) {
        if (bondTypeCode == null) {
            return "未知";
        }

        switch (bondTypeCode) {
            case "1":
                return "信用债";
            case "2":
                return "利率债";
            case "3":
                return "可转债";
            case "4":
                return "其他";
            default:
                return "未知";
        }
    }
}
