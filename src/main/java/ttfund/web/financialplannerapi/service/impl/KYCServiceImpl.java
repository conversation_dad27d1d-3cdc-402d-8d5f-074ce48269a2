package ttfund.web.financialplannerapi.service.impl;

import com.ttfund.web.core.model.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ttfund.web.financialplannerapi.config.AppConfig;
import ttfund.web.financialplannerapi.data.KYCMysql;
import ttfund.web.financialplannerapi.data.impl.ContractMysqlImpl;
import ttfund.web.financialplannerapi.data.impl.InstitutionConfigMysqlImpl;
import ttfund.web.financialplannerapi.model.po.mysqlpo.ContractRecordPO;
import ttfund.web.financialplannerapi.model.request.KYCRecordRequest;
import ttfund.web.financialplannerapi.service.IKYCService;

import javax.annotation.Resource;
import java.util.List;

@Service
public class KYCServiceImpl implements IKYCService {

    @Autowired
    private KYCMysql kycMysql;

    @Autowired
    ContractMysqlImpl contractMysql;

    @Autowired
    private AppConfig appConfig;

    @Resource
    private InstitutionConfigMysqlImpl institutionConfigMysql;

    @Override
    public ApiResponse<Boolean, Object> record(KYCRecordRequest request) {
        ApiResponse<Boolean, Object> result = new ApiResponse<>();
        //验证参数
        List<String> institutionIds = institutionConfigMysql.getAllInstitutionId();
        institutionIds.addAll(appConfig.agreement.keySet());
        if (!request.cheack() || !institutionIds.contains(request.getInstitutionId())) {
            result.setFirstError("参数错误");
            result.setData(false);
            return result;
        }
        //校验用户是否签约
        List<ContractRecordPO> contractRecordPOS = contractMysql.selectByInsAndPass(request.getPassportid(), request.getInstitutionId());
        if (contractRecordPOS == null || contractRecordPOS.isEmpty()) {
            result.setFirstError("未签绑定，不留痕");
            result.setData(false);
            return result;
        }
        result.setData(kycMysql.insertOne(request));
        return result;
    }
}
