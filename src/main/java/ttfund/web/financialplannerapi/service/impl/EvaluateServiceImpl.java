package ttfund.web.financialplannerapi.service.impl;

import com.ttfund.web.base.helper.DateHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import ttfund.web.financialplannerapi.constant.ErrorCodeEnum;
import ttfund.web.financialplannerapi.data.impl.EvaluateMysqlImpl;
import ttfund.web.financialplannerapi.exception.BusinessException;
import ttfund.web.financialplannerapi.model.po.mysqlpo.ServicePO;
import ttfund.web.financialplannerapi.model.request.EvaluateAddRequest;
import ttfund.web.financialplannerapi.model.request.FeedBackAddRequest;
import ttfund.web.financialplannerapi.service.IEvaluateService;

import javax.annotation.Resource;
import java.util.Date;


@Service
public class EvaluateServiceImpl implements IEvaluateService {

    private static final Logger logger = LoggerFactory.getLogger(EvaluateServiceImpl.class);

    @Resource
    EvaluateMysqlImpl evaluateMysqlImpl;

    @Override
    public Boolean userEvaluate(EvaluateAddRequest request) throws BusinessException {
        boolean result = false;
        if (request==null|| StringUtils.isEmpty(request.getPlannerid()) || StringUtils.isEmpty(request.getPassportid()) ||
                StringUtils.isEmpty(request.getInstitutionid()) || StringUtils.isEmpty(request.getVersion()) || StringUtils.isEmpty(request.getPlat()) ||
                request.getAttitudescore()==0 || request.getProfessionscore()==0 || request.getCompositescore()==0){
            throw new BusinessException(ErrorCodeEnum.LackParameter.getMsg(),ErrorCodeEnum.LackParameter.getCode());
        }
        String plannerid = request.getPlannerid();
        String userPassportid = request.getPassportid();
        String serviceId = request.getServiceId();
        String appointmentId = null;
        if (StringUtils.isBlank(serviceId)) {
            //在线聊天评价
            Date dayAgo = DateHelper.localDateTime2Date(DateHelper.getNow().minusDays(1));
            boolean isEvaluated = evaluateMysqlImpl.ifEvaluateMoreThan24h(plannerid,userPassportid,dayAgo);
            if (isEvaluated) {
                logger.info("24h内客户{}评价过理财师{}",userPassportid,plannerid);
                throw new BusinessException(ErrorCodeEnum.Evaluated.getMsg(),ErrorCodeEnum.Evaluated.getCode());
            }
            //插入数据
            result = evaluateMysqlImpl.insertEvaluation(request);

        } else {
            //外呼评价
            ServicePO serviceRec = evaluateMysqlImpl.queryServiceInfo(serviceId);
            if (serviceRec == null) {
                // 必须有服务记录才能评价
                throw new BusinessException(ErrorCodeEnum.CANNOT_EVALUATE.getMsg(), ErrorCodeEnum.CANNOT_EVALUATE.getCode());
            }
            appointmentId = serviceRec.getAppointmentId();
            if (StringUtils.isNotEmpty(appointmentId)) {
                result = evaluateMysqlImpl.insertEvaluationWithAppointmentId(request, appointmentId);
            } else {
                result = evaluateMysqlImpl.insertEvaluationWithServiceId(request);
            }
        }
        return result;
    }

    @Override
    public Boolean userFeedBack(FeedBackAddRequest request) throws BusinessException {
        boolean result = false;
        if (request==null|| StringUtils.isEmpty(request.getPlannerid()) || StringUtils.isEmpty(request.getPassportid()) ||
                StringUtils.isEmpty(request.getInstitutionid()) || StringUtils.isEmpty(request.getVersion()) || StringUtils.isEmpty(request.getPlat()) ||
                StringUtils.isEmpty(request.getOpinion())){
            throw new BusinessException(ErrorCodeEnum.LackParameter.getMsg(),ErrorCodeEnum.LackParameter.getCode());
        }

        //插入数据
        result = evaluateMysqlImpl.insertFeedBack(request);
        return result;

    }

}
