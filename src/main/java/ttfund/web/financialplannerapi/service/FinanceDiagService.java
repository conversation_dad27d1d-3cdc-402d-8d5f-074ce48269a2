package ttfund.web.financialplannerapi.service;

import com.ttfund.web.core.model.ApiResponse;
import ttfund.web.financialplannerapi.model.api.IndustryAnalysisResponse;
import ttfund.web.financialplannerapi.model.reponse.*;
import ttfund.web.financialplannerapi.model.request.*;

import java.util.List;

public interface FinanceDiagService {
    ApiResponse<UserHoldResponse, Object> getUserHold(String customerNo, String subAccountNo);

    ApiResponse<MineFundTypeResponse, Object> getMineFundType(String customerNo, String subAccountNo);

    ApiResponse<IncomeResponse, Object> getIncome(String customerNo, String subAccountNo,String indexCode, String range);

    ApiResponse<HoldingPenetrationResponse, Object> getHoldingPenetration(String customerNo, String subAccountNo);

    ApiResponse<PortfolioStyleResponse, Object> getPortfolioStyle(String customerNo, String subAccountNo);

    ApiResponse<List<IndustryAnalysisResponse>, Object> getPortfolioIndustry(String customerNo, String subAccountNo);

    ApiResponse<ListIndustryResponse, Object> getListIndustry(String customerNo, String subAccountNo);

    ApiResponse<BondPenetrationResponse, Object> getBondPenetration(String customerNo, String subAccountNo);
}
