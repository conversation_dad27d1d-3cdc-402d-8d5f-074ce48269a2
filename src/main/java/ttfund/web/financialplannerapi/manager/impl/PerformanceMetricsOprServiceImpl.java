package ttfund.web.financialplannerapi.manager.impl;

import com.alibaba.fastjson.JSON;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import com.ttfund.web.core.model.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import ttfund.web.financialplannerapi.api.FinanceConfigApi;
import ttfund.web.financialplannerapi.api.FundMarketApi;
import ttfund.web.financialplannerapi.api.GroupCalApi;
import ttfund.web.financialplannerapi.api.UserPortraitApi;
import ttfund.web.financialplannerapi.constant.ErrorCodeEnum;
import ttfund.web.financialplannerapi.manager.PerformanceMetricsOprService;
import ttfund.web.financialplannerapi.model.bo.financeconfig.FinanceConfigFundResBO;
import ttfund.web.financialplannerapi.model.bo.financeconfig.FinanceMainFundResBO;
import ttfund.web.financialplannerapi.model.bo.fundpool.FundReqBO;
import ttfund.web.financialplannerapi.model.dto.financeconfig.AdjustDTO;
import ttfund.web.financialplannerapi.model.dto.financeconfig.FinanceConfigPageDTO;
import ttfund.web.financialplannerapi.model.dto.financeconfig.PerformanceMetricsDTO;
import ttfund.web.financialplannerapi.model.reponse.PageCountListResponse;
import ttfund.web.financialplannerapi.model.reponse.groupcal.GroupCalResponse;
import ttfund.web.financialplannerapi.model.request.groupcal.FundDetail;
import ttfund.web.financialplannerapi.model.vo.financeconfig.FinanceConfigVO;
import ttfund.web.financialplannerapi.model.vo.financeconfig.GroupCalResult;
import ttfund.web.financialplannerapi.service.UserAuthValidService;
import ttfund.web.financialplannerapi.util.AssertUtils;
import ttfund.web.financialplannerapi.util.other.QuickListUtils;
import ttfund.web.financialplannerapi.util.other.StringUtils;

import java.math.BigDecimal;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PerformanceMetricsOprServiceImpl implements PerformanceMetricsOprService {

    private final GroupCalApi groupCalApi;
    private final FundMarketApi fundMarketApi;
    private final FinanceConfigApi financeConfigApi;
    private final UserPortraitApi userPortraitApi;
    private final UserAuthValidService userAuthValidService;

    @Override
    public GroupCalResult performanceMetrics(PerformanceMetricsDTO dto) {
        dto.setUserPassportId(userAuthValidService.getUserPassportIdByAuth(dto.getUserPassportId()));
        dto.validDate();
        String customerNo = userPortraitApi.getCustomerNoByPid(dto.getUserPassportId());
        log.info("用户交易账号查询customerNo:{}", customerNo);
        AssertUtils.isNotBlank(customerNo, ErrorCodeEnum.NoCustomerNo);
        dto.setUserid(customerNo);

        // 获取用户所有历史观点，并按建议期顺序排序
        List<FinanceConfigVO> configs = this.getUserAllConfig(dto.getUserPassportId());
        log.info("用户配置观点数据:{}", JSON.toJSONString(configs));
        AssertUtils.isNotEmpty(configs, ErrorCodeEnum.PERSON_FINANCE_CONFIG_NOT_FOUND);

        // 将用户所有历史观点，封装到组合回测请求参数中
        this.handlePerformanceMetricsParam(dto,configs);

        // 获取组合回测数据
        GroupCalResponse<GroupCalResult> response = groupCalApi.groupCal(dto);
        boolean result = Objects.nonNull(response) &&  Objects.nonNull(response.getData());
        AssertUtils.isTrue(result, ErrorCodeEnum.LOCKERROR);

        GroupCalResult groupCal = response.getData();
        // 封装基金名称及调仓理由
        this.handleGroupResult(groupCal,configs);
        return groupCal;
    }

    /**
     * 处理组合回测参数
     * @param dto 组合回测参数
     * @param configs 用户历史所有观点
     */
    private void handlePerformanceMetricsParam(PerformanceMetricsDTO dto, List<FinanceConfigVO> configs) {
        // 获取当前配置
        FinanceConfigVO first = configs.get(0);
        // 根据当前配置，封装基金配比
        List<FundDetail> fundDetails = this.cast2FundDetails(first);
        dto.setFundDetails(fundDetails);

        if (configs.size() > 1) {
            // 如果不止一个观点，则将后续所有观点封装
            List<FinanceConfigVO> histories = configs.subList(1, configs.size());
            // 将历史观点配置，封装基金配比
            List<AdjustDTO> adjusts = this.cast2Adjust(histories);
            dto.setAdjustList(adjusts);
        }
    }

    /**
     * 将配置的基金及占比，封装基金配比参数
     * @param configs 配置建议
     * @return 基金配比参数
     */
    private List<AdjustDTO> cast2Adjust(List<FinanceConfigVO> configs) {
        List<AdjustDTO> adjusts = new ArrayList<>();
        for (FinanceConfigVO config : configs) {
            AdjustDTO adjust = new AdjustDTO();
            adjust.setFundDetails(this.cast2FundDetails(config))
                    .setAdjustDate(config.getRecommendDate().atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli())
                    .setFundPercent(JSON.toJSONString(adjust.getFundDetails()));
            adjusts.add(adjust);
        }
        return adjusts;
    }

    /**
     * 将用户的观点，封装到组合回测请求参数中
     * @param current 配置观点
     * @return 组合回撤基金配比参数
     */
    private List<FundDetail> cast2FundDetails(FinanceConfigVO current) {
        List<FundDetail> fundDetails = new ArrayList<>();
        if (Objects.isNull(current) || CollectionUtils.isEmpty(current.getFunds())) {
            return fundDetails;
        }

        for (FinanceConfigFundResBO financeConfigFund : current.getFunds()) {
            if (CollectionUtils.isEmpty(financeConfigFund.getRecommendFundDetails())) {
                continue;
            }

            for (FinanceMainFundResBO mainFund : financeConfigFund.getRecommendFundDetails()) {
                // 获取配置下每个大类的主选基金代码及配比
                BigDecimal percent = mainFund.getPercent();
                String fundCode = mainFund.getFundCode();
                if (Objects.isNull(percent) || StringUtils.isEmpty(fundCode)) {
                    continue;
                }
                // 封装到配比参数中
                fundDetails.add(new FundDetail(fundCode, percent.toString()));
            }
        }
        return fundDetails;
    }

    /**
     * 获取用户历史所有配置
     * @param passportId 用户通行证id
     * @return 配置列表
     */
    private List<FinanceConfigVO> getUserAllConfig(String passportId) {
        FinanceConfigPageDTO page = new FinanceConfigPageDTO();
        page.setUserPassportId(QuickListUtils.of(passportId));

        ApiResponse<PageCountListResponse<FinanceConfigVO>, Object> response = financeConfigApi.list(page);
        boolean result = Objects.nonNull(response) && Objects.nonNull(response.getData());
        AssertUtils.isTrue(result, ErrorCodeEnum.LOCKERROR);

        // 根据建议期倒序排序
        return response.getData().getList().stream()
                .sorted(Comparator.comparing(FinanceConfigVO::getRecommendDate))
                .collect(Collectors.toList());
    }

    /**
     * 获取基金名字，封装调仓理由
     * @param groupCal 组合回测数据
     */
    private void handleGroupResult(GroupCalResult groupCal, List<FinanceConfigVO> configs) {
        if(CollectionUtils.isEmpty(groupCal.getAdjustPlans())) {
            return;
        }

        List<String> fundCodes = new ArrayList<>();
        groupCal.getAdjustPlans().forEach(adjustPlan -> {
            if(CollectionUtils.isEmpty(adjustPlan.getFundAdjustList())) {
                return;
            }

            adjustPlan.getFundAdjustList().forEach(fundAdjust -> {
                fundCodes.add(fundAdjust.getFundCode());
            });
        });

        // 获取所有需要的基金信息
        List<FundReqBO> list = fundMarketApi.list(fundCodes);
        // 封装为基金code-基金的map
        Map<String, FundReqBO> fundMap = list.stream().collect(Collectors.toMap(FundReqBO::getFundCode, item -> item, (k1, k2) -> k1));
        // 封装为建议期-配置观点的map
        Map<String, FinanceConfigVO> configMap = configs.stream()
                .collect(Collectors.toMap(FinanceConfigVO::getRecommendDateFormat, item -> item, (k1, k2) -> k1));

        // 设置基金名字及调仓理由
        groupCal.getAdjustPlans().forEach(adjustPlan -> {
            if(CollectionUtils.isEmpty(adjustPlan.getFundAdjustList())) {
                return;
            }
            FinanceConfigVO config = configMap.get(adjustPlan.getAdjustDate());
            if (Objects.nonNull(config)) {
                // 根据调仓日期匹配对应建议期的观点，封装调仓理由
                adjustPlan.setReason(config.getReason());
            }

            adjustPlan.getFundAdjustList().forEach(fundAdjust -> {
                fundAdjust.setFundName(fundMap.getOrDefault(fundAdjust.getFundCode(), new FundReqBO()).getFundName());
            });
        });
    }

}
