package ttfund.web.financialplannerapi.manager.impl;

import com.ttfund.web.core.constant.CoreConstant;
import com.ttfund.web.core.model.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.financialplannerapi.api.FinanceConfigApi;
import ttfund.web.financialplannerapi.api.FundAiApi;
import ttfund.web.financialplannerapi.api.UserPortraitApi;
import ttfund.web.financialplannerapi.constant.ErrorCodeEnum;
import ttfund.web.financialplannerapi.constant.financeconfig.FinanceConfigTypeEnum;
import ttfund.web.financialplannerapi.constant.sys.TimeRangeEnum;
import ttfund.web.financialplannerapi.data.impl.ContractMysqlImpl;
import ttfund.web.financialplannerapi.exception.BusinessException;
import ttfund.web.financialplannerapi.manager.FinanceConfigOprService;
import ttfund.web.financialplannerapi.model.bo.financeconfig.FinanceConfigFundResBO;
import ttfund.web.financialplannerapi.model.bo.financeconfig.FinanceMainFundResBO;
import ttfund.web.financialplannerapi.model.bo.sys.UserInfoResBO;
import ttfund.web.financialplannerapi.model.dto.financeconfig.UserDetailsDTO;
import ttfund.web.financialplannerapi.model.reponse.financeconfig.FundAiResponse;
import ttfund.web.financialplannerapi.model.vo.financeconfig.*;
import ttfund.web.financialplannerapi.service.UserAuthValidService;
import ttfund.web.financialplannerapi.service.UserInfoService;
import ttfund.web.financialplannerapi.util.AssertUtils;
import ttfund.web.financialplannerapi.util.other.StringUtils;
import ttfund.web.financialplannerapi.util.security.SecurityUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceConfigOprServiceImpl implements FinanceConfigOprService {

    private final FinanceConfigApi financeConfigApi;
    private final ExecutorService fundAiExecPool;
    private final UserInfoService userInfoService;
    private final FundAiApi fundAiApi;
    private final UserPortraitApi userPortraitApi;
    private final UserAuthValidService userAuthValidService;

    @SneakyThrows
    @Override
    public UserFinanceConfigVO details(UserDetailsDTO dto) {
        dto.setUserPassportId(userAuthValidService.getUserPassportIdByAuth(dto.getUserPassportId()));
        UserFinanceConfigVO result = new UserFinanceConfigVO();
        String userPassportId = dto.getUserPassportId();
        FinanceConfigVO config = this.getUserFinanceConfig(userPassportId);
        String customerNo = userPortraitApi.getCustomerNoByPid(userPassportId);
        AssertUtils.isNotBlank(customerNo, ErrorCodeEnum.NoCustomerNo);
//        UserInfoResBO user = userInfoService.infoByPassportId(userPassportId);

        if(FinanceConfigTypeEnum.isPersonalized(config.getType())) {
            String firstRecommendDate = this.getFirstRecommendDate();
            result.setFirstRecommendDate(firstRecommendDate)
                    .setTimeRange(this.getTimeRage(firstRecommendDate));
        }

        this.setFundAi(config, customerNo);

        result.setConfig(config);
        return result;
    }

    /**
     * 获取时间区间
     * @param firstRecommendDate 首次推荐时间
     * @return 时间区间
     */
    private List<String> getTimeRage(String firstRecommendDate) {
        List<String> timeRange = new ArrayList<>();
        timeRange.add(TimeRangeEnum.NEAR_1_WEEK.getCode());

        if(StringUtils.isEmpty(firstRecommendDate)) {
            return timeRange;
        }

        LocalDate date = LocalDate.parse(firstRecommendDate);
        LocalDate now = LocalDate.now();

        if (date.isBefore(now.minusWeeks(1))) {
            timeRange.add(TimeRangeEnum.NEAR_1_MONTH.getCode());
        }
        if (date.isBefore(now.minusMonths(1))) {
            timeRange.add(TimeRangeEnum.NEAR_3_MONTH.getCode());
        }
        if (date.isBefore(now.minusMonths(3))) {
            timeRange.add(TimeRangeEnum.NEAR_6_MONTH.getCode());
        }
        if (date.isBefore(now.minusMonths(6))) {
            timeRange.add(TimeRangeEnum.NEAR_1_YEAR.getCode());
        }
        if (date.isBefore(now.minusYears(1))) {
            timeRange.add(TimeRangeEnum.NEAR_3_YEAR.getCode());
        }
        if (date.isBefore(now.minusYears(3))) {
            timeRange.add(TimeRangeEnum.NEAR_5_YEAR.getCode());
        }
        return timeRange;
    }


    /**
     * 设置AI秒懂基金
     * @param config 配置建议
     * @param customerNo 交易账号
     */
    private void setFundAi(FinanceConfigVO config,String customerNo) {
        if(Objects.isNull(config.getFunds())) {
            return;
        }

        if(CollectionUtils.isEmpty(config.getFunds())) {
            return;
        }

        List<String> fundCodes = this.getAllMainFundCodes(config);
        if(CollectionUtils.isEmpty(fundCodes)) {
            return;
        }

        List<FundAiVO> fundAis = this.queryFundAi(customerNo, fundCodes);
        if(CollectionUtils.isEmpty(fundAis)) {
            return;
        }

        Map<String, FundAiVO> fundAiMap = fundAis.stream()
                .collect(Collectors.toMap(FundAiVO::getFundCode, v -> v, (v1, v2) -> v1));

        config.getFunds().forEach(fund -> {
            if(CollectionUtils.isEmpty(fund.getRecommendFundDetails())) {
                return;
            }
            fund.getRecommendFundDetails().forEach(detail -> {
                FundAiVO fundAi = fundAiMap.get(detail.getFundCode());
                if(Objects.nonNull(fundAi)) {
                    detail.setFundAi(fundAi.getShortSummary());
                }
            });
        });
    }

    /**
     * 查询AI秒懂基金
     * @param customerNo 用户交易账号
     * @param fundCodes 基金代码
     * @return 基金AI秒懂结果
     */
    private List<FundAiVO> queryFundAi(String customerNo, List<String> fundCodes) {
        String traceId = MDC.get(CoreConstant.logtraceid);
        CountDownLatch latch = new CountDownLatch(fundCodes.size());
        List<FundAiVO> fundAis = new Vector<>();

        fundCodes.forEach(fundCode -> {
            fundAiExecPool.submit(() -> {
                MDC.put(CoreConstant.logtraceid, traceId);
                try{
                    FundAiResponse<FundAiDataVO> response = fundAiApi.queryFundAi(customerNo, fundCode);
                    boolean result = Objects.nonNull(response) && Objects.nonNull(response.getResult()) && Objects.nonNull(response.getResult().getData());
                    if(result) {
                        FundAiVO fundAi = response.getResult().getData();
                        fundAi.setFundCode(fundCode);
                        fundAis.add(fundAi);
                    }
                }finally {
                    latch.countDown();
                }
            });
        });

        try {
            latch.await(1L, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error(e.getMessage(),e);
        }

        return fundAis;
    }

    /**
     * 获取所有主选基金
     * @param config 配置建议
     * @return 所有主选基金
     */
    private List<String> getAllMainFundCodes(FinanceConfigVO config) {
        return config.getFunds().stream()
                .map(FinanceConfigFundResBO::getRecommendFundDetails)
                .filter(v -> !CollectionUtils.isEmpty(v))
                .flatMap(Collection::stream)
                .map(FinanceMainFundResBO::getFundCode)
                .collect(Collectors.toList());
    }

    /**
     * 获取首次配置建议期
     * @return 首次配置建议期
     */
    private String getFirstRecommendDate() {
        ApiResponse<FinanceConfigVO, Object> response = financeConfigApi.getUserFirstConfig(SecurityUtils.getPassportId());
        AssertUtils.notNull(response, ErrorCodeEnum.LOCKERROR);
        return response.getData().getRecommendDateFormat();
    }

    /**
     * 获取用户当前配置建议
     * @param userPassportId 用户通行证id
     * @return 用户配置建议
     */
    @SneakyThrows
    private FinanceConfigVO getUserFinanceConfig(String userPassportId) {
        ApiResponse<FinanceConfigVO, Object> response = financeConfigApi.getUserCurrentConfig(userPassportId);

        AssertUtils.notNull(response, ErrorCodeEnum.LOCKERROR);
        if(Objects.isNull(response.getData())) {
            throw new BusinessException(response.getFirstError());
        }
        return response.getData();
    }
}
