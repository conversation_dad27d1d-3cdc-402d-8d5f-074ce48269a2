package ttfund.web.financialplannerapi.manager.impl;

import com.ttfund.web.core.model.ApiResponse;
import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.financialplannerapi.api.PortfolioApi;
import ttfund.web.financialplannerapi.api.UserHoldAssetApi;
import ttfund.web.financialplannerapi.api.UserPortraitApi;
import ttfund.web.financialplannerapi.constant.ErrorCodeEnum;
import ttfund.web.financialplannerapi.data.impl.ContractMysqlImpl;
import ttfund.web.financialplannerapi.manager.FinanceDiagOprService;
import ttfund.web.financialplannerapi.model.bo.financeconfig.PortfolioResBO;
import ttfund.web.financialplannerapi.model.bo.sys.UserInfoResBO;
import ttfund.web.financialplannerapi.model.dto.financeconfig.UserDetailsDTO;
import ttfund.web.financialplannerapi.model.dto.financeconfig.UserHoldViewDTO;
import ttfund.web.financialplannerapi.model.reponse.financeconfig.PortfolioResponse;
import ttfund.web.financialplannerapi.model.vo.financeconfig.PortfolioVO;
import ttfund.web.financialplannerapi.model.vo.financeconfig.SubAccountInfoVO;
import ttfund.web.financialplannerapi.model.vo.financeconfig.UserHoldViewVO;
import ttfund.web.financialplannerapi.service.UserAuthValidService;
import ttfund.web.financialplannerapi.service.UserInfoService;
import ttfund.web.financialplannerapi.util.AssertUtils;
import ttfund.web.financialplannerapi.util.other.StringUtils;
import ttfund.web.financialplannerapi.util.security.SecurityUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceDiagOprServiceImpl implements FinanceDiagOprService {

    private final UserHoldAssetApi userHoldAssetApi;
    private final PortfolioApi portfolioApi;
    private final UserPortraitApi userPortraitApi;
    private final UserAuthValidService userAuthValidService;

    @Override
    public UserHoldViewVO userHoldView(UserHoldViewDTO request) {
        request.setUserPassportId(userAuthValidService.getUserPassportIdByAuth(request.getUserPassportId()));
//        UserInfoResBO userInfo = userInfoService.infoByPassportId(SecurityUtils.getPassportId());
        String customerNo = userPortraitApi.getCustomerNoByPid(request.getUserPassportId());
        AssertUtils.isNotBlank(customerNo, ErrorCodeEnum.NoCustomerNo);

        ApiResponse<UserHoldViewVO, Object> result = userHoldAssetApi.getHoldAsset(customerNo);
        AssertUtils.notNull(result, ErrorCodeEnum.USER_HOLD_ASSET_ERROR);

        UserHoldViewVO userHoldView = result.getData();
        this.getPortfolioName(request, userHoldView, customerNo);
        return result.getData();
    }

    /**
     * 获取组合名称
     * @param request 请求
     * @param userHoldView 用户持仓
     * @param customerNo 用户交易账号
     */
    private void getPortfolioName(UserHoldViewDTO request, UserHoldViewVO userHoldView, String customerNo) {
        if(CollectionUtils.isEmpty(userHoldView.getPortfolioHolds())) {
            return;
        }

        List<String> portfolioNos = userHoldView.getPortfolioHolds().stream().map(PortfolioResBO::getPortfolioNo).collect(Collectors.toList());
        PortfolioResponse<List<SubAccountInfoVO>> response = portfolioApi.getName(request, portfolioNos, customerNo);

        boolean result = Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getData());
//        AssertUtils.isTrue(result, ErrorCodeEnum.LOCKERROR);
        if (result) {
            List<SubAccountInfoVO> list = response.getData();
            Map<String,String> map = list.stream()
                    .collect(Collectors.toMap(SubAccountInfoVO::getPortfolioNo,
                            SubAccountInfoVO::getPortfolioName, (k1, k2) -> k1));

            userHoldView.getPortfolioHolds().forEach(v -> v.setPortfolioName(map.getOrDefault(v.getPortfolioNo(), "")));
        }
    }
}
