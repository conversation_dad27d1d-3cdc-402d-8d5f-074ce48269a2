package ttfund.web.financialplannerapi.manager;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import ttfund.web.financialplannerapi.model.bo.financeconfig.UserBaseResBO;
import ttfund.web.financialplannerapi.model.dto.financeconfig.UserHoldViewDTO;
import ttfund.web.financialplannerapi.model.vo.financeconfig.UserHoldViewVO;

/**
 * <AUTHOR>
 */
public interface FinanceDiagOprService {

    /**
     * 用户基础信息
     * @param request 请求
     * @return 用户基础信息
     */
    UserHoldViewVO userHoldView(UserHoldViewDTO request);
}
