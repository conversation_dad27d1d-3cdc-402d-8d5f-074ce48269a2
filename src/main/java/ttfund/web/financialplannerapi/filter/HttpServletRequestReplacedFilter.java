package ttfund.web.financialplannerapi.filter;

import ttfund.web.financialplannerapi.util.context.MyHttpServletRequestWrapper;
import ttfund.web.financialplannerapi.util.security.SecurityConstants;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 * <p>创建一个实现Filter的类，重写doFilter方法，将ServletRequest替换为自定义的request类 </p>
 */
@WebFilter(urlPatterns = "/*",filterName = "requestReplaced")
public class HttpServletRequestReplacedFilter implements Filter {

    @Override
    public void destroy() {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String requestURI = httpRequest.getRequestURI();
        String passportId = httpRequest.getParameter(SecurityConstants.PASSPORT_ID);

        if(passportId!=null){
            chain.doFilter(request, response);
            return;
        }

        // 检查 URL 是否需要被过滤
        if (requestURI.contains("/uploadFTP/")) {
            // 继续执行过滤器链
            chain.doFilter(request, response);
            return;
        }

        ServletRequest requestWrapper = null;
        if(request instanceof HttpServletRequest) {
            requestWrapper = new MyHttpServletRequestWrapper((HttpServletRequest) request);
        }
        if(requestWrapper == null) {
            chain.doFilter(request, response);
        } else {
            chain.doFilter(requestWrapper, response);
        }
    }

    @Override
    public void init(FilterConfig arg0) throws ServletException {

    }
}