package ttfund.web.financialplannerapi.converter.fundpool;

import org.springframework.beans.BeanUtils;
import ttfund.web.financialplannerapi.model.bo.fundpool.FundReqBO;
import ttfund.web.financialplannerapi.model.vo.fundpool.FundComVO;
import ttfund.web.financialplannerapi.util.number.BigDecimalUtil;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class FundConverter {

    public static FundReqBO toReq(FundComVO vo){
        FundReqBO bo = new FundReqBO();
        bo.setFundCode(vo.getFundCode())
                .setFundName(vo.getShortname())
                .setStockTurnoverRate(BigDecimalUtil.isBigDecimal(vo.getTurnover())?new BigDecimal(vo.getTurnover()):null)
                .setHistoricMaxDrawdown(BigDecimalUtil.isBigDecimal(vo.getMaxretraSe())?new BigDecimal(vo.getMaxretraSe()):null)
                .setFundFullType(vo.getRsbType())
                .setFundMajorType(vo.getRsbType().length()>3?vo.getRsbType().substring(0,3):vo.getRsbType())
                .setYearToDateReturn(BigDecimalUtil.isBigDecimal(vo.getSylSy())?new BigDecimal(vo.getSylSy()):null)
                .setOneYearReturn(BigDecimalUtil.isBigDecimal(vo.getSylY())?new BigDecimal(vo.getSylY()):null)
                .setThreeYearReturn(BigDecimalUtil.isBigDecimal(vo.getSylTry())?new BigDecimal(vo.getSylTry()):null)
                .setOneYearSharpeRatio(BigDecimalUtil.isBigDecimal(vo.getSharp1())?new BigDecimal(vo.getSharp1()):null)
                .setThreeYearSharpeRatio(BigDecimalUtil.isBigDecimal(vo.getSharp3())?new BigDecimal(vo.getSharp3()):null)
                .setCurrentManagerMaxDrawdown(BigDecimalUtil.isBigDecimal(vo.getMaxretra1())?new BigDecimal(vo.getMaxretra1()).multiply(BigDecimal.valueOf(100)):null)
                .setSubscribeStatus(vo.getSgzt())
                .setSubscribeLimit(BigDecimalUtil.isBigDecimal(vo.getMaxSg())?new BigDecimal(vo.getMaxSg()):null);
        return bo;
    }

    public static FundReqBO toReqOnlyCodeName(FundComVO vo){
        FundReqBO bo = new FundReqBO();
        bo.setFundCode(vo.getFundCode())
                .setFundName(vo.getShortname());
        return bo;
    }


}
