package ttfund.web.financialplannerapi.converter.contract;


import ttfund.web.financialplannerapi.model.reponse.LcsWindowResponse;
import ttfund.web.financialplannerapi.model.reponse.TTFundUserContractResponse;
import ttfund.web.financialplannerapi.model.reponse.UserTTPlannerResponse;

/**
 * <AUTHOR>
 */
public class ContractRecordConverter {


    public static LcsWindowResponse ttFundPlannerResponse2LcsWindow(UserTTPlannerResponse ttPlanner){
        LcsWindowResponse result = new LcsWindowResponse();
        result.setInstitutionId(ttPlanner.getInstitutionId());
        result.setInstitutionName(ttPlanner.getInstitutionName());
        result.setNickName(ttPlanner.getFinancialPlannerName());
        return result;
    }
}
