package ttfund.web.financialplannerapi.interceptor;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;
import ttfund.web.financialplannerapi.constant.sys.HttpMethodConstants;
import ttfund.web.financialplannerapi.util.context.GetRequestJsonUtil;
import ttfund.web.financialplannerapi.util.context.MyHttpServletRequestWrapper;
import ttfund.web.financialplannerapi.util.security.SecurityConstants;
import ttfund.web.financialplannerapi.util.security.SecurityContextHolder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 自定义请求头拦截器，将Header数据封装到线程变量中方便获取
 * 注意：此拦截器会同时验证当前用户有效期自动刷新有效期
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HeaderInterceptor implements AsyncHandlerInterceptor {



    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
//            System.out.println("Handler error");
//            System.out.println(handler);
            return true;
        }

        String passportId = request.getParameter(SecurityConstants.PASSPORT_ID);
        if(passportId!=null){
            SecurityContextHolder.setPassportId(passportId);
            return true;
        }

        boolean existJson = HttpMethodConstants.POST_METHOD.equals(request.getMethod())
                || HttpMethodConstants.PUT_METHOD.equals(request.getMethod())
                || HttpMethodConstants.DELETE_METHOD.equals(request.getMethod());

        if(existJson){
            //封装request
            MyHttpServletRequestWrapper myWrapper= new MyHttpServletRequestWrapper(request);
            //获取json数据
            JSONObject json = GetRequestJsonUtil.getRequestJsonObject(myWrapper);

            if(json==null){return true;}
            if(json.get(SecurityConstants.PASSPORT_ID)!=null){
                passportId = json.get(SecurityConstants.PASSPORT_ID).toString();
                SecurityContextHolder.setPassportId(passportId);
                return true;
            }
        }

        log.warn("未解析到passportid");
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        SecurityContextHolder.remove();
    }

}
