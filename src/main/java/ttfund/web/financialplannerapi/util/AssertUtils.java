package ttfund.web.financialplannerapi.util;

import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import ttfund.web.financialplannerapi.constant.ErrorCodeEnum;
import ttfund.web.financialplannerapi.exception.BusinessException;

import java.util.List;
import java.util.Map;

public class AssertUtils {
    private static final String detailIdMsg = ", id: %s";

    private static final String detailErrorMsg = " errorData: 【%s】";

    public AssertUtils() {
    }

    @SneakyThrows
    public static void isTrue(boolean expression, ErrorCodeEnum errorCodeEnum) {
        if (!expression) {
            throw new BusinessException(errorCodeEnum);
        }
    }

    /**
     *
     * @param exposeDataId 是否暴露异常数据Id，根据该字段拼接不同提示
     * @param errorMsg 异常数据详情
     */

    @SneakyThrows
    public static void isTrue(boolean expression, ErrorCodeEnum errorCodeEnum, boolean exposeDataId, String errorMsg) {
        if (!expression) {
            if (exposeDataId) {
                throw new BusinessException(errorCodeEnum.getMsg() + String.format(detailIdMsg, errorMsg),errorCodeEnum.getCode());
            } else {
                throw new BusinessException(errorCodeEnum.getMsg() + String.format(detailErrorMsg, errorMsg),errorCodeEnum.getCode());
            }
        }
    }

    @SneakyThrows
    public static void isTrue(boolean expression, int code, String msg) {
        if (!expression) {
            throw new BusinessException( msg,code);
        }
    }

    @SneakyThrows
    public static void isFalse(boolean expression, ErrorCodeEnum errorCodeEnum) {
        if (expression) {
            throw new BusinessException(errorCodeEnum);
        }
    }

    /**
     *
     * @param exposeDataId 是否暴露异常数据Id，根据该字段拼接不同提示
     * @param errorMsg 异常数据详情
     */
    @SneakyThrows
    public static void isFalse(boolean expression, ErrorCodeEnum errorCodeEnum, boolean exposeDataId, String errorMsg) {
        if (expression) {
            if (exposeDataId) {
                throw new BusinessException(errorCodeEnum.getMsg() + String.format(detailIdMsg, errorMsg),errorCodeEnum.getCode());
            } else {
                throw new BusinessException(errorCodeEnum.getMsg() + String.format(detailErrorMsg, errorMsg),errorCodeEnum.getCode());
            }
        }
    }


    @SneakyThrows
    public static void isNull(@Nullable Object object, ErrorCodeEnum errorCodeEnum) {
        if (object != null) {
            throw new BusinessException(errorCodeEnum);
        }
    }

    @SneakyThrows
    public static void isNotNull(@Nullable Object object, ErrorCodeEnum errorCodeEnum) {
        if (object == null) {
            throw new BusinessException(errorCodeEnum);
        }
    }

    /**
     *
     * @param exposeDataId 是否暴露异常数据Id，根据该字段拼接不同提示
     * @param errorMsg 异常数据详情
     */
    @SneakyThrows
    public static void isNull(@Nullable Object object, ErrorCodeEnum errorCodeEnum, boolean exposeDataId, String errorMsg) {
        if (object != null) {
            if (exposeDataId) {
                throw new BusinessException(errorCodeEnum.getMsg() + String.format(detailIdMsg, errorMsg),errorCodeEnum.getCode());
            } else {
                throw new BusinessException(errorCodeEnum.getMsg() + String.format(detailErrorMsg, errorMsg),errorCodeEnum.getCode());
            }
        }
    }

    @SneakyThrows
    public static void notNull(@Nullable Object object, ErrorCodeEnum errorCodeEnum) {
        if (object == null) {
            throw new BusinessException(errorCodeEnum);
        }
    }

    @SneakyThrows
    public static void notNull(@Nullable Object object, ErrorCodeEnum errorCodeEnum, boolean exposeDataId, String errorMsg) {
        if (object == null) {
            if (exposeDataId) {
                throw new BusinessException(errorCodeEnum.getMsg() + String.format(detailIdMsg, errorMsg),errorCodeEnum.getCode());
            } else {
                throw new BusinessException(errorCodeEnum.getMsg() + String.format(detailErrorMsg, errorMsg),errorCodeEnum.getCode());
            }
        }
    }

    @SneakyThrows
    public static void isBlank(String text, ErrorCodeEnum errorCodeEnum) {
        if (!StringUtils.isBlank(text)) {
            throw new BusinessException(errorCodeEnum);
        }
    }

    @SneakyThrows
    public static void isNotBlank(String text, ErrorCodeEnum errorCodeEnum) {
        if (StringUtils.isBlank(text)) {
            throw new BusinessException(errorCodeEnum);
        }
    }

    @SneakyThrows
    public static void isNotEmpty(List list, ErrorCodeEnum errorCodeEnum) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException(errorCodeEnum);
        }
    }

    @SneakyThrows
    public static void isNotEmpty(Map map, ErrorCodeEnum errorCodeEnum) {
        if (CollectionUtils.isEmpty(map)) {
            throw new BusinessException(errorCodeEnum);
        }
    }

    @SneakyThrows
    public static void isEmpty(List list, ErrorCodeEnum errorCodeEnum) {
        if (!CollectionUtils.isEmpty(list)) {
            throw new BusinessException(errorCodeEnum);
        }
    }
}
