package ttfund.web.financialplannerapi.util;

import com.ttfund.web.base.helper.CommonHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2021/10/27 17:23
 */
public class MatchUtil {

    public static final String pattern_notice_area = "\\{[A-Za-z0-9]{0,100}}";

    public static final String PATTERN_YYYY_MM_DD = "^(19|20)\\d{2}-(0[1-9]|1[0-2])-(0[1-9]|[1-2]\\d|3[0-1])$";
    public static final String PATTERN_YYYYMMDD = "^(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\\d|3[0-1])$";

    /**
     * 占位公告匹配需要回填字段
     *
     * @param title
     * @return
     */
    public static List<String> getNoticeArea(String title) {
        List<String> result = new ArrayList<>();
        if (StringUtils.isEmpty(title)) {
            return result;
        }
        Pattern r = Pattern.compile(pattern_notice_area);
        Matcher m = r.matcher(title);
        while (m.find()) {
            String temp1= CommonHelper.toStr(m.group()).replace("{", "").replace("}", "");
            result.add(temp1);
        }
        return result;
    }
}
