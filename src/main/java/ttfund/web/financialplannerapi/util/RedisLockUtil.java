package ttfund.web.financialplannerapi.util;

import com.ttfund.web.base.redishelper.IRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class RedisLockUtil {

    Logger logger = LoggerFactory.getLogger(RedisLockUtil.class);

    /**
     * 加redis锁工具
     * @param redis redis，写
     * @param key 加锁key
     * @param time 重复尝试次数不易太大
     * @param expri 锁过期时间
     * @return
     */
    public boolean lock(IRedis redis, String key, int time, long expri) {
        boolean result = false;
        int index = 0;
        try {
            while (true) {
                Long aLong = redis.incrBy(key, 1L, expri);
                index++;
                if (aLong != 1L) {
                    Thread.sleep(50);
                }else {
                    result = true;
                    break;
                }
                if (index >= time) {
                    logger.info("获取锁失败：key={}", key);
                    break;
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();  // set interrupt flag
            logger.error(e.getMessage(), e);
        }
        return result;
    }
}
