package ttfund.web.financialplannerapi.util.security.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import ttfund.web.financialplannerapi.constant.ErrorCodeEnum;
import ttfund.web.financialplannerapi.exception.InnerAuthException;
import ttfund.web.financialplannerapi.util.other.ServletUtils;
import ttfund.web.financialplannerapi.util.other.StringUtils;
import ttfund.web.financialplannerapi.util.security.SecurityConstants;
import ttfund.web.financialplannerapi.util.security.annotation.InnerAuth;

/**
 * 内部服务调用验证处理
 * 
 * <AUTHOR>
 */
@Aspect
@Component
public class InnerAuthAspect implements Ordered
{
    @Around("@annotation(innerAuth)")
    public Object innerAround(ProceedingJoinPoint point, InnerAuth innerAuth) throws Throwable
    {
        String source = ServletUtils.getRequest().getHeader(SecurityConstants.FROM_SOURCE);
        // 内部请求验证
        if (!StringUtils.equals(SecurityConstants.INNER, source))
        {
            throw new InnerAuthException(ErrorCodeEnum.INNER_ACCESS_DENIED.getMsg());
        }

        return point.proceed();
    }

    /**
     * 确保在权限认证aop执行前执行
     */
    @Override
    public int getOrder()
    {
        return Ordered.HIGHEST_PRECEDENCE + 1;
    }
}
