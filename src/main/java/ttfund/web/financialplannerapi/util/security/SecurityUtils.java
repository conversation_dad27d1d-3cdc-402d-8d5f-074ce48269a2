package ttfund.web.financialplannerapi.util.security;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import ttfund.web.financialplannerapi.constant.AppConfigConstant;
import ttfund.web.financialplannerapi.constant.DataAuthorityConstant;
import ttfund.web.financialplannerapi.constant.sys.RoleConstant;

import javax.servlet.http.HttpServletRequest;

/**
 * 权限获取工具类
 * 
 * <AUTHOR>
 */
public class SecurityUtils
{
    /**
     * 获取用户通行证ID
     */
    public static String getPassportId()
    {
        return SecurityContextHolder.getPassportId();
    }

    /**
     * 是否为全部数据权限
     * @return 结果
     */
    public static boolean isAll() {
        Integer dataType = SecurityUtils.getDataType();
        return DataAuthorityConstant.isAll(dataType);
    }

    /**
     * 是否不为全部数据权限
     * @return 结果
     */
    public static boolean isNotAll() {
        return !isAll();
    }

    /**
     * 获取机构ID
     * @return 机构ID
     */
    public static String getInstitutionId() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        return (String) request.getAttribute("institutionId");
    }

    /**
     * 获取数据权限类型
     * @return 数据权限类型
     */
    public static Integer getDataType() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        return (Integer) request.getAttribute("dataType");
    }

    /**
     * 获取角色类型
     * @return 角色类型
     */
    public static Integer getRole() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        return (Integer) request.getAttribute("role");
    }

    /**
     * 是否是理财师
     * @return true：理财师，false：非理财师
     */
    public static boolean isNotPlanner(){
        return !isPlanner();
    }

    /**
     * 是否是理财师
     * @return true：理财师，false：非理财师
     */
    public static boolean isPlanner(){
        return RoleConstant.FINANCIAL_PLANNER == getRole();
    }

    /**
     * 是否是超级管理员
     * @return true：超级管理员，false：非超级管理员
     */
    public static boolean isSuperAdmin(){
        return RoleConstant.SUPER_ADMIN == getRole();
    }

    /**
     * 是否是理财师总监
     * @return true：理财师总监，false：非理财师总监
     */
    public static boolean isPlannerAdmin(){
        return RoleConstant.FINANCIAL_PLANNER_ADMIN == getRole();
    }

    /**
     * 是否是普通用户
     * @return true：普通用户，false：非普通用户
     */
    public static boolean isCustomer(){
        return RoleConstant.CUSTOMER == getRole();
    }


    /**
     * 是否是天天基金机构
     * @return 判断结果
     */
    public static boolean isTtfundIns() {
        return AppConfigConstant.TTFUND_INSTITUTIONID.equals(getInstitutionId());
    }

    public static boolean isNotTtfundIns() {
        return !isTtfundIns();
    }

}
