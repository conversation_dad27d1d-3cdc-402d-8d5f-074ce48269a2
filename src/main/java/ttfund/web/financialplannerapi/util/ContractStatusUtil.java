package ttfund.web.financialplannerapi.util;

/**
 * 签约状态
 */
public class ContractStatusUtil {
    /**
     * 签约
     */
    public final static int CONTRACT = 1 << 0;
    /**
     * 黑名单
     */
    public final static int BLACK = 1 << 1;

    /**
     * 校验
     */
    public static boolean hasState(int states, int value) {
        return (states & value) != 0;
    }

    /**
     * 添加状态
     */
    public static long addState(int states, int value) {
        if (hasState(states, value)) {
            return states;
        }
        return (states | value);
    }

    /**
     * 删除状态
     */
    public static long removeState(int states, int value) {
        if (!hasState(states, value)) {
            return states;
        }
        return states ^ value;
    }

    public static void main(String[] args) {
        int status = 2;
        System.out.println(hasState(status, CONTRACT));
    }
}
