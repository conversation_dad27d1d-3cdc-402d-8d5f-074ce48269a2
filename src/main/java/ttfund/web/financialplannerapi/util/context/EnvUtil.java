package ttfund.web.financialplannerapi.util.context;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import ttfund.web.financialplannerapi.config.AppConfig;
import ttfund.web.financialplannerapi.constant.EnvConst;

/**
 * 判断运行环境
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class EnvUtil {

    private final AppConfig appConfig;

    public boolean isProd(){
        return EnvConst.PROP.equals(this.appConfig.ENV);
    }

    public boolean isTest(){
        return EnvConst.TEST.equals(this.appConfig.ENV);
    }

    public boolean isDev(){
        return EnvConst.DEV.equals(this.appConfig.ENV);
    }
}
