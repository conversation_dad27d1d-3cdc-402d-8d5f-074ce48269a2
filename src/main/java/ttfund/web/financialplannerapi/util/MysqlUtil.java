package ttfund.web.financialplannerapi.util;

import com.ttfund.web.base.helper.DruidHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import ttfund.web.financialplannerapi.model.SQLHandler;

import java.sql.*;
import java.util.List;

/**
 * 核心包sql执行缺少返回值、事务等功能，此处进行工具加强
 */
@Slf4j
public class MysqlUtil {
    private MysqlUtil() {
    }

    private static DruidHelper mysqlWrite;

    private static ThreadLocal<Connection> threadLocal = new ThreadLocal<>();

    public static void enhance(DruidHelper mysql) {
        mysqlWrite = mysql;
    }

    public static Connection getConnection() {
        Connection conn = threadLocal.get();
        if (conn == null) {
            conn = mysqlWrite.getconn();
            threadLocal.set(conn);
        }
        return conn;
    }

    public static Connection startTransaction() {
        Connection conn = getConnection();
        try {
            conn.setAutoCommit(false);
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
        }
        return conn;
    }

    public static void commit() {
        try (Connection conn = getConnection()) {
            conn.commit();
            conn.setAutoCommit(true);
            threadLocal.remove();
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static int[] doUpdateWithTransaction(List<SQLHandler> sqlHandlers) {
        if (CollectionUtils.isEmpty(sqlHandlers)) {
            return new int[0];
        }
        int[] result = new int[sqlHandlers.size()];
        Connection conn = startTransaction();
        for (int i = 0; i < sqlHandlers.size(); i++) {
            try (PreparedStatement ps = conn.prepareStatement(sqlHandlers.get(i).getSQL())) {
                List<Object> params = sqlHandlers.get(i).getParams();
                if (!CollectionUtils.isEmpty(params)) {
                    for (int j = 0; j < params.size(); j++) {
                        ps.setObject(j + 1, params.get(j));
                    }
                }
                result[i] = ps.executeUpdate();
            } catch (SQLException e) {
                try {
                    log.error(e.getMessage(), e);
                    conn.rollback();
                    threadLocal.remove();
                    conn.close();
                    return new int[0];
                } catch (SQLException ex) {
                    // none
                }
            }
        }
        commit();
        return result;
    }

    public static int executeBatchUpdate(DruidHelper mysql,String sql, List<List<Object>> parameters) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        try {
            conn = mysql.getconn();
            conn.setAutoCommit(false);
            Statement stm = conn.createStatement();
            ps = conn.prepareStatement(sql);

            for(int i = 0; i < parameters.size(); ++i) {
                List<Object> paramlist = (List)parameters.get(i);

                for(int i2 = 0; i2 < paramlist.size(); ++i2) {
                    ps.setObject(i2 + 1, paramlist.get(i2));
                }

                ps.addBatch();
                if (i > 0 && i % 200 == 0) {
                    int[] ints = ps.executeBatch();
                    result += ints.length;
                }
            }

            int[] ints = ps.executeBatch();
            result += ints.length;
            conn.commit();
            ps.close();
            conn.setAutoCommit(true);
        } catch (Exception var15) {
            log.error(sql, var15);
        } finally {
            mysql.closeConn(conn,ps,(ResultSet) null);
        }

        return result;
    }
}
