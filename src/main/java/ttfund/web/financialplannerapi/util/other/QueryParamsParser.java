package ttfund.web.financialplannerapi.util.other;

import lombok.SneakyThrows;

import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryParamsParser {

    /**
     * 获取参数字符串里的指定参数的值
     * @param target 参数字符串，如“/project/interface?param=test”
     * @param param 参数名，如param
     * @return 参数的值，如test
     */
    @SneakyThrows
    public static String getQueryParam(String target, String param) {
        Map<String, String> params = getQueryParams(target);
        return params.get(param);
    }

    /**
     * 获取参数字符串里的参数键值对
     * @param queryString 参数字符串，如“/project/interface?param=test”
     * @return 参数键值对
     */
    @SneakyThrows
    public static Map<String, String> getQueryParams(String queryString) {
        Map<String, String> queryParams = new HashMap<>();
        
        // 如果字符串以"?"开头，则去掉它
        if (queryString.contains("?")) {
            queryString = queryString.substring(queryString.indexOf("?")+1);
        }
        
        // 分割查询字符串为键值对
        String[] pairs = queryString.split("&");
        
        for (String pair : pairs) {
            // 查找"="的位置
            int idx = pair.indexOf("=");

            // 如果找到了"="，则解析键和值
            if (idx != -1) {
                String key = URLDecoder.decode(pair.substring(0, idx), "UTF-8");
                String value = URLDecoder.decode(pair.substring(idx + 1), "UTF-8");
                queryParams.put(key, value);
            }
        }
        
        return queryParams;
    }

}