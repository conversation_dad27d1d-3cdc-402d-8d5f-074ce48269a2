package ttfund.web.financialplannerapi.util.other;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class CamelCaseUtil {

    /**
     * <AUTHOR>
     * @Description 将驼峰转为下划线
     * @param str
     * @return java.lang.String
     * @Date   2022/4/22 13:11
     * @since  1.0.0
     */
    public static String camel2Underline(String str) {
        Pattern compile = Pattern.compile("[A-Z]");
        Matcher matcher = compile.matcher(str);
        StringBuffer sb = new StringBuffer();
        while(matcher.find()) {
            matcher.appendReplacement(sb,  "_" + matcher.group(0).toLowerCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * <AUTHOR>
     * @Description 将下划线转为驼峰
     * @param str
     * @return java.lang.String
     * @Date   2022/4/22 13:12
     * @since  1.0.0
     */
    public static String underline2Camel(String str) {
        // 使用正则表达式匹配下划线及其后的字符
        Pattern pattern = Pattern.compile("_(.)");
        Matcher matcher = pattern.matcher(str);
        StringBuffer sb = new StringBuffer();

        // 替换匹配到的下划线及其后的字符为大写字母
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(sb);

        // 返回最终结果
        return sb.toString();
    }
}
