package ttfund.web.financialplannerapi.util.other;

import ttfund.web.financialplannerapi.util.time.LocalDateUtil;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class QuickListUtils {

    public static List<String> of(String... str){
        List<String> list = new ArrayList<>();
        Arrays.stream(str).forEach(item->{
            if(StringUtils.isNotEmpty(item)){
                list.add(item);
            }
        });
        return list;
    }

    public static  List<LocalDateTime> of(LocalDateTime... time){
        List<LocalDateTime> list = new ArrayList<>();
        Arrays.stream(time).forEach(item->{
            if(item!=null){
                list.add(item);
            }
        });
        return list;
    }

    public static  List<Date> of(Date... time){
        List<Date> list = new ArrayList<>();
        Arrays.stream(time).forEach(item->{
            if(item!=null){
                list.add(item);
            }
        });
        return list;
    }

    @SafeVarargs
    public static <T> List<T> of(T... t){
        List<T> list = new ArrayList<>();
        Arrays.stream(t).forEach(item->{
            if(item!=null){
                list.add(item);
            }
        });
        return list;
    }

    public static List<LocalDateTime> toLocalDateTimeOf(String... time){
        List<LocalDateTime> list = new ArrayList<>();
        Arrays.stream(time).forEach(item->{
            if(StringUtils.isNotEmpty(item)){
                list.add((LocalDateUtil.generateLocalDateTime(item)));
            }
        });
        return list;
    }
}
