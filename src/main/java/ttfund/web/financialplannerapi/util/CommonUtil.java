package ttfund.web.financialplannerapi.util;

import com.ttfund.web.base.helper.DateHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import ttfund.web.financialplannerapi.constant.AppConfigConstant;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2021/8/2 14:36
 */
public class CommonUtil {

    public static Long getTimePoint(Date dt) {
        return DateHelper.dateToUnixTimeMillisecond(dt) * 1000000;
    }

    /**
     * 字符串转list
     *
     * @param content
     * @param spit
     * @return
     */
    public static List<String> toList(String content, String spit) {
        List<String> result = new ArrayList<String>();
        if (StringUtils.isNotEmpty(content)) {
            String[] list = content.split(spit);
            for (String item : list) {
                if (StringUtils.isNotEmpty(item)) {
                    result.add(item);
                }
            }
        }
        return result;
    }

    /**
     * 获取过去第几天的日期
     *
     * @param past 过去天数
     * @param date 指定日期
     * @param format 精确度：时分秒
     * @return
     */
    public static Date getPastDate(int past,Date date,String format) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - past);
        Date today = calendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        String result = sdf.format(today);
        Date date1 = DateHelper.stringToDate2(result, format);
        return date1;
    }
    /**
     * 验证是否为手机号
     * @param mobile 手机号
     * @return true 验证通过
     */
    public static boolean checkMobile(String mobile) {
        String regex = "^(1[3-9][0-9])\\d{8}$";
        Pattern p = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        Matcher m = p.matcher(mobile);
        return m.matches();
    }

    public static <K, V> K getRandomMapKey(Map<K, V> map) {
        List<K> keys = new ArrayList<>(map.keySet());
        int size = keys.size();
        if (size == 0) {
            throw new IllegalArgumentException("Map is empty");
        }
        int randomIndex = new Random().nextInt(size);
        return keys.get(randomIndex);
    }

}
