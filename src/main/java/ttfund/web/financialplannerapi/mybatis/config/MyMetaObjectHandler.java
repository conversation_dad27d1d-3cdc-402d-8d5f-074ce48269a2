package ttfund.web.financialplannerapi.mybatis.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    /**
     * 插入时的填充策略
     * @param metaObject
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "timeCreated", Date::new, Date.class);
        this.strictInsertFill(metaObject, "timeModified", Date::new, Date.class);
        this.strictInsertFill(metaObject, "createTime", LocalDateTime::now, LocalDateTime.class);
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime::now, LocalDateTime.class);
        this.strictInsertFill(metaObject, "deleted" , Integer.class,0);
//        this.strictInsertFill(metaObject, "bizId" , String.class, IdWorker.worker.nextId());
//        this.strictInsertFill(metaObject, "operation" , String.class,"add");
    }

    /**
     * 更新时的填充策略
     * @param metaObject
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "timeModified", Date::new, Date.class);
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime::now, LocalDateTime.class);
//        this.strictInsertFill(metaObject, "editBy" , String.class,SecurityUtils.getUsername());
//        this.strictInsertFill(metaObject, "operation" , String.class,"edit");
    }
}
