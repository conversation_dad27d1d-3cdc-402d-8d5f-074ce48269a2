package ttfund.web.financialplannerapi.mybatis.generator;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;
import ttfund.web.financialplannerapi.mybatis.util.PageParam;
import ttfund.web.financialplannerapi.util.other.CamelCaseUtil;
import ttfund.web.financialplannerapi.util.other.ReflectUtil;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class ParamWrapperGenerator<T> {

    private final String pageField = "page";
    private final String sizeField = "size";
    private final String sortField = "sortField";
    private final String sortType = "sortType";
    private final String ascValue = "asc";
    private final String descValue = "desc";

    public QueryWrapper<T> generate(PageParam pp){
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        if(pp.getSortField()!=null){
            if(pp.getSortType()!=null&&pp.getSortType().equals(ascValue)){
                wrapper.orderByAsc(CamelCaseUtil.camel2Underline(pp.getSortField()));
            }else{
                wrapper.orderByDesc(CamelCaseUtil.camel2Underline(pp.getSortField()));
            }
        }else{
            wrapper.orderByDesc("id");
        }
        return this.generateParam(wrapper,pp);
    }

    public QueryWrapper<T> generateParam(QueryWrapper<T> wrapper,Object pageObject) {
        Field[] fields = pageObject.getClass().getDeclaredFields();
        for(Field field : fields) {
            field.setAccessible(true);
            String fieldName = field.getName();
            switch (fieldName){
                case pageField:
                case sizeField:
                case sortField:
                case sortType: continue;
                default: addParam(wrapper, pageObject,field);break;
            }
        }
        return wrapper;
    }

    @SneakyThrows
    public void addParam(QueryWrapper<T> wrapper, Object pageObject, Field field){
        String fieldName = field.getName();
        Object fieldValue = ReflectUtil.getValue(pageObject,fieldName);
        if(fieldValue==null){return;}
        Class<?> classType = fieldValue.getClass();
        if(List.class.isAssignableFrom(classType) || classType.newInstance() instanceof List){
            int size = ReflectUtil.getListSize(fieldValue);
            if(size==0){return;}
            Object item = ReflectUtil.getItemValue(fieldValue,0);
            if(item.getClass().isEnum()){
                List<String> codeList = getCodeList(fieldValue,size);
                wrapper.in(CamelCaseUtil.camel2Underline(fieldName),codeList);
            }else{
                for(int i=0;i<size;i++){
                    if(i>0){
                        wrapper.or().like(CamelCaseUtil.camel2Underline(fieldName),ReflectUtil.getItemValue(fieldValue,i));
                    }else{
                        wrapper.like(CamelCaseUtil.camel2Underline(fieldName),ReflectUtil.getItemValue(fieldValue,i));
                    }
                }
            }
        }
    }

    public List<String> getCodeList(Object fieldValue,int size){
        List<String> codeList = new ArrayList<>();
        for(int i=0;i<size;i++){
            Object item = ReflectUtil.getItemValue(fieldValue,0);
            codeList.add(Objects.requireNonNull(ReflectUtil.getValue(item, "code")).toString());
        }
        return codeList;
    }
}
