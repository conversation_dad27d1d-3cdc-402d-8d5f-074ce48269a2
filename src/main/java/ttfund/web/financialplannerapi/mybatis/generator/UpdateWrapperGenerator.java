package ttfund.web.financialplannerapi.mybatis.generator;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class UpdateWrapperGenerator<T> {

    public UpdateWrapper<T> updateNotUseByBizId(String bizId){
        UpdateWrapper<T> wrapper = this.generateBasic();
        wrapper.eq("biz_id",bizId);
        return wrapper;
    }

    public UpdateWrapper<T> updateNotUseByUserBizId(String userBizId){
        UpdateWrapper<T> wrapper = this.generateBasic();
        wrapper.eq("user_biz_id",userBizId);
        return wrapper;
    }

    public UpdateWrapper<T> updateNotUseByUserTechBizId(String userTechBizId){
        UpdateWrapper<T> wrapper = this.generateBasic();
        wrapper.eq("user_tech_biz_id",userTechBizId);
        return wrapper;
    }

    public UpdateWrapper<T> updateNotUseByCompBizId(String compBizId){
        UpdateWrapper<T> wrapper = this.generateBasic();
        wrapper.eq("comp_biz_id",compBizId);
        return wrapper;
    }

    private UpdateWrapper<T> generateBasic(){
        UpdateWrapper<T> wrapper = new UpdateWrapper<>();
        wrapper.set("deleted","1").set("status", "StatusEnum.DISABLED.getCode()").in("status","StatusEnum.APPROVED.getCode()",
                        "StatusEnum.DRAFT.getCode()","StatusEnum.NOT_APPROVED.getCode()");
        return wrapper;
    }
}
