package ttfund.web.financialplannerapi.mybatis.generator;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import ttfund.web.financialplannerapi.mybatis.annotation.*;
import ttfund.web.financialplannerapi.mybatis.util.PageParam;
import ttfund.web.financialplannerapi.util.other.CamelCaseUtil;
import ttfund.web.financialplannerapi.util.other.ReflectUtil;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
public class QueryWrapperGenerator<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private final String pageField = "page";
    private final String sizeField = "size";
    private final String sortField = "sortField";
    private final String sortType = "sortType";
    private final String queryFields = "queryFields";
    private final String ascValue = "asc";
    private final String descValue = "desc";
    private final String onlyMe = "onlyMe";
    private final String query = "query";
    private final String queries = "queries";

    public Class<T> clazz;

    public QueryWrapper<T> generate(PageParam pp){
        return this.generate(pp,this.getIdField());
    }

    public QueryWrapper<T> generateAsc(PageParam pp){
        return this.generate(pp,"sort");
    }

    public QueryWrapper<T> generate(PageParam pp,String field){
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        if(pp.getSortField()!=null&&!pp.getSortField().isEmpty()){
            if(pp.getSortType()!=null&&pp.getSortType().equals(descValue)){
                wrapper.last("order by "+CamelCaseUtil.camel2Underline(pp.getSortField())+" desc,"+this.getIdField()+" desc");
            }else{
                wrapper.last("order by "+ CamelCaseUtil.camel2Underline(pp.getSortField()) +" ,"+this.getIdField()+" desc");
            }
        }else{
            wrapper.orderByAsc(field).orderByDesc(this.getIdField());
        }
        return this.generateParam(wrapper,pp);
    }

    public QueryWrapper<T> generateAsc(PageParam pp,String field){
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        if(pp.getSortField()!=null&&!pp.getSortField().isEmpty()){
            if(pp.getSortType()!=null&&pp.getSortType().equals(descValue)){
                wrapper.last("order by "+ CamelCaseUtil.camel2Underline(pp.getSortField()) +" desc,"+this.getIdField());
            }else{
                wrapper.last("order by "+ CamelCaseUtil.camel2Underline(pp.getSortField()) +" ,"+this.getIdField());
            }
        }else{
            wrapper.orderByAsc(field).orderByAsc(this.getIdField());
        }
        return this.generateParam(wrapper,pp);
    }

    public QueryWrapper<T> generate(PageParam pp,String field,String tableId){
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        if(pp.getSortField()!=null&&!pp.getSortField().isEmpty()){
            if(pp.getSortType()!=null&&pp.getSortType().equals(descValue)){
                wrapper.last("order by "+ CamelCaseUtil.camel2Underline(pp.getSortField()) +" desc,"+tableId+" desc");
            }else{
                wrapper.last("order by "+ CamelCaseUtil.camel2Underline(pp.getSortField()) +" ,"+tableId+" desc");
            }
        }else{
            wrapper.orderByDesc(field).orderByDesc(tableId);
        }
        return this.generateParam(wrapper,pp);
    }

    public QueryWrapper<T> generateAsc(PageParam pp,String field,String tableId){
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        if(pp.getSortField()!=null&&!pp.getSortField().isEmpty()){
            if(pp.getSortType()!=null&&pp.getSortType().equals(descValue)){
                wrapper.last("order by "+ CamelCaseUtil.camel2Underline(pp.getSortField()) +" asc,"+tableId+" asc");
            }else{
                wrapper.last("order by "+ CamelCaseUtil.camel2Underline(pp.getSortField()) +" ,"+tableId+" asc");
            }
        }else{
            wrapper.orderByAsc(field).orderByAsc(tableId);
        }
        return this.generateParam(wrapper,pp);
    }

    public QueryWrapper<T> generateParam(QueryWrapper<T> wrapper,Object pageObject) {
        Field[] fields = pageObject.getClass().getDeclaredFields();
        for(Field field : fields) {
            field.setAccessible(true);
            String fieldName = field.getName();
            switch (fieldName){
                case onlyMe: continue;
                default: this.addParam(wrapper, pageObject,field);;break;
            }
        }

        Field[] superFields = pageObject.getClass().getSuperclass().getDeclaredFields();
        for(Field field : superFields) {
            field.setAccessible(true);
            String fieldName = field.getName();
            switch (fieldName){
                case pageField:
                case sizeField:
                case sortField:
                case sortType:
                case query:
                case queryFields: continue;
                case queries:
                    Object fieldValue = ReflectUtil.getValue(pageObject, fieldName);
                    if(fieldValue == null) {continue;}
                    addQuery(wrapper,pageObject, ReflectUtil.getValue(pageObject, fieldName));break;
                default: addParam(wrapper, pageObject,field);break;
            }
        }
        return wrapper;
    }

    @SneakyThrows
    public void addParam(QueryWrapper<T> wrapper, Object pageObject, Field field){
        if(this.isIgnoreQuery(field)){return;}
        String fieldName = field.getName();
        Object fieldValue = ReflectUtil.getValue(pageObject,fieldName);
        if(fieldValue==null){return;}
        Class<?> classType = fieldValue.getClass();
        if(List.class.isAssignableFrom(classType) || classType.newInstance() instanceof List){
            int size = ReflectUtil.getListSize(fieldValue);
            if(size==0){return;}
            Object item = ReflectUtil.getItemValue(fieldValue,0);
            if(item==null){return;}
            if(item.getClass().isEnum()){
                List<String> codeList = getCodeList(fieldValue,size);
                wrapper.in(this.getTableField(field),codeList);
            }else if(Date.class.isAssignableFrom(item.getClass())){
                // 长度为2再处理，表示时间段
                if(size==2){
                    Date startDate = (Date) ReflectUtil.getItemValue(fieldValue,0);
                    Date endDate = (Date) ReflectUtil.getItemValue(fieldValue,1);
                    SimpleDateFormat simpleDateFormat =new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    wrapper.ge(CamelCaseUtil.camel2Underline(fieldName), simpleDateFormat.format(startDate));
                    wrapper.le(CamelCaseUtil.camel2Underline(fieldName), simpleDateFormat.format(endDate));
                }
            }else{
                wrapper.and(w->{
                    for(int i=0;i<size;i++){
                        Object value = ReflectUtil.getItemValue(fieldValue,i);

                        boolean isPreciseQuery = this.isPreciseQuery(field);
                        if(i>0){
                            if(!isPreciseQuery){
                                w.or().like(this.getTableField(field),value);
                            }
                        }else{
                            if(isPreciseQuery){
                                List<Object> itemList = getItemList(fieldValue,size);
                                w.in(this.getTableField(field), itemList);
                            }else{
                                w.like(this.getTableField(field),value);
                            }
                        }
                    }
                });
            }
        }
    }

    @SneakyThrows
    public void addQuery(QueryWrapper<T> wrapper, Object pageObject, Object queries){
        Field[] tFields = this.clazz.getDeclaredFields();
//        Field[] baseEntityFields = BaseEntity.class.getFields();

        Field[] paramFields = pageObject.getClass().getDeclaredFields();
        List<Field> list = new ArrayList<>();
        boolean isCommonQuery = this.isCommonQuery(tFields);
        JSONArray arr = JSON.parseArray(JSON.toJSONString(queries));
        if(arr.size()==0){return;}
        Object queryFieldList = ReflectUtil.getValue(pageObject, this.queryFields);
        JSONArray queryFieldArray = JSON.parseArray(JSON.toJSONString(queryFieldList));

        wrapper.and(w->{
            for (Object o : arr) {

                for (Field field : paramFields) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    boolean isSkip = fieldName.toLowerCase().equals("id")
                            || fieldName.toLowerCase().contains("deleted")
                            || fieldName.toLowerCase().contains("bizid");
                    if (isSkip) {continue;}

                    for (Field tField : tFields) {
                        if (tField.getType().equals(Date.class)) {
                            continue;
                        }
                        tField.setAccessible(true);
                        String tFieldName = tField.getName();
                        if (tFieldName.equals(fieldName)) {
                            if(isCommonQuery){
                                // 普通查询，直接添加该字段为查询字段
                                list.add(tField);
                            }else{
                                // 指定高亮查询
                                if(queryFieldArray.isEmpty()){
                                    // 前端未指定查询字段，根据配置注解字段添加
                                    if(tField.isAnnotationPresent(HighLightField.class)){
                                        list.add(tField);
                                    }
                                }else{
                                    // 前端指定查询字段添加
                                    if(queryFieldArray.contains(tFieldName)){
                                        list.add(tField);
                                    }
                                }
                            }
                        }
                    }

//                    for (Field baseEntityField : baseEntityFields) {
//                        if (baseEntityField.getType().equals(Date.class)) {
//                            continue;
//                        }
//                        baseEntityField.setAccessible(true);
//                        String baseEntityFieldName = baseEntityField.getName();
//                        if (baseEntityFieldName.equals(fieldName)) {
//                            if(queryFieldArray.size()==0){
//                                if(baseEntityField.isAnnotationPresent(HighLightField.class)){
//                                    list.add(baseEntityField);
//                                }
//                            }else{
//                                if(queryFieldArray.contains(baseEntityFieldName)){
//                                    list.add(baseEntityField);
//                                }
//                            }
//
//                        }
//                    }
                }

                String queryItem = o.toString();
                w.and(f -> {
                    list.forEach(item -> {
                        f.or().like(CamelCaseUtil.camel2Underline(item.getName()), queryItem);
                    });
                });
            }
        });
    }

    private String getIdField(){
        Field[] tFields = this.clazz.getDeclaredFields();
        List<Field> fields = Arrays.stream(tFields).collect(Collectors.toList());

        for (Field field : fields){
            if(field.isAnnotationPresent(TableId.class)){
                return field.getAnnotation(TableId.class).value();
            }
        }
        return "id";
    }

    private boolean isPreciseQuery(Field field){
        String fieldName = field.getName();

        // 根据字段名字，粗略判断是否为模糊查询
        boolean isPreciseField = fieldName.toLowerCase().contains("id")
                || fieldName.toLowerCase().contains("deleted")
                || fieldName.toLowerCase().contains("status")
                || fieldName.toLowerCase().contains("type");

        boolean isPreciseQuery = field.isAnnotationPresent(PreciseQuery.class);
        boolean isFuzzyQuery = field.isAnnotationPresent(FuzzyQuery.class);

        // 如果有精确查询注解，则使用精确查询，返回true
        if(isPreciseQuery){return true;}
        // 如果没有精确查询注解但有模糊查询注解，则使用模糊查询，返回false
        if(isFuzzyQuery){return false;}
        // 如果没有注解，根据字段名字判断，如果是id、deleted类型，则使用精确查询，否则使用模糊查询
        return isPreciseField;
    }

    /**
     * 判断本次列表查询模式，如果没有任意字段标注HighLightField注解，则为普通查询，否则为指定高亮查询
     * 普通查询：查询除id、deleted、Date类型外的所有字段
     * 指定高亮查询：只查询标注了HighLightField注解字段或前端指定字段的查询
     * @param tFields 实体类的字段数组
     * @return 是否为普通查询的判断值
     */
    private boolean isCommonQuery(Field[] tFields){
        for (Field tField : tFields) {
            if(tField.isAnnotationPresent(HighLightField.class)){
                return false;
            }
        }
        return true;
    }

    private String getTableField(Field field){
        if(field.isAnnotationPresent(TableFieldName.class)){
            TableFieldName tfName = field.getAnnotation(TableFieldName.class);
            return tfName.value();
        }
        return CamelCaseUtil.camel2Underline(field.getName());
    }

    private boolean isIgnoreQuery(Field field){
        return field.isAnnotationPresent(IgnoreQuery.class);
    }

    /**
     * 通过反射,获得定义Class时声明的父类的范型参数的类型. 如public BookManager extends GenricManager<Book>
     *
     * @param clazz clazz The class to introspect
     * @param index the Index of the generic ddeclaration,start from 0.
     */
    public Class getSuperClassGenricType(Class clazz, int index)
            throws IndexOutOfBoundsException {
        Type genType = clazz.getGenericSuperclass();
        if (!(genType instanceof ParameterizedType)) {
            return Object.class;
        }
        Type[] params = ((ParameterizedType) genType).getActualTypeArguments();
        if (index >= params.length || index < 0) {
            return Object.class;
        }
        if (!(params[index] instanceof Class)) {
            return Object.class;
        }
        return (Class) params[index];
    }

    public List<Object> getItemList(Object fieldValue,int size){
        List<Object> list = new ArrayList<>();
        for(int i=0;i<size;i++){
            Object item = ReflectUtil.getItemValue(fieldValue,i);
            list.add(item);
        }
        return list;
    }

    public List<String> getCodeList(Object fieldValue,int size){
        List<String> codeList = new ArrayList<>();
        for(int i=0;i<size;i++){
            Object item = ReflectUtil.getItemValue(fieldValue,i);
            codeList.add(Objects.requireNonNull(ReflectUtil.getValue(item, "code")).toString());
        }
        return codeList;
    }

    public QueryWrapper<T> generateById(String id){
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        return this.addEqId(wrapper,id);
    }

    private QueryWrapper<T> addEqId(QueryWrapper<T> wrapper,String id){
        wrapper.eq("id",id);
        return wrapper;
    }
}
