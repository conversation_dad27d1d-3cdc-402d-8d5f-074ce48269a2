package ttfund.web.financialplannerapi.mybatis.sort;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import ttfund.web.financialplannerapi.mybatis.util.SortParam;
import ttfund.web.financialplannerapi.util.context.SpringUtils;
import ttfund.web.financialplannerapi.util.other.CamelCaseUtil;
import ttfund.web.financialplannerapi.util.other.ReflectUtil;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
public class SortUtil<T> {

    private Class<T> clazz;
    private String groupField;
    private Boolean isOrderByIdAes;

    public SortUtil(Class<T> clazz, String groupField){
        this.clazz = clazz;
        this.groupField = groupField;
        this.isOrderByIdAes = true;
    }

    @SneakyThrows
    public void sort(Class<?> serviceClazz, SortParam param){
        Object service = SpringUtils.getBean(serviceClazz);

        Object current = ReflectUtil.getById(service,param.getCurrentId());
        Object target = ReflectUtil.getById(service,param.getTargetId());

        assert current != null;
        assert target != null;

        Object fieldValue = null;
        if(this.groupField!=null){
            //如果两个数据的分组字段不相同，则拒绝排序
            if(!Objects.equals(ReflectUtil.getValue(current, groupField),
                    ReflectUtil.getValue(target, groupField))){
                return;
            }
            fieldValue = ReflectUtil.getValue(current,groupField);
        }

        Integer currentSort = (Integer) ReflectUtil.getValue(current,"sort");
        Integer targetSort = (Integer) ReflectUtil.getValue(target,"sort");
        Long currentId = (Long) ReflectUtil.getValue(current,"id");
        Long targetId = (Long) ReflectUtil.getValue(target,"id");

        assert currentSort != null;
        assert targetSort != null;
        assert currentId != null;
        assert targetId != null;

        boolean isMoveUp = this.getMoveSort(currentSort,targetSort,currentId,targetId);
        QueryWrapper<T> wrapper = new QueryWrapper<>();

        if(this.groupField!=null){
            wrapper.eq(CamelCaseUtil.camel2Underline(groupField),fieldValue);
        }
        wrapper.ne("id",currentId);

        if (isMoveUp){
            if(currentSort.equals(targetSort)){
                if(this.isOrderByIdAes){
                    wrapper.and(i->
                            i.gt("sort",targetSort).and(t->t.lt("sort",currentSort))
                                    .or().eq("sort",targetSort).and(t->t.ge("id",targetId))
                    );
                }else{
                    wrapper.and(i->
                            i.gt("sort",targetSort).and(t->t.lt("sort",currentSort))
                                    .or().eq("sort",targetSort).and(t->t.le("id",targetId))
                    );
                }
            }else{
                wrapper.ge("sort",targetSort).and(t->t.le("sort",currentSort));
            }
        }else {
            if(currentSort.equals(targetSort)){
                if(this.isOrderByIdAes){
                    wrapper.and(i->
                            i.gt("sort",currentSort).and(t->t.lt("sort",targetSort))
                                    .or().eq("sort",targetSort).and(t->t.le("id",targetId))
                    );
                }else{
                    wrapper.and(i->
                            i.gt("sort",currentSort).and(t->t.lt("sort",targetSort))
                                    .or().eq("sort",targetSort).and(t->t.ge("id",targetId))
                    );
                }
            }else{
                wrapper.ge("sort",currentSort).and(t->t.le("sort",targetSort));
            }
        }

        Object listInfo = this.list(service,wrapper);
        ReflectUtil.setValue(current,"sort",Integer.class,targetSort);

        Class<?> classType = listInfo.getClass();
        if(List.class.isAssignableFrom(classType) || classType.newInstance() instanceof List){
            int size = ReflectUtil.getListSize(listInfo);
            if(size==0){return;}
            for(int i=0;i<size;i++){
                Object item = ReflectUtil.getItemValue(listInfo,i);
                Integer sort = (Integer) ReflectUtil.getValue(item,"sort");
                assert sort != null;
                if(isMoveUp){
                    ReflectUtil.setValue(item,"sort",Integer.class,sort+1);
                }else{
                    ReflectUtil.setValue(item,"sort",Integer.class,sort-1);
                }
            }
        }
        ReflectUtil.add(listInfo,current);
        this.updateBatchById(service,listInfo);
    }

    public void topping(Class<?> serviceClazz,Long id){
        Object service = SpringUtils.getBean(serviceClazz);
        Object entity = ReflectUtil.getById(service,id);
        assert entity != null;
        String groupValue = null;
        if(this.groupField!=null){
            groupValue = (String) ReflectUtil.getValue(entity,this.groupField);
        }
        int minSort = this.getMinSort(serviceClazz,groupValue) - 1;
        ReflectUtil.setValue(entity,"sort",Integer.class,minSort);
        ReflectUtil.updateById(service,entity);
    }

    public void ending(Class<?> serviceClazz,Long id){
        Object service = SpringUtils.getBean(serviceClazz);
        Object entity = ReflectUtil.getById(service,id);
        assert entity != null;
        String groupValue = null;
        if(this.groupField!=null){
            groupValue = (String) ReflectUtil.getValue(entity,this.groupField);
        }
        int maxSort = this.getMaxSort(serviceClazz,groupValue) + 1;
        ReflectUtil.setValue(entity,"sort",Integer.class,maxSort);
        ReflectUtil.updateById(service,entity);
    }

    public Integer getMaxSort(Class<?> serviceClazz,String groupValue){
        Object service = SpringUtils.getBean(serviceClazz);
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        wrapper.select("max(sort) as sort");
        if(this.groupField!=null){
            wrapper.eq(CamelCaseUtil.camel2Underline(groupField) , groupValue);
        }
        Object entity = this.getOne(service,wrapper);
        if(entity==null){return 0;}
        Object sort =  ReflectUtil.getValue(entity,"sort");
        if(sort==null){return 0;}
        return (Integer) ReflectUtil.getValue(entity,"sort");
    }

    public Integer getMinSort(Class<?> serviceClazz,String groupValue){
        Object service = SpringUtils.getBean(serviceClazz);
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        wrapper.select("min(sort) as sort");
        if(this.groupField!=null){
            wrapper.eq(CamelCaseUtil.camel2Underline(groupField) , groupValue);
        }
        Object entity = this.getOne(service,wrapper);
        if(entity==null){return 0;}
        Object sort =  ReflectUtil.getValue(entity,"sort");
        if(sort == null){return 0;}
        return (Integer) ReflectUtil.getValue(entity,"sort");
    }

    private boolean getMoveSort(Integer currentSort, Integer targetSort,Long currentId, Long targetId){
        if(currentSort.equals(targetSort)){
            if(this.isOrderByIdAes){
                return currentId>targetId;
            }else{
                return currentId<=targetId;
            }

        }
        return currentSort > targetSort;
    }

    @SneakyThrows
    public Object list(Object o, Wrapper<T> queryWrapper){
        Method listMethod = o.getClass().getMethod("list",Wrapper.class);
        return listMethod.invoke(o,queryWrapper);
    }

    @SneakyThrows
    public Object getOne(Object o, Wrapper<T> queryWrapper){
        Method listMethod = o.getClass().getMethod("getOne",Wrapper.class);
        return listMethod.invoke(o,queryWrapper);
    }

    @SneakyThrows
    public Object updateBatchById(Object o,Object list){
        Method updateBatch = o.getClass().getMethod("updateBatchById", Collection.class);
        return updateBatch.invoke(o,list);
    }
}
