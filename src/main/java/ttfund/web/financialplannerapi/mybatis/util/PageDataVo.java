package ttfund.web.financialplannerapi.mybatis.util;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="分页数据对象")
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PageDataVo<T> {

    private List<T> rows;
    private Long total;
}
