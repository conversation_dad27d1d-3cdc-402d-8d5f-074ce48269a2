package ttfund.web.financialplannerapi.mybatis.util;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import ttfund.web.financialplannerapi.mybatis.group.SortGroup;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SortParam对象",description = "排序参数")
public class SortParam {

    @ApiModelProperty("当前数据排序")
    @NotNull(message = "当前数据id不可为null；")
    private Long currentId;

    @ApiModelProperty("目标数据排序")
    @NotNull(groups = SortGroup.class,message = "目标数据id不可为null；")
    private Long targetId;
}
