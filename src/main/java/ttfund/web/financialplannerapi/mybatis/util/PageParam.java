package ttfund.web.financialplannerapi.mybatis.util;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(value="PageParam对象",description = "分页参数")
public class PageParam extends AuthBaseRequest {

    @ApiModelProperty(value = "页码",required = true)
    @Min(value = 1,message = "页码(page)至少为1;")
    private long page = 1;

    @ApiModelProperty(value = "条数",required = true)
    @Min(value = 1,message = "条数(size)至少为1;")
    private long size = 999999;

    @ApiModelProperty(value = "排序字段")
    private String sortField;

    @ApiModelProperty(value = "排序方式")
    private String sortType;

    @ApiModelProperty("id")
    private List<String> id;

//    @ApiModelProperty("bizId")
//    private List<String> bizId;
//
//    @ApiModelProperty("状态")
//    private List<StatusEnum> status;

    @ApiModelProperty("是否gbk排序")
    private boolean useGbk;

    @ApiModelProperty("通用查询")
    private String query;

    @ApiModelProperty(value = "指定查询并高亮的字段")
    private List<String> queryFields = new ArrayList<>();

    @ApiModelProperty("通用查询多字段")
    private List<String> queries;

    @ApiModelProperty("逻辑删除字段")
    private List<Integer> deleted = Stream.of(0).collect(Collectors.toList());

    public void setAllMode(){
        this.page = 1;
        this.size = 999999;
    }

    public void initApproved(){
//        if(this.status == null){
//            this.status = Stream.of(StatusEnum.APPROVED).collect(Collectors.toList());
//        }
        this.init();
    }

    public void init(){
        this.initQuery();
        this.initSortField();
        this.initDeleted();
    }

    public void initByFull(){
        this.initQuery();
        this.initSortField();
    }

    private void initQuery(){
        if(this.query==null||this.query.isEmpty()){return;}
        this.queries = Arrays.stream(this.query.split("\\s+")).filter(i->!i.isEmpty()).collect(Collectors.toList());
    }

    private void initSortField(){
        if(this.sortField==null||this.sortField.isEmpty()){return;}
        this.sortField = camel2Underline(this.sortField);
    }

    private void initDeleted(){
        this.deleted = Stream.of(0).collect(Collectors.toList());
    }

    public void setAllData(){
        this.deleted = Stream.of(0,1).collect(Collectors.toList());
    }

    private String camel2Underline(String str) {
        str = String.valueOf(str.charAt(0)).toLowerCase().concat(str.substring(1));
        Pattern compile = Pattern.compile("[A-Z]");
        Matcher matcher = compile.matcher(str);
        StringBuffer sb = new StringBuffer();
        while(matcher.find()) {
            matcher.appendReplacement(sb,  "_" + matcher.group(0).toLowerCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

}
