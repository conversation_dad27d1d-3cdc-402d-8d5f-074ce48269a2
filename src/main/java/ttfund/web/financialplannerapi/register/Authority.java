package ttfund.web.financialplannerapi.register;

import java.lang.annotation.*;

/**
 * 角色和权限可以只传一个也可以两个都传
 * <AUTHOR>
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Authority {
    /**
     * 验证角色
     *
     * @return
     */
    int role() default 0;
    /**
     * 验证权限
     *
     * @return
     */
    String authority() default "";
}