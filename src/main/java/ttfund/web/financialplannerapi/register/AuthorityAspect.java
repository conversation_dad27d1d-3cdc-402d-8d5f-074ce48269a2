package ttfund.web.financialplannerapi.register;

import com.ttfund.web.core.constant.ResultCode;
import com.ttfund.web.core.model.ApiResponse;
import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import ttfund.web.financialplannerapi.config.App;
import ttfund.web.financialplannerapi.model.permission.PermissionModel;
import ttfund.web.financialplannerapi.service.impl.UserRoleMysqlImpl;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Order(200)
public class AuthorityAspect {

    @Around("@annotation(Authority)")
    public Object AroundClass(ProceedingJoinPoint joinPoint) throws Throwable {
        return valid(joinPoint);
    }

    @Around("@within(Authority)")
    public Object AroundMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        return valid(joinPoint);
    }

    @Autowired
    private App app;

    @Autowired
    UserRoleMysqlImpl roleMysql;

    public Object valid(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = null;
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Object[] param = joinPoint.getArgs();
        AuthBaseRequest p1 = null;
        if (param != null && param.length > 0) {
            p1 = (AuthBaseRequest) param[0];
        }
        String passportId = p1.getPassportid();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        // 获取当前方法注解中的值
        Authority annotation = method.getAnnotation(Authority.class);
        // 如果方法上面没有注解，则获取类的注解
        if (annotation == null) {
            annotation = joinPoint.getTarget().getClass().getAnnotation(Authority.class);
        }
        //角色验证结果
        Boolean role = false;
        //权限验证结果
        Boolean authority = false;
        Map<String, Object> permissionMap = roleMysql.selectRole(passportId);
        PermissionModel permission = null;
        //验证角色
        if (annotation.role() > 0) {
            permission = authenticateRole(permissionMap,annotation.role());
            role = permission != null;
        }else {
            //不验证角色
            role = true;
        }
        //验证权限
        if (StringUtils.isNotEmpty(annotation.authority())) {
            permission = authenticateAuthority(permissionMap, annotation.authority());
            authority = permission != null && permission.getDataType() > 0 ? true : false;
        }else {
            authority = true;
        }

        if (Objects.isNull(permission)) {
            permission =  authenticateCommon(permissionMap);
        }
        //角色和权限都通过则算通过
        if (role && authority) {
            request.setAttribute("dataType", permission.getDataType());
            request.setAttribute("role", permission.getRole());
            request.setAttribute("institutionId", permission.getInstitutionId());
            result = joinPoint.proceed(joinPoint.getArgs());
        } else {
            ApiResponse<Object, ?> re2 = new ApiResponse<>();
            re2.setErrorCode(ResultCode.errorcode502);
            re2.setFirstError("无权限访问");
            result = re2;
        }
        return result;
    }

    /**
     * 认证权限
     * @param permissionMap
     * @param authority
     * @return
     */
    private PermissionModel authenticateAuthority(Map<String, Object> permissionMap, String authority) {
        PermissionModel permission = null;
        if (permissionMap!=null && !permissionMap.isEmpty()) {
            Integer dataType = (Integer) permissionMap.getOrDefault(authority, 0);
            if (dataType != null) {
                permission = new PermissionModel();
                permission.setDataType(dataType);
                permission.setInstitutionId((String) permissionMap.get("institution_id"));
                permission.setRole((Integer) permissionMap.get("role"));
            }
        }
        return permission;
    }

    /**
     * 验证角色
     * @param permissionMap
     * @param role
     * @return
     */
    private PermissionModel authenticateRole(Map<String, Object> permissionMap, int role) {
        PermissionModel permission = null;
        if (permissionMap!=null && !permissionMap.isEmpty()) {
            Integer roleDb = (Integer) permissionMap.get("role");
            if (roleDb != null && roleDb.equals(role)) {
                permission = new PermissionModel();
                permission.setDataType(0);
                permission.setInstitutionId((String) permissionMap.get("institution_id"));
                permission.setRole(roleDb);
            }
        }
        return permission;
    }

    /**
     * 验证角色
     * @param permissionMap
     * @return
     */
    private PermissionModel authenticateCommon(Map<String, Object> permissionMap) {
        // 默认为普通用户
        PermissionModel permission = new PermissionModel();
        permission.setDataType(0);
        permission.setInstitutionId("");
        permission.setRole(0);
        if (permissionMap!=null && !permissionMap.isEmpty()) {
            Integer roleDb = (Integer) permissionMap.get("role");
            if (roleDb != null) {
                permission.setInstitutionId((String) permissionMap.get("institution_id"));
                permission.setRole(roleDb);
            }
        }
        return permission;
    }
}
