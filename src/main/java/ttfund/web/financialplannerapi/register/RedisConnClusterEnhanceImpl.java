package ttfund.web.financialplannerapi.register;

import com.ttfund.web.base.Constant;
import com.ttfund.web.base.model.basemodel.RedisConnModel;
import com.ttfund.web.base.redishelper.impl.RedisConnClusterImpl;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.JedisCluster;

import java.util.List;

/**
 * @Auhor lu
 * @Date 2021/5/31 13:39
 */
public class RedisConnClusterEnhanceImpl extends RedisConnClusterImpl implements IRedisEnhance {
    private static final Logger logger = LoggerFactory.getLogger(RedisConnClusterImpl.class);

    public RedisConnClusterEnhanceImpl(List<RedisConnModel> connlist) {
        super(connlist);
    }

    public RedisConnClusterEnhanceImpl(List<RedisConnModel> connlist, GenericObjectPoolConfig poolConfig) {
        super(connlist,poolConfig);
    }


    @Override
    public Long hdel(String key, String... fields) {
        JedisCluster db = getJedis();
        try {
            return db.hdel(key,fields);
        } catch (Exception e) {
            logger.error(String.format(Constant.LOGERRORFORMAT2, key, e.getMessage()), e);
        }
        return null;
    }
}
