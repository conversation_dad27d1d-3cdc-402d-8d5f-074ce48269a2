package ttfund.web.financialplannerapi.register;

import com.ttfund.web.base.model.basemodel.RedisConnModel;
import com.ttfund.web.base.redishelper.impl.RedisConnSentinelImpl;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

import java.util.List;

/**
 * @Author: wrs
 * @Date: 2023-06-26 17:00
 * @Description:
 **/
public class RedisConnSentinelEnhanceImpl extends RedisConnSentinelImpl implements IRedisEnhance{

    private static final Logger logger = LoggerFactory.getLogger(RedisConnSentinelImpl.class);

    public RedisConnSentinelEnhanceImpl(String name, List<RedisConnModel> connlist) {
       super(name, connlist);
    }

    public RedisConnSentinelEnhanceImpl(String name, List<RedisConnModel> connlist, GenericObjectPoolConfig poolConfig) {
        super(name,connlist,poolConfig);
    }


    @Override
    public Long hdel(String key, String... fields) {
        Long result = 0L;
        Jedis instance = getResource();
        try {
            result = instance.hdel(key, fields);
        } catch (Exception e) {
            logger.error(key, e);
        } finally {
            if (instance != null) {
                instance.close();
            }
        }
        return result;
    }
}
