package ttfund.web.financialplannerapi.register;

import com.alibaba.fastjson.JSON;
import com.ttfund.web.base.Constant;
import com.ttfund.web.base.helper.IPUtils;
import com.ttfund.web.core.constant.CoreConstant;
import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import ttfund.web.financialplannerapi.config.App;
import ttfund.web.financialplannerapi.constant.AppConfigConstant;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

@Aspect
@Component
@Order(-1)
public class RequestLogAspectEnhance {
    @Autowired
    App app;

    @Value("${" + AppConfigConstant.LOG_TOPIC + ":lcs_log_notice}")
    private String logTopic;

    @Value("${" + AppConfigConstant.LOG_KAFKA_SWITCH + ":false}")
    private boolean sendLog;

    private static final Logger logger = LoggerFactory.getLogger(RequestLogAspectEnhance.class);

    public static ThreadLocal<Boolean> sendKafkaLog = new ThreadLocal<>();

    @Around("@annotation(ttfund.web.financialplannerapi.register.RequestLogPro)")
    public Object AroundClass(ProceedingJoinPoint joinPoint) throws Throwable {
        return logWrite(joinPoint, true);
    }

    @Around("@within(ttfund.web.financialplannerapi.register.RequestLogPro)")
    public Object AroundMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        return logWrite(joinPoint, false);
    }

    private Object logWrite(ProceedingJoinPoint joinPoint, boolean classAspect) throws Throwable {
        Object result = null;
        Object[] param = joinPoint.getArgs();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取当前方法注解中的值
        RequestLogPro annotation = method.getAnnotation(RequestLogPro.class);

        // 类和方法都有注解时，只执行方法上的
        if (classAspect && annotation != null) {
            return joinPoint.proceed(joinPoint.getArgs());
        }

        // 如果方法上面没有注解，则获取类的注解
        if (annotation == null) {
            annotation = joinPoint.getTarget().getClass().getAnnotation(RequestLogPro.class);
        }
        sendKafkaLog.set(false);
        long execStartTime = System.currentTimeMillis();
        result = joinPoint.proceed(joinPoint.getArgs());
        long execEndTime = System.currentTimeMillis();


        try {
            String p1 = signature.getDeclaringTypeName();
            String p2 = signature.getName();
            String p3 = IPUtils.getIp();
            String p4 = annotation.isrequestparam() == 0 ? JSON.toJSONString(param[0]) : "";
            String p5 = annotation.isresponseparam() == 0 ? JSON.toJSONString(result) : "";
            Long p6 = execEndTime - execStartTime;

            MDC.put(CoreConstant.logexectime, p6.toString());
            logger.info(String.format(Constant.LOGAOPFORMAT6, p1, p2, p3, p4, p5, p6));
            MDC.remove(CoreConstant.logexectime);
            if (Boolean.TRUE.equals(sendKafkaLog.get()) && sendLog) {
                //推送请求日志
                Map<String, Object> map = new HashMap<>();
                map.put("request", param[0]);
                map.put("response", result);
                map.put("requestip", p3);
                map.put("exectime", p6);
                HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
                map.put("url", request.getRequestURL());
                AuthBaseRequest auth = (AuthBaseRequest) param[0];
                String passportid = auth == null ? "" : auth.getPassportid();
                map.put("operator_passportid", passportid);
                app.kafkaProducerHelper.produce(logTopic, MDC.get("traceid"),JSON.toJSONString(map));
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            sendKafkaLog.remove();
        }
        return result;
    }
}
