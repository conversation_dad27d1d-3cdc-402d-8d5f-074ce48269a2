package ttfund.web.financialplannerapi.register;

import com.ttfund.web.base.model.basemodel.RedisConnModel;
import com.ttfund.web.base.redishelper.impl.RedisConnPoolImpl;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

import java.util.List;

/**
 * @Author: wrs
 * @Date: 2023-06-26 16:26
 * @Description:
 **/
public class RedisConnPoolEnhanceImpl extends RedisConnPoolImpl implements IRedisEnhance {
    private static final Logger logger = LoggerFactory.getLogger(RedisConnPoolImpl.class);

    public RedisConnPoolEnhanceImpl(List<RedisConnModel> connlist) {
        super(connlist);
    }

    public RedisConnPoolEnhanceImpl(List<RedisConnModel> connlist, GenericObjectPoolConfig poolConfig) {
        super(connlist,poolConfig);
    }


    @Override
    public Long hdel(String key, String... fields) {
        Jedis instance = this.getResource();

        try {
            Long var3 = instance.hdel(key, fields);
            return var3;
        } catch (Exception var7) {
            logger.error(key, var7);
        } finally {
            if (instance != null) {
                instance.close();
            }

        }

        return null;
    }
}
