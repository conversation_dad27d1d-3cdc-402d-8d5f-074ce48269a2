package ttfund.web.financialplannerapi.register;

import com.ttfund.web.base.helper.RSAUtils;
import com.ttfund.web.base.model.basemodel.RedisConfigModel;
import com.ttfund.web.base.redishelper.RedisUtils;
import com.ttfund.web.base.redishelper.impl.RedisConnImpl;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLDecoder;
import java.util.List;
import java.util.Map;

/**
 * @Author: wrs
 * @Date: 2023-06-26 16:22
 * @Description:
 **/
public class RedisConnEnhanceImpl implements IRedisEnhance{

    private static final Logger logger = LoggerFactory.getLogger(RedisConnImpl.class);
    public String connstr = null;
    public int conntype = 0;
    private String privateKey;
    public IRedisEnhance db = null;
    public GenericObjectPoolConfig poolconfig;

    /** @deprecated */
    @Deprecated
    public RedisConnEnhanceImpl(String connstr) {
        this.connstr = connstr;
        this.load();
    }

    /** @deprecated */
    public RedisConnEnhanceImpl(String connstr, GenericObjectPoolConfig poolconfig) {
        this.connstr = connstr;
        this.poolconfig = poolconfig;
        this.load();
    }

    /** @deprecated */
    public RedisConnEnhanceImpl(String connstr, int connmaxnum, int maxwaitmillis) {
        this.connstr = connstr;
        this.poolconfig = RedisUtils.getJedisPoolConfigDefault();
        this.poolconfig.setMaxTotal(connmaxnum);
        this.poolconfig.setMaxWaitMillis((long)maxwaitmillis);
        this.load();
    }

    public RedisConnEnhanceImpl(String connStrEncrypted, String privateKey) {
        this.connstr = this.decodeAndDecryptUrl(connStrEncrypted, privateKey);
        this.conntype = 1;
        this.privateKey = privateKey;
        this.load();
    }

    public RedisConnEnhanceImpl(String connStrEncrypted, GenericObjectPoolConfig poolConfig, String privateKey) {
        this.connstr = this.decodeAndDecryptUrl(connStrEncrypted, privateKey);
        this.conntype = 1;
        this.privateKey = privateKey;
        this.poolconfig = poolConfig;
        this.load();
    }

    public RedisConnEnhanceImpl(String connStrEncrypted, int connMaxNum, int maxWaitMillis, String privateKey) {
        this.connstr = this.decodeAndDecryptUrl(connStrEncrypted, privateKey);
        this.conntype = 1;
        this.privateKey = privateKey;
        this.poolconfig = RedisUtils.getJedisPoolConfigDefault();
        this.poolconfig.setMaxTotal(connMaxNum);
        this.poolconfig.setMaxWaitMillis((long)maxWaitMillis);
        this.load();
    }

    private String decodeAndDecryptUrl(String connStrEncrypted, String privateKey) {
        String[] prefixSplit = connStrEncrypted.split("@")[0].split(",");
        String passwordEncodedAndEncrypted = prefixSplit[prefixSplit.length - 1];

        try {
            String password = RSAUtils.decryptByPrivateKey(URLDecoder.decode(passwordEncodedAndEncrypted, "UTF-8"), privateKey);
            return connStrEncrypted.replace(passwordEncodedAndEncrypted, password);
        } catch (Exception var6) {
            logger.error("decrypt error", var6);
            return connStrEncrypted;
        }
    }
    @Override
    public void close() {
        this.db.close();
    }

    public void load() {
        RedisConfigModel configmodel = RedisUtils.getRedisConnModel(this.connstr);
        switch(configmodel.getType()) {
            case 1:
                this.db = new RedisConnPoolEnhanceImpl(configmodel.getHostlist(), this.poolconfig);
                break;
            case 2:
                this.db = new RedisConnSentinelEnhanceImpl(configmodel.getName(), configmodel.getHostlist(), this.poolconfig);
                break;
            case 3:
                this.db = new RedisConnClusterEnhanceImpl(configmodel.getHostlist(), this.poolconfig);
        }

    }
    @Override
    public void reload(String connstr) {
        if (this.conntype == 0) {
            this.connstr = connstr;
        } else {
            this.connstr = this.decodeAndDecryptUrl(connstr, this.privateKey);
        }

        this.close();
        this.load();
    }

    public String get(String key) {
        return this.db.get(key);
    }

    public String get(String key, boolean isthrow) {
        return this.db.get(key, isthrow);
    }

    public Long expire(String key, long secound) {
        return this.db.expire(key, secound);
    }

    public Map<String, String> getAll(List<String> keys) {
        return this.db.getAll(keys);
    }

    public Map<String, String> getAll(List<String> keys, boolean isthrow) {
        return this.db.getAll(keys, isthrow);
    }

    public Map<String, String> hgetAll(String key) {
        return this.db.hgetAll(key);
    }

    public String hmset(String key, Map<String, String> map) {
        return this.db.hmset(key, map);
    }

    public Long hSet(String key, String filed, String value) {
        return this.db.hSet(key, filed, value);
    }

    public Boolean set(String key, String value) {
        return this.db.set(key, value);
    }

    public Boolean set(String key, String value, Long seconds) {
        return this.db.set(key, value, seconds);
    }

    public Long incrBy(String key, Long value, Long seconds) {
        return this.db.incrBy(key, value, seconds);
    }

    public Long decrBy(String key, Long value, Long seconds) {
        return this.db.decrBy(key, value, seconds);
    }

    public Boolean del(String key) {
        return this.db.del(key);
    }

    public List<String> lrange(String key, Long startoffset, Long endoffset) {
        return this.db.lrange(key, startoffset, endoffset);
    }

    public Long rpush(String key, String[] list) {
        return this.db.rpush(key, list);
    }

    public Boolean checkIsOk() {
        return this.db.checkIsOk();
    }

    public Long hIncreaseBy(String key, String filed, Long value) {
        return this.db.hIncreaseBy(key, filed, value);
    }

    public Long lGetLen(String key) {
        return this.db.lGetLen(key);
    }

    public String hGet(String key, String field) {
        return this.db.hGet(key, field);
    }

    public Long setnx(String key, String value) {
        return this.db.setnx(key, value);
    }

    public Long hsetnx(String key, String filed, String value) {
        return this.db.hsetnx(key, filed, value);
    }

    public List<String> hmget(String key, String... fields) {
        return this.db.hmget(key, fields);
    }


    @Override
    public Long hdel(String key, String... fields) {
        return this.db.hdel(key,fields);
    }
}
