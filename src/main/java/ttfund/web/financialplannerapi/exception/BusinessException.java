package ttfund.web.financialplannerapi.exception;

import lombok.Data;
import ttfund.web.financialplannerapi.constant.ErrorCodeEnum;

@Data
public class BusinessException extends Exception {

    private int errorCode;

    public BusinessException() {
    }

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(String message, int code) {
        super(message);
        this.errorCode = code;
    }

    public BusinessException(ErrorCodeEnum errorCodeEnum) {
        super(errorCodeEnum.getMsg());
        this.errorCode = errorCodeEnum.getCode();
    }
}
