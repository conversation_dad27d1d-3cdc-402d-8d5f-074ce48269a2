package ttfund.web.financialplannerapi.exception;

import lombok.Data;

@Data
public class TermFormatException extends Exception {

    private int errorCode;

    public TermFormatException() {
    }

    public TermFormatException(String message) {
        super(message);
    }

    public TermFormatException(String message,int code) {
        super(message);
        this.errorCode = code;
    }


}
