package ttfund.web.financialplannerapi.exception;

import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.ttfund.web.core.model.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.validation.ConstraintViolationException;
import java.lang.reflect.UndeclaredThrowableException;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static ttfund.web.financialplannerapi.constant.ErrorCodeEnum.*;

/**
 * @Author: wrs
 * @Date: 2023-05-19 17:25
 * @Description: 自定义异常处理
 **/
@RestControllerAdvice
@Slf4j
@Order(0)
public class CustomExceptionHandler {

    /**
     * 自定义异常处理器
     *
     * @param e 自定义异常BusinessException
     * @return ResultBean
     */
    @ExceptionHandler({BusinessException.class, UndeclaredThrowableException.class})
    public ResponseEntity<ApiResponse> handlerBusinessEx(BusinessException e) {
        int code = e.getErrorCode();
        String msg = e.getMessage();
        ApiResponse<Object, Object> response = new ApiResponse<>();
        response.setErrorCode(code);
        response.setFirstError(msg);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Content-Type", "application/json;charset=UTF-8");
        return new ResponseEntity(response, httpHeaders,HttpStatus.OK);
    }

    /**
     * 自定义异常处理器
     *
     * @param e 自定义异常BusinessException
     * @return ResultBean
     */
    @ExceptionHandler(InnerAuthException.class)
    public ResponseEntity<ApiResponse> handlerBusinessEx(InnerAuthException e) {
        String msg = e.getMessage();
        ApiResponse<Object, Object> response = new ApiResponse<>();
        response.setErrorCode(INNER_ACCESS_DENIED.getCode());
        response.setFirstError(INNER_ACCESS_DENIED.getMsg());
        response.setFirstError(msg);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Content-Type", "application/json;charset=UTF-8");
        log.info(INNER_ACCESS_DENIED.getMsg(),e);
        return new ResponseEntity(response, httpHeaders,HttpStatus.OK);
    }

    /**
     * 上传文件过大异常处理器
     *
     * @param e MaxUploadSizeExceededException
     * @return ResultBean
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<ApiResponse> handlerMaxUploadSizeExceededEx(MaxUploadSizeExceededException e) {
        ApiResponse<Object, Object> response = new ApiResponse<>();
        response.setErrorCode(EXCEED_UPLOAD_FILE_LIMIT.getCode());
        response.setFirstError(EXCEED_UPLOAD_FILE_LIMIT.getMsg());
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Content-Type", "application/json;charset=UTF-8");
        log.info("上传文件大小超出限制",e);
        return new ResponseEntity(response, httpHeaders,HttpStatus.OK);
    }

    /**
     * 请求参数转换异常
     * @param e HttpMessageNotReadableException
     * @return
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public Object handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        ApiResponse<Object, Object> response = new ApiResponse<>();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Content-Type", "application/json;charset=UTF-8");
        String msg = LackParameter.getMsg();
        // 检查是否由于 InvalidFormatException 导致的异常
        if (e.getCause() instanceof InvalidFormatException) {
            InvalidFormatException invalidFormatException = (InvalidFormatException) e.getCause();
            String fieldName = Optional.ofNullable(invalidFormatException.getPath())
                    .filter(v -> !v.isEmpty())
                    .map(v -> v.get(0).getFieldName())
                    .orElse("未知");
            if(e.getMessage().contains("not one of the values accepted for Enum class")){
                String regex = "\\[(.*?)\\]";
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(e.getMessage());
                if(matcher.find()){
                    msg = String.format("字段【%s】格式错误,请使用正确的枚举：%s", fieldName, matcher.group(0));
                }
            }
            log.info("fieldName:{}格式错误,请使用正确的格式 value:{}",fieldName,invalidFormatException.getValue(),e);
        }
        response.setErrorCode(LackParameter.getCode());
        response.setFirstError(msg);
        return new ResponseEntity(response, httpHeaders,HttpStatus.OK);
    }

    /**
     * 参数校验异常处理器
     *
     * @param e 参数校验异常 [POST(FORM)]
     * @return
     */
    @ExceptionHandler(BindException.class)
    public  ResponseEntity<ApiResponse> handleBindException(BindException e) {
        BindingResult result = e.getBindingResult();
        ApiResponse<Object, Object> response = new ApiResponse<>();
        response.setErrorCode(LackParameter.getCode());
        if (result.hasErrors()) {
            List<ObjectError> errors = result.getAllErrors();
            String errorMessage = errors.stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining(","));
            response.setFirstError(errorMessage);
        }
        return new ResponseEntity(response,HttpStatus.OK);
    }


    /**
     * 处理参数校验异常 [POST(JSON)]
     *
     * @param e
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public  ResponseEntity<ApiResponse> handleMethodArgumentNotValid(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        List<FieldError> errors = bindingResult.getFieldErrors();
        StringBuilder errorMessage = new StringBuilder();
        for (FieldError error : errors) {
            errorMessage.append(error.getField()).append(": ").append(error.getDefaultMessage()).append("; ");
        }
        ApiResponse<Object, Object> response = new ApiResponse<>();
        response.setErrorCode(LackParameter.getCode());
        response.setFirstError(errorMessage.toString());
        return new ResponseEntity(response,HttpStatus.OK);
    }

    /**
     * 参数绑定异常
     * @param e
     * @return
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public  ResponseEntity<ApiResponse> handleConstraintViolationException(ConstraintViolationException e) {
        ApiResponse<Object, Object> response = new ApiResponse<>();
        response.setErrorCode(LackParameter.getCode());
        response.setFirstError(e.getMessage());
        return new ResponseEntity(response,HttpStatus.OK);
    }

    /**
     * 兜底捕获 覆盖核心包的
     * @param e
     * @return
     */
    @ExceptionHandler(Exception.class)
    public  ResponseEntity<ApiResponse> handleException(Exception e) {
        ApiResponse<Object, Object> response = new ApiResponse<>();
        response.setErrorCode(LackParameter.getCode());
        response.setFirstError(e.getMessage());
        return new ResponseEntity(response,HttpStatus.OK);
    }

}
