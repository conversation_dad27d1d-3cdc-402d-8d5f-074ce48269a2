package ttfund.web.financialplannerapi.controlloer;

import com.ttfund.web.core.annotation.UserValid;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ttfund.web.financialplannerapi.model.request.ValuationPictureRequest;
import ttfund.web.financialplannerapi.register.RequestLogPro;
import ttfund.web.financialplannerapi.service.ValuationPictureService;

import javax.servlet.http.HttpServletResponse;

@RequestLogPro(isresponseparam = 1)
@Api(tags = {"用户白名单相关接口"})
@RestController
@RequestMapping(value = "/market")
public class MarketController {


    @Autowired
    private ValuationPictureService valuationPictureService;

    @ApiOperation(value = "获取估值图片")
    @GetMapping(value = "/getValuationPicture")
    public void getValuationPicture(ValuationPictureRequest request, HttpServletResponse response) {
        valuationPictureService.getValuationPicture(request, response);
    }
}
