package ttfund.web.financialplannerapi.controlloer;

import com.ttfund.web.core.annotation.RequestLog;
import com.ttfund.web.core.annotation.UserValid;
import com.ttfund.web.core.model.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import ttfund.web.financialplannerapi.model.request.KYCRecordRequest;
import ttfund.web.financialplannerapi.service.IKYCService;


@RequestLog
@Api(tags = {"kyc留痕"})
@RestController
@RequestMapping(value = "/kyc")
public class KYCController {

    @Autowired
    private IKYCService kycService;

    @UserValid(validtype = 2)
    @ApiOperation(value = "kyc留痕,通行证登录验证")
    @RequestMapping(value = "/record", method = { RequestMethod.POST})
    public ApiResponse<Boolean, Object> record(@RequestBody KYCRecordRequest request) {
        return kycService.record(request);
    }
}
