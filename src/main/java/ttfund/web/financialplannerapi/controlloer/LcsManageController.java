package ttfund.web.financialplannerapi.controlloer;

import com.ttfund.web.core.annotation.RequestLog;
import com.ttfund.web.core.annotation.UserValid;
import com.ttfund.web.core.model.ApiResponse;
import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import ttfund.web.financialplannerapi.model.reponse.LcsManageInfoResponse;
import ttfund.web.financialplannerapi.service.ILcsManageService;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName LcsManageController
 * @Description
 * <AUTHOR>
 * @Date 2022/5/25 8:30
 * @Vserion 1.0.0
 */
@RequestLog
@Api(tags = {"理财师管理"})
@RestController
@RequestMapping(value = "/lcsManage")
public class LcsManageController {

    @Resource
    private ILcsManageService lcsManageService;

    @ApiOperation(value = "获取所有理财师")
    @UserValid(validtype = 2)
    @RequestMapping(value = "/queryAllLcs", method = RequestMethod.POST)
    public ApiResponse<List<LcsManageInfoResponse>, Object> queryAllLcs(@RequestBody AuthBaseRequest params) {
        return lcsManageService.queryAll();
    }
}
