package ttfund.web.financialplannerapi.controlloer;

import com.ttfund.web.core.annotation.RequestLog;
import com.ttfund.web.core.annotation.UserValid;
import com.ttfund.web.core.model.ApiResponse;
import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import ttfund.web.financialplannerapi.api.UserPortraitApi;
import ttfund.web.financialplannerapi.manager.PerformanceMetricsOprService;
import ttfund.web.financialplannerapi.model.api.IndustryAnalysisResponse;
import org.springframework.web.bind.annotation.*;
import ttfund.web.financialplannerapi.manager.FinanceDiagOprService;
import ttfund.web.financialplannerapi.model.dto.financeconfig.PerformanceMetricsDTO;
import ttfund.web.financialplannerapi.model.dto.financeconfig.UserHoldViewDTO;
import ttfund.web.financialplannerapi.model.reponse.*;
import ttfund.web.financialplannerapi.model.request.*;
import ttfund.web.financialplannerapi.model.vo.financeconfig.GroupCalResult;
import ttfund.web.financialplannerapi.model.vo.financeconfig.UserHoldViewVO;
import ttfund.web.financialplannerapi.register.Authority;
import ttfund.web.financialplannerapi.service.FinanceDiagService;
import ttfund.web.financialplannerapi.service.UserAuthValidService;
import ttfund.web.financialplannerapi.util.other.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.*;

@RequestLog
@Api(tags = {"用户资产诊断接口"})
@RestController
@RequestMapping(value = "/finance-diag")
@RequiredArgsConstructor
public class FinanceDiagController {

    @Autowired
    private FinanceDiagService financeDiagService;
    @Resource(name = "financeDiagExecPool")
    private Executor financeDiagExecPool;

    private final FinanceDiagOprService financeDiagOprService;
    private final PerformanceMetricsOprService performanceMetricsOprService;
    private final UserAuthValidService userAuthValidService;
    private final UserPortraitApi userPortraitApi;


    @UserValid(validtype = 2)
    @Authority
    @ApiOperation(value = "用户分析资产")
    @GetMapping("/user-hold-view")
    public ApiResponse<UserHoldViewVO, ?> userHoldView(UserHoldViewDTO dto) {
        return ApiResponse.succeed(financeDiagOprService.userHoldView(dto));
    }

//    @UserValid(validtype = 2)
    @Authority
    @ApiOperation(value = "业绩表现")
    @PostMapping("/performance-metrics")
    public ApiResponse<GroupCalResult, ?> performanceMetrics(@RequestBody PerformanceMetricsDTO dto) {
        return ApiResponse.succeed(performanceMetricsOprService.performanceMetrics(dto));
    }

    @UserValid(validtype = 2)
    @Authority
    @ApiOperation(value = "持仓基金分类")
    @RequestMapping(value = "/mine-fund-type", method = {RequestMethod.GET})
    public ApiResponse<MineFundTypeResponse, ?> mineFundType(MineFundTypeRequest request) {
        request.setUserPassportId(userAuthValidService.getUserPassportIdByAuth(request.getUserPassportId()));
        String customerNo = userPortraitApi.getCustomerNoByPid(request.getUserPassportId());
        return financeDiagService.getMineFundType(customerNo, request.getSubAccountNo());
    }

    @UserValid(validtype = 2)
    @Authority
    @ApiOperation(value = "用户持仓基金")
    @RequestMapping(value = "/userHold", method = {RequestMethod.POST})
    public ApiResponse<UserHoldResponse, ?> userHold(@RequestBody UserHoldRequest request) {
        request.setUserPassportId(userAuthValidService.getUserPassportIdByAuth(request.getUserPassportId()));
        String customerNo = userPortraitApi.getCustomerNoByPid(request.getUserPassportId());
        return financeDiagService.getUserHold(customerNo, request.getSubAccountNo());
    }

    @UserValid(validtype = 2)
    @ApiOperation(value = "收益表现")
    @Authority
    @RequestMapping(value = "/income", method = {RequestMethod.POST})
    public ApiResponse<IncomeResponse, ?> income(@RequestBody IncomeRequest request) {
        request.setUserPassportId(userAuthValidService.getUserPassportIdByAuth(request.getUserPassportId()));
        String customerNo = userPortraitApi.getCustomerNoByPid(request.getUserPassportId());
        return financeDiagService.getIncome(customerNo, request.getSubAccountNo(), request.getIndexCode(), request.getRange());
    }

    @UserValid(validtype = 2)
    @Authority
    @ApiOperation(value = "持仓穿透")
    @RequestMapping(value = "/holding-penetration", method = {RequestMethod.POST})
    public ApiResponse<HoldingPenetrationResponse, ?> holdingPenetration(@RequestBody HoldingPenetrationRequest request) {
        request.setUserPassportId(userAuthValidService.getUserPassportIdByAuth(request.getUserPassportId()));
        String customerNo = userPortraitApi.getCustomerNoByPid(request.getUserPassportId());
        return financeDiagService.getHoldingPenetration(customerNo, request.getSubAccountNo());
    }

    @UserValid(validtype = 2)
    @Authority
    @ApiOperation(value = "股票持仓风格")
    @RequestMapping(value = "/portfolio-style", method = {RequestMethod.POST})
    public ApiResponse<PortfolioStyleResponse, ?> portfolioStyle(@RequestBody PortfolioStyleRequest request) {
        request.setUserPassportId(userAuthValidService.getUserPassportIdByAuth(request.getUserPassportId()));
        String customerNo = userPortraitApi.getCustomerNoByPid(request.getUserPassportId());
        return financeDiagService.getPortfolioStyle(customerNo, request.getSubAccountNo());
    }

    @UserValid(validtype = 2)
    @Authority
    @ApiOperation(value = "股票持仓行业AI解读")
    @RequestMapping(value = "/portfolio-industry", method = {RequestMethod.POST})
    public ApiResponse<List<IndustryAnalysisResponse>, ?> portfolioIndustry(@RequestBody PortfolioIndustryRequest request) {
        request.setUserPassportId(userAuthValidService.getUserPassportIdByAuth(request.getUserPassportId()));
        String customerNo = userPortraitApi.getCustomerNoByPid(request.getUserPassportId());
        return financeDiagService.getPortfolioIndustry(customerNo, request.getSubAccountNo());
    }

    @UserValid(validtype = 2)
    @Authority
    @ApiOperation(value = "行业列表")
    @RequestMapping(value = "/list/industry", method = {RequestMethod.POST})
    public ApiResponse<ListIndustryResponse, ?> listIndustry(@RequestBody ListIndustryRequest request) {
        request.setUserPassportId(userAuthValidService.getUserPassportIdByAuth(request.getUserPassportId()));
        String customerNo = userPortraitApi.getCustomerNoByPid(request.getUserPassportId());
        return financeDiagService.getListIndustry(customerNo, request.getSubAccountNo());
    }

//    @UserValid(validtype = 2)
    @Authority
    @ApiOperation(value = "债券穿透")
    @RequestMapping(value = "/bond-penetration", method = {RequestMethod.POST})
    public ApiResponse<BondPenetrationResponse, ?> bondPenetration(@RequestBody BondPenetrationRequest request) {
        request.setUserPassportId(userAuthValidService.getUserPassportIdByAuth(request.getUserPassportId()));
        String customerNo = userPortraitApi.getCustomerNoByPid(request.getUserPassportId());
        return financeDiagService.getBondPenetration(customerNo, request.getSubAccountNo());
    }



    /**
     * 聚合用户资产诊断接口
     * <p>
     * 该方法通过并发方式调用多个资产诊断相关服务，聚合返回用户资产的全面诊断信息。
     * 包括基金分类、用户持仓、收益表现、持仓穿透、投资风格、行业分析等多维度数据。
     * 使用 CompletableFuture 实现并发调用，提高接口响应速度。
     * </p>
     *
     * @param request 包含用户通行证ID、子账户号、指数代码、时间范围等请求参数
     * @return 聚合后的资产诊断信息，包含多个维度的诊断结果
     */
//    @UserValid(validtype = 2)
    @Authority
    @ApiOperation(value = "聚合用户资产诊断接口")
    @RequestMapping(value = "/combined", method = {RequestMethod.POST})
    public ApiResponse<FinanceDiagCombinedResponse, ?> combinedDiagnosis(@RequestBody CombinedRequest request) {
        // 创建响应对象和组合响应数据对象
        ApiResponse<FinanceDiagCombinedResponse, ?> res = new ApiResponse<>();
        FinanceDiagCombinedResponse combinedResponse = new FinanceDiagCombinedResponse();
        request.setUserPassportId(userAuthValidService.getUserPassportIdByAuth(request.getUserPassportId()));
        // 通过 userPortraitApi 获取 customerNo
        String customerNo = userPortraitApi.getCustomerNoByPid(request.getUserPassportId());

        // 并发调用各个服务，使用线程池异步执行
        // 1. 获取持仓基金分类信息
        CompletableFuture<MineFundTypeResponse> mineFundTypeFuture = CompletableFuture.supplyAsync(() ->
                financeDiagService.getMineFundType(customerNo, request.getSubAccountNo()).getData(), financeDiagExecPool);

        // 2. 获取用户持仓基金信息
        CompletableFuture<UserHoldResponse> userHoldFuture = CompletableFuture.supplyAsync(() ->
                financeDiagService.getUserHold(customerNo, request.getSubAccountNo()).getData(), financeDiagExecPool);

        // 3. 获取收益表现信息
        CompletableFuture<IncomeResponse> incomeFuture = CompletableFuture.supplyAsync(() ->
                financeDiagService.getIncome(customerNo, request.getSubAccountNo(), request.getIndexCode(), request.getRange()).getData(), financeDiagExecPool);

        // 4. 获取持仓穿透信息
        CompletableFuture<HoldingPenetrationResponse> holdingPenetrationFuture = CompletableFuture.supplyAsync(() ->
                financeDiagService.getHoldingPenetration(customerNo, request.getSubAccountNo()).getData(), financeDiagExecPool);

        // 5. 获取股票持仓风格信息
        CompletableFuture<PortfolioStyleResponse> portfolioStyleFuture = CompletableFuture.supplyAsync(() ->
                financeDiagService.getPortfolioStyle(customerNo, request.getSubAccountNo()).getData(), financeDiagExecPool);

        // 6. 获取股票持仓行业AI解读信息
        CompletableFuture<List<IndustryAnalysisResponse>> portfolioIndustryFuture = CompletableFuture.supplyAsync(() ->
                financeDiagService.getPortfolioIndustry(customerNo, request.getSubAccountNo()).getData(), financeDiagExecPool);

        // 7. 获取行业列表信息
        CompletableFuture<ListIndustryResponse> listIndustryFuture = CompletableFuture.supplyAsync(() ->
                financeDiagService.getListIndustry(customerNo, request.getSubAccountNo()).getData(), financeDiagExecPool);

        // 8. 获取债券穿透信息
        CompletableFuture<BondPenetrationResponse> bondPenetrationFuture = CompletableFuture.supplyAsync(() ->
                financeDiagService.getBondPenetration(customerNo, request.getSubAccountNo()).getData(), financeDiagExecPool);

        // 使用 CompletableFuture.allOf 等待所有异步任务完成
        CompletableFuture.allOf(mineFundTypeFuture, userHoldFuture, incomeFuture, holdingPenetrationFuture,
                portfolioStyleFuture, portfolioIndustryFuture, listIndustryFuture, bondPenetrationFuture).join();

        try {
            // 从各个 CompletableFuture 中获取结果，并设置到组合响应对象中
            combinedResponse.setMineFundTypeResponse(mineFundTypeFuture.get());
            combinedResponse.setUserHoldResponse(userHoldFuture.get());
            combinedResponse.setIncomeResponse(incomeFuture.get());
            combinedResponse.setHoldingPenetrationResponse(holdingPenetrationFuture.get());
            combinedResponse.setPortfolioStyleResponse(portfolioStyleFuture.get());
            combinedResponse.setPortfolioIndustryResponse(portfolioIndustryFuture.get());
            combinedResponse.setListIndustryResponse(listIndustryFuture.get());
            combinedResponse.setBondPenetrationResponse(bondPenetrationFuture.get());
        } catch (InterruptedException | ExecutionException e) {
            // 异常处理：如果任何一个异步任务失败，设置错误信息并返回
            res.setErrorCode(500);
            res.setFirstError("聚合请求失败: " + e.getMessage());
            return res;
        }

        // 设置组合响应数据并返回成功结果
        res.setData(combinedResponse);
        return res;
    }
}
