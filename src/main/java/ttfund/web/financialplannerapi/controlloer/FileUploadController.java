package ttfund.web.financialplannerapi.controlloer;

import com.ttfund.web.core.annotation.RequestLog;
import com.ttfund.web.core.annotation.UserValid;
import com.ttfund.web.core.model.ApiResponse;
import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import ttfund.web.financialplannerapi.service.IFileUploadService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName FileUploadController
 * @Description
 * <AUTHOR>
 * @Date 2022/5/25 9:08
 * @Vserion 1.0.0
 */
@RequestLog(isrequestparam = 1)
@Api(tags = {"图片上传接口"})
@RestController
@RequestMapping(value = "upload")
public class FileUploadController {

    @Resource
    private IFileUploadService fileUploadService;

    @ApiOperation(value = "图片上传FTP")
    @UserValid(validtype = 2)
    @RequestMapping(value = "/uploadFTP/{type}", method = RequestMethod.POST)
    public ApiResponse<String, Object> uploadFTP(AuthBaseRequest params, @PathVariable(value = "type") int type, MultipartFile file) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        return fileUploadService.uploadFTP(type, file, params.getPassportid());
    }
}
