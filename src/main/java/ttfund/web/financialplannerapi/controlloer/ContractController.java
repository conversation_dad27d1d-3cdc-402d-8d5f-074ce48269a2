package ttfund.web.financialplannerapi.controlloer;

import com.ttfund.web.core.annotation.UserValid;
import com.ttfund.web.core.model.ApiResponse;
import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import com.ttfund.web.core.utils.AesUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import ttfund.web.financialplannerapi.constant.AesConst;
import ttfund.web.financialplannerapi.constant.ErrorCodeEnum;
import ttfund.web.financialplannerapi.model.reponse.*;
import ttfund.web.financialplannerapi.model.request.*;
import ttfund.web.financialplannerapi.register.RequestLogPro;
import ttfund.web.financialplannerapi.service.IContractService;
import ttfund.web.financialplannerapi.util.security.annotation.InnerAuth;

import java.util.Collections;
import java.util.List;

@RequestLogPro
@Api(tags = {"签约"})
@RestController
@RequestMapping(value = "/contract")
public class ContractController {

    @Autowired
    private IContractService contractService;

    @Value("${" + AesConst.USER_NAME_KEY + ":Ads2sdEf5DS2ATWg}")
    public String key;

//    @UserValid(validtype = 2)
    @ApiOperation(value = "查签约或绑定状态")
    @GetMapping(value = "/status")
    public ApiResponse<ContractStatusResponse, Object> status(ContractStatusRequest request) {
        ApiResponse<ContractStatusResponse, Object> result = new ApiResponse<>();
        String passportId = request.getCheckPassportid();
        String financialPlannerId = request.getFinancialPlannerId();
        String institutionId = request.getInstitutionId();
        if (StringUtils.isEmpty(passportId) || (StringUtils.isEmpty(institutionId) && StringUtils.isEmpty(financialPlannerId))) {
            result.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
            result.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
            return result;
        }
        result = contractService.status(passportId, institutionId, financialPlannerId, result);
        return result;
    }

    @ApiOperation(value = "查签约或绑定状态--接微信")
    @GetMapping(value = "/status/v2")
    public ApiResponse<ContractStatusResponse, Object> statusV2(ContractStatusRequest request) throws Exception {
        ApiResponse<ContractStatusResponse, Object> result = new ApiResponse<>();
        String passportId = request.getCheckPassportid();
        String financialPlannerId = request.getFinancialPlannerId();
        String institutionId = request.getInstitutionId();
        if (StringUtils.isEmpty(passportId) || (StringUtils.isEmpty(institutionId) && StringUtils.isEmpty(financialPlannerId))) {
            result.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
            result.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
            return result;
        }
        passportId = AesUtils.Decrypt(AesConst.MODE, AesConst.PACK, key, AesConst.SIV, passportId);
        if (StringUtils.isEmpty(passportId)) {
            result.setErrorCode(ErrorCodeEnum.ErrorCode501.getCode());
            result.setFirstError(ErrorCodeEnum.ErrorCode501.getMsg());
            return result;
        }
        result = contractService.status(passportId, institutionId, financialPlannerId, result);
        return result;
    }

    @UserValid(validtype = 2)
    @ApiOperation(value = "签约")
    @PostMapping(value = "/contract")
    public ApiResponse<Boolean, Object> contract(@RequestBody ContractRequest request) {
        ApiResponse<Boolean, Object> result = new ApiResponse<>();
        String financialPlannerId = request.getFinancialPlannerId();
        String passportId = request.getPassportid();
        String institutionId = request.getInstitutionId();
        String deviceId = request.getDeviceid();
        boolean param = StringUtils.isEmpty(financialPlannerId) || StringUtils.isEmpty(passportId) ||
                StringUtils.isEmpty(institutionId) || StringUtils.isEmpty(deviceId);
        if (param) {
            result.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
            result.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
            return result;
        }
        if (financialPlannerId.equals(passportId)) {
            result.setFirstError(ErrorCodeEnum.CANNOTCONTRACTWITHSELFE.getMsg());
            result.setErrorCode(ErrorCodeEnum.CANNOTCONTRACTWITHSELFE.getCode());
            return result;
        }
        result = contractService.contract(request, result);
        return result;
    }

    @UserValid(validtype = 2)
    @ApiOperation(value = "解约")
    @PostMapping(value = "/cancel")
    public ApiResponse<Boolean, Object> cancelContract(@RequestBody CancelContractRequest request) {
        ApiResponse<Boolean, Object> result = new ApiResponse<>();
        String passportId = request.getPassportid();
        String institutionId = request.getInstitutionId();
        if (StringUtils.isBlank(passportId) || StringUtils.isBlank(institutionId)) {
            result.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
            result.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
            return result;
        }
        return contractService.cancelContract(request, result);
    }

    @UserValid(validtype = 2)
    @ApiOperation(value = "更换理财师")
    @PostMapping(value = "/change")
    public ApiResponse<ChangePlannerResponse, Object> change(@RequestBody ChangePlannerRequest request) {
        ApiResponse<ChangePlannerResponse, Object> result = new ApiResponse<>();
        String passportId = request.getPassportid();
        String institutionId = request.getInstitutionId();
        String deviceId = request.getDeviceid();
        boolean param = StringUtils.isEmpty(passportId) ||
                StringUtils.isEmpty(institutionId) || StringUtils.isEmpty(deviceId);
        if (param) {
            result.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
            result.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
            return result;
        }
        result = contractService.change(request, result);
        return result;
    }

    @UserValid(validtype = 2)
    @ApiOperation(value = "根据用户通行证查所有签约的理财师")
    @GetMapping(value = "/getPlannerByUser")
    public ApiResponse<List<UserPlannerResponse>, Object> getPlannerByUser(AuthBaseRequest request) {
        ApiResponse<List<UserPlannerResponse>, Object> result = new ApiResponse<>();
        String passportId = request.getPassportid();
        if (StringUtils.isEmpty(passportId)) {
            result.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
            result.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
            return result;
        }
        result = contractService.getPlannerByUser(passportId, result);
        return result;
    }

    @UserValid(validtype = 2)
    @ApiOperation(value = "根据用户通行证查询签约的自营理财师-VIP首页")
    @GetMapping(value = "/getTTPlannerByUser")
    public ApiResponse<UserTTPlannerResponse, Object> getTTPlannerByUser(AuthBaseRequest request) {
        return contractService.getTTPlannerByUser(request);
    }


    @UserValid(validtype = 2)
    @ApiOperation(value = "查询与当前机构是否解约,data中: true=存在签约关系，false=已解约")
    @GetMapping(value = "/isCancel")
    public ApiResponse<Boolean, Object> isCancel(QueryContractRequest request) {
        return contractService.isCancel(request);
    }

    @UserValid(validtype = 2)
    @ApiOperation(value = "理财师悬浮窗展示, 无数据则不展示")
    @GetMapping(value = "/lcsWindow")
    public ApiResponse<List<LcsWindowResponse>, Object> lcsWindow(LcsWindowRequest request) {
        return contractService.getLcsWindow(request);
    }

    @UserValid(validtype = 2)
    @ApiOperation(value = "根据用户通行证查所有绑定或签约的理财师")
    @GetMapping(value = "/getAllPlannerByUser")
    public ApiResponse<List<UserPlannerResponse>, Object> getAllPlannerByUser(AuthBaseRequest request) {
        ApiResponse<List<UserPlannerResponse>, Object> result = new ApiResponse<>();
        String passportId = request.getPassportid();
        if (StringUtils.isEmpty(passportId)) {
            result.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
            result.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
            return result;
        }
        result = contractService.getAllPlannerByUser(passportId, result);
        return result;
    }

    @UserValid(validtype = 2)
    @ApiOperation(value = "绑定天天理财师")
    @PostMapping(value = "/bind")
    public ApiResponse<BindStatusResponse, Object> bind(@RequestBody AuthBaseRequest request) {
        ApiResponse<BindStatusResponse, Object> result = new ApiResponse<>();
        String passportId = request.getPassportid();
        if (StringUtils.isEmpty(passportId)) {
            result.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
            result.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
            return result;
        }
        result = contractService.bindPlanner(request, result);
        return result;
    }

    @UserValid
    @ApiOperation(value = "自营-获取用户是否签约")
    @PostMapping(value = "/batchTTFundUserContract")
    public ApiResponse<List<TTFundUserContractResponse>, Object> batchTTFundUserContract(@RequestBody TTFundUserContractRequest request) {
        return contractService.batchTTFundUserContract(request);
    }

    @UserValid(validtype = 2)
    @ApiOperation(value = "查询与当前机构是否签约(缓存)")
    @GetMapping(value = "/contractInstWithCache")
    public ApiResponse<UserPlannerInfoResponse, Object> contractInstWithCache(QueryContractRequest request) {
        return contractService.contractInstWithCache(request);
    }

    @InnerAuth
    @ApiOperation(value = "内--查签约或绑定状态")
    @GetMapping(value = "/inner/status")
    public ApiResponse<ContractStatusResponse, Object> innerStatus(ContractStatusRequest request) {
        return this.status(request);
    }

    @InnerAuth
    @ApiOperation(value = "内--签约")
    @PostMapping(value = "/inner/contract")
    public ApiResponse<Boolean, Object> innerContract(@RequestBody ContractRequest request) {
        return this.contract(request);
    }

}
