package ttfund.web.financialplannerapi.controlloer;

import com.ttfund.web.core.annotation.AopCache;
import com.ttfund.web.core.annotation.RequestLog;
import com.ttfund.web.core.annotation.UserValid;
import com.ttfund.web.core.model.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ttfund.web.financialplannerapi.constant.ErrorCodeEnum;
import ttfund.web.financialplannerapi.model.request.WhiteListVerifyRequest;
import ttfund.web.financialplannerapi.service.WhiteListService;

@RequestLog
@Api(tags = {"用户白名单相关接口"})
@RestController
@RequestMapping(value = "/whitelist")
public class WhiteListController {
    @Autowired
    private WhiteListService whiteListService;

    @UserValid(validtype = 2)
    @AopCache(cachetype = 3, fieldlist = {"passportid", "type"}, cache1expire = 60 * 1000, cache2expire = 10 * 60 * 1000)
    @ApiOperation(value = "查询用户是否在白名单中")
    @GetMapping(value = "/verify")
    public ApiResponse<Boolean, Object> status(WhiteListVerifyRequest request) {
        if (StringUtils.isBlank(request.getPassportid()) || request.getType() < 0) {
            ApiResponse<Boolean, Object> result = new ApiResponse<>();
            result.setData(false);
            result.setFirstError(ErrorCodeEnum.LackParameter.getMsg());
            result.setErrorCode(ErrorCodeEnum.LackParameter.getCode());
            return result;
        }
        return whiteListService.verifyUserInWhiteList(request.getPassportid(), request.getType());
    }

}
