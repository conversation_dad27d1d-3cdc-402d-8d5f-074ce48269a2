//package ttfund.web.financialplannerapi.controlloer;
//
//import com.ttfund.web.core.annotation.RequestLog;
//import com.ttfund.web.core.model.ApiResponse;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RestController;
//import ttfund.web.financialplannerapi.exception.BusinessException;
//import ttfund.web.financialplannerapi.model.reponse.AddBlackListResponse;
//import ttfund.web.financialplannerapi.model.reponse.PlannerBlackListResponse;
//import ttfund.web.financialplannerapi.model.reponse.RemoveBlackListResponse;
//import ttfund.web.financialplannerapi.model.request.AddBlackListRequest;
//import ttfund.web.financialplannerapi.model.request.PlannerBlackListRequest;
//import ttfund.web.financialplannerapi.model.request.RemoveBlackListRequest;
//import ttfund.web.financialplannerapi.service.IBlackListService;
//
//import javax.annotation.Resource;
//
//@RequestLog
//@Api(tags = {"用户黑名单接口"})
//@RestController
//@RequestMapping(value = "/blacklist")
//public class BlackListController {
//
//    @Resource
//    IBlackListService blackListServiceImpl;
//
//    @ApiOperation(value = "客户拉黑理财师")
//    @RequestMapping(value = "/addToBlackList", method = { RequestMethod.GET,RequestMethod.POST})
//    public ApiResponse<AddBlackListResponse, ?> addToBlackList(AddBlackListRequest request) {
//        ApiResponse<AddBlackListResponse, ?> result = new ApiResponse<>();
//        try {
//            result.setData(blackListServiceImpl.addToBlackList(request));
//        } catch (BusinessException e) {
//            result.setErrorCode(e.getErrorCode());
//            result.setFirstError(e.getMessage());
//        }
//        return result;
//    }
//
//    @ApiOperation(value = "移出黑名单")
//    @RequestMapping(value = "/removeBlackList", method = { RequestMethod.GET,RequestMethod.POST})
//    public ApiResponse<RemoveBlackListResponse, ?> removeBlackList(RemoveBlackListRequest request) {
//        ApiResponse<RemoveBlackListResponse, ?> result = new ApiResponse<>();
//        try {
//            result.setData(blackListServiceImpl.removeBlackList(request));
//        } catch (BusinessException e) {
//            result.setErrorCode(e.getErrorCode());
//            result.setFirstError(e.getMessage());
//        }
//        return result;
//    }
//
//    @ApiOperation(value = "获取黑名单列表")
//    @RequestMapping(value = "/getBlackList", method = { RequestMethod.GET,RequestMethod.POST})
//    public ApiResponse<PlannerBlackListResponse, ?> getBlackList(PlannerBlackListRequest request) {
//        ApiResponse<PlannerBlackListResponse, ?> result = new ApiResponse<>();
//        try {
//            result.setData(blackListServiceImpl.getBlackList(request));
//        } catch (BusinessException e) {
//            result.setErrorCode(e.getErrorCode());
//            result.setFirstError(e.getMessage());
//        }
//        return result;
//    }
//}
