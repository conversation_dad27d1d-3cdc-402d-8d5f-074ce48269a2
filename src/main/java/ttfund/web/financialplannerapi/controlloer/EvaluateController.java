package ttfund.web.financialplannerapi.controlloer;

import com.ttfund.web.core.annotation.RequestLog;
import com.ttfund.web.core.annotation.UserValid;
import com.ttfund.web.core.model.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import ttfund.web.financialplannerapi.exception.BusinessException;
import ttfund.web.financialplannerapi.model.request.EvaluateAddRequest;
import ttfund.web.financialplannerapi.model.request.FeedBackAddRequest;
import ttfund.web.financialplannerapi.service.IEvaluateService;

import javax.annotation.Resource;


@RequestLog
@Api(tags = {"用户评价接口"})
@RestController
@RequestMapping(value = "/evaluate")
public class EvaluateController {

    private static final Logger logger = LoggerFactory.getLogger(EvaluateController.class);
    @Resource
    IEvaluateService evaluateServiceImpl;

    @UserValid(validtype = 2)
    @ApiOperation(value = "客户评价添加")
    @RequestMapping(value = "/addEvaluation", method = { RequestMethod.POST})
    public ApiResponse<Boolean, ?> addEvaluate(@RequestBody EvaluateAddRequest request) {
        ApiResponse<Boolean, ?> result = new ApiResponse<>();
        try {
            result.setData(evaluateServiceImpl.userEvaluate(request));
        } catch (BusinessException e) {
            result.setErrorCode(e.getErrorCode());
            result.setFirstError(e.getMessage());
        }
        return result;
    }

    @UserValid(validtype = 2)
    @ApiOperation(value = "意见反馈添加")
    @RequestMapping(value = "/addFeedBack", method = { RequestMethod.POST})
    public ApiResponse<Boolean, ?> addFeedBack(@RequestBody FeedBackAddRequest request) {
        ApiResponse<Boolean, ?> result = new ApiResponse<>();
        try {
            result.setData(evaluateServiceImpl.userFeedBack(request));
        } catch (BusinessException e) {
            result.setErrorCode(e.getErrorCode());
            result.setFirstError(e.getMessage());
        }
        return result;
    }
}