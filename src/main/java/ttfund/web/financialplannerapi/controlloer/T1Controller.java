package ttfund.web.financialplannerapi.controlloer;

import com.ttfund.web.core.annotation.RequestLog;
import com.ttfund.web.core.model.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RequestLog
@Api(tags = {"测试用"})
@RestController
@RequestMapping(value = "/test")
public class T1Controller {
    @ApiOperation(value = "测试")
    @RequestMapping(value = "/t1", method = {RequestMethod.GET, RequestMethod.POST})
    public ApiResponse<Object, ?> t1() {
        ApiResponse<Object, ?> result = new ApiResponse<>();
        result.setData("123");
        return result;
    }
}
