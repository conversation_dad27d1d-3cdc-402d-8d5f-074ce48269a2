package ttfund.web.financialplannerapi.controlloer;

import com.ttfund.web.core.annotation.RequestLog;
import com.ttfund.web.core.annotation.UserValid;
import com.ttfund.web.core.model.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import ttfund.web.financialplannerapi.model.reponse.AppointmentAvailableResponse;
import ttfund.web.financialplannerapi.model.reponse.AppointmentInfoResponse;
import ttfund.web.financialplannerapi.model.request.AddAppointmentRequest;
import ttfund.web.financialplannerapi.model.request.AppointmentQueryRequest;
import ttfund.web.financialplannerapi.service.AppointmentService;

/**
 * <AUTHOR>
 * @date 2022/8/22
 * @Description 电话预约API
 */
@UserValid(validtype = 2)
@RequestLog
@Api(tags = {"电话预约"})
@RestController
@RequestMapping(value = "/appointment")
public class AppointmentController {

    @Autowired
    private AppointmentService appointmentService;

    @ApiOperation(value = "预约信息查询")
    @RequestMapping(value = "/queryAppointment", method = { RequestMethod.GET})
    public ApiResponse<AppointmentInfoResponse, ?> queryAppointment(AppointmentQueryRequest request) {
        return appointmentService.queryAppointmentInfo(request);
    }

    @ApiOperation(value = "查询可预约的时段")
    @RequestMapping(value = "/queryCanBeAppointByFinancialPlannerId", method = { RequestMethod.GET})
    public ApiResponse<AppointmentAvailableResponse, ?> queryCanBeAppoint(AppointmentQueryRequest request) {
        return appointmentService.queryCanBeAppointment(request);
    }

    @ApiOperation(value = "新增或修改预约记录")
    @RequestMapping(value = "/addOrUpdateAppointment", method = RequestMethod.POST)
    public ApiResponse<Boolean,Object> addOrUpdateAppointment(@RequestBody AddAppointmentRequest request){
        return appointmentService.addOrUpdateAppointment(request);
    }

    @ApiOperation(value = "取消预约")
    @RequestMapping(value = "/cancelAppointment", method = RequestMethod.GET)
    public ApiResponse<Boolean,Object> cancelAppointment(AppointmentQueryRequest request){
        return appointmentService.cancelAppointment(request);
    }
}
