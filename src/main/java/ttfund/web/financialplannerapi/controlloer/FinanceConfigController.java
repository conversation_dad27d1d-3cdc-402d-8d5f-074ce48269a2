package ttfund.web.financialplannerapi.controlloer;

import com.ttfund.web.core.annotation.RequestLog;
import com.ttfund.web.core.annotation.UserValid;
import com.ttfund.web.core.model.ApiResponse;
import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import ttfund.web.financialplannerapi.manager.FinanceConfigOprService;
import ttfund.web.financialplannerapi.model.dto.financeconfig.UserDetailsDTO;
import ttfund.web.financialplannerapi.model.dto.financeconfig.UserHoldViewDTO;
import ttfund.web.financialplannerapi.model.vo.financeconfig.UserFinanceConfigVO;
import ttfund.web.financialplannerapi.model.vo.financeconfig.UserHoldViewVO;
import ttfund.web.financialplannerapi.register.Authority;
import ttfund.web.financialplannerapi.util.security.SecurityUtils;

/**
 * <AUTHOR>
 */

@RequestLog
@Api(tags = {"用户配置建议"})
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/finance-config")
public class FinanceConfigController {

    private final FinanceConfigOprService financeConfigOprService;

    @UserValid(validtype = 2)
    @Authority
    @ApiOperation(value = "用户配置建议")
    @PostMapping("/details")
    public ApiResponse<UserFinanceConfigVO, ?> details(@RequestBody UserDetailsDTO request) {
        return ApiResponse.succeed(financeConfigOprService.details(request));
    }
}
