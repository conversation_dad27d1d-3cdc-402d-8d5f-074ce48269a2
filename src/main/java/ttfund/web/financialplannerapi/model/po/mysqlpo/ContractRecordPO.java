package ttfund.web.financialplannerapi.model.po.mysqlpo;

import lombok.Data;

import java.util.Date;
@Data
public class ContractRecordPO {

    private String id;

    private String institutionId;

    private String financialPlannerId;

    private String passportId;

    private Date contractTime;

    private String deviceId;

    private String contractContent;

    private String communityAttribute;

    private String nickname;

    private String mark;

    private Date createTime;

    private Date updateTime;

    private int deleted;

    private int contractStatus;

    private String institutionName;

    private String imgUrl;

    private String plannerNickname;

}
