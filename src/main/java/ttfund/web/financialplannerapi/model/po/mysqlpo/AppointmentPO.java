package ttfund.web.financialplannerapi.model.po.mysqlpo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 电话预约记录实体
 */
@Data
public class AppointmentPO {
    private String id;
    /**
     * 机构id
     */
    private String institutionId;
    /**
     * 理财师id 理财师的passportId
     */
    private String financialPlannerId;
    /***
     * 用户通行证id
     */
    private String passportId;
    /**
     * 用户预留手机号
     */
    private String phone;
    /**
     * 预约时段 yyyy-MM-dd HH:mm 固定上午09:00 和下午13:00
     */
    private Date appointment;
    /**
     * 预约状态标识 0=未回访，1=已回访，2=过期未回访
     */
    private Integer status;

    private Date createTime;

    private Date updateTime;
    /**
     * 逻辑删除状态
     */
    private Integer deleted;

    /**
     * 会话id
     */
    private String serviceId;

}
