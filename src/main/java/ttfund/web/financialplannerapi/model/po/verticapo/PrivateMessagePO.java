package ttfund.web.financialplannerapi.model.po.verticapo;

import lombok.Data;
import org.apache.kafka.clients.producer.internals.Sender;

import java.util.Date;

@Data
public class PrivateMessagePO {


    private String Sender;
    private String MsgID;
    private String Receiver;
    private String MsgIndexID;
    private String Device;
    private String Content;
    private Date SendDate;
    private Date PushDate;


}
