package ttfund.web.financialplannerapi.model.po.financeconfig;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import ttfund.web.financialplannerapi.constant.financeconfig.FinanceConfigFundTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Accessors(chain = true)
@TableName("t_finance_config_fund")
@ApiModel(value = "FinanceConfigFundPO", description = "配置建议大类基金实体")
public class FinanceConfigFundPO {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "自增主键ID")
    private Long id;

    @ApiModelProperty(value = "配置ID")
    private Long financeConfigId;

    @ApiModelProperty(value = "配置类型")
    private FinanceConfigFundTypeEnum type;

    @ApiModelProperty(value = "类型占比(%)")
    private BigDecimal percent;

    @ApiModelProperty(value = "推荐基金JSON")
    private String recommendFunds;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "修改人")
    private String editor;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "乐观锁版本")
    private Integer version;

    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty(value = "逻辑删除标记")
    private Integer deleted;
}