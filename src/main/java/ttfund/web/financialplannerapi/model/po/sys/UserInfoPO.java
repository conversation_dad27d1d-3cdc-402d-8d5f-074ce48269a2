package ttfund.web.financialplannerapi.model.po.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("t_user_info")
@ApiModel(value = "UserInfoPO", description = "用户表")
public class UserInfoPO {

    @ApiModelProperty(value = "用户通行证id", required = true)
    @TableId(value = "passport_id", type = IdType.NONE)
    private String passportId;

    @ApiModelProperty(value = "用户交易账号")
    private String customerNo;

    @ApiModelProperty(value = "用户平台当前AUM")
    private BigDecimal nowAum;

    @ApiModelProperty(value = "生日")
    private LocalDateTime birthday;

    @ApiModelProperty(value = "最近登陆APP日期")
    private LocalDateTime loginDate;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "出生日期（字符串格式）")
    private String birthdate;

    @ApiModelProperty(value = "当前AUM密度（脱敏）")
    private String nowAumDensi;

    @ApiModelProperty(value = "用户姓名密文")
    private String userName;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "未脱敏的年龄")
    private String age;

    @ApiModelProperty(value = "年龄密度（脱敏）")
    private String ageDensi;

    @ApiModelProperty(value = "风险等级")
    private String riskLevel;

    @ApiModelProperty(value = "未脱敏开户时间")
    private String openTime;

    @ApiModelProperty(value = "开户时间密度（脱敏）")
    private String openTimeDensi;

    @ApiModelProperty(value = "标签扩展字段")
    private String extraInfo;

    @ApiModelProperty(value = "标签字段")
    private String tag;

    @ApiModelProperty(value = "住址")
    private String address;

    @ApiModelProperty(value = "用户企微id")
    private String qwId;
}
