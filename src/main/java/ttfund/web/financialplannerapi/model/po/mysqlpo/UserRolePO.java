package ttfund.web.financialplannerapi.model.po.mysqlpo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("理财师信息")
public class UserRolePO {

    private String id;
    @ApiModelProperty("机构id")
    private String institutionId;
    @ApiModelProperty("机构名字")
    private String institutionName;
    @ApiModelProperty("角色id")
    private String roleId;
    @ApiModelProperty("理财师名字")
    private String username;
    @ApiModelProperty("理财师id")
    private String passportId;
    @ApiModelProperty("特色")
    private String characteristics;
    @ApiModelProperty("个人简介")
    private String personalProfile;
    @ApiModelProperty("上半身图像")
    private String img;
    @ApiModelProperty("从业年限")
    private String workYear;
    @ApiModelProperty("执业编号")
    private String workNumber;
    @ApiModelProperty("理财师昵称")
    private String nickName;
    @ApiModelProperty("企业微信二维码")
    private String wxCode;
    @ApiModelProperty("获客链接")
    private String qwUserLink;

}
