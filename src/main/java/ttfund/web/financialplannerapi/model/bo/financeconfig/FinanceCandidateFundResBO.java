package ttfund.web.financialplannerapi.model.bo.financeconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import ttfund.web.financialplannerapi.constant.fund.FundFullTypeEnum;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(value = "FinanceCandidateFundResBO",description = "备选基金BO")
public class FinanceCandidateFundResBO {

    @ApiModelProperty(value = "基金代码")
    private String fundCode;

    @ApiModelProperty(value = "基金名称")
    private String fundName;

    @ApiModelProperty(value = "基金类型")
    private FundFullTypeEnum fundType;

    @ApiModelProperty(value = "申购状态")
    private String subscribeStatus;

    @ApiModelProperty(value = "申购限额")
    private BigDecimal subscribeLimit;

    @ApiModelProperty(value = "申购限额格式化")
    private String subscribeLimitFormat;

}