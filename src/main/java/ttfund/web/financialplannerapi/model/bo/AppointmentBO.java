package ttfund.web.financialplannerapi.model.bo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/8/26
 * @Description
 */
@Data
public class AppointmentBO {
    private String id;
    /**
     * 用户通行证id
     */
    private String passportId;
    /**
     * 用户预留手机号
     */
    private String phone;
    /**
     * 预约时段 yyyy-MM-dd HH:mm 固定上午09:00 和下午13:00
     */
    private LocalDateTime appointment;
    /**
     * 回访电话通话时长 null=未进行回访, 0=回访未接通, 大于0=回访已接通
     */
    private Integer callTime;
    /**
     * 创建时间  用于排序
     */
    private LocalDateTime createTime;
    /**
     * 用户备注
     */
    private String mark;
    /**
     * 服务记录id 用于跳转服务记录，为空则没有填写服务记录
     */
    private String serviceRecordId;
}
