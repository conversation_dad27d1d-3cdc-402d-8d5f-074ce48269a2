package ttfund.web.financialplannerapi.model.bo.sys;

import com.alibaba.fastjson2.JSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import ttfund.web.financialplannerapi.constant.user.UserCustRiskLevelEnum;
import ttfund.web.financialplannerapi.model.po.sys.UserInfoPO;
import ttfund.web.financialplannerapi.util.other.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "UserInfoResBO", description = "用户BO")
public class UserInfoResBO extends UserInfoPO {

    @ApiModelProperty("用户关联的企微id")
    private List<String> qwIds;

    @ApiModelProperty("用户真实姓名")
    private String userRealName;

    public void initQwIds(){
        if(StringUtils.isEmpty(this.getQwId())){
            this.qwIds = new ArrayList<>();
            return;
        }
        this.qwIds = JSON.parseArray(this.getQwId(), String.class);
    }
}
