package ttfund.web.financialplannerapi.model.bo.financeconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import ttfund.web.financialplannerapi.constant.financeconfig.FinanceConfigFundSubTypeEnum;
import ttfund.web.financialplannerapi.constant.fund.FundFullTypeEnum;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "FinanceMainFundResBO",description = "主选基金BO")
public class FinanceMainFundResBO {

    @ApiModelProperty(value = "基金代码")
    private String fundCode;

    @ApiModelProperty(value = "占比")
    private BigDecimal percent;

    @ApiModelProperty(value = "占比")
    private String percentFormat;

    @ApiModelProperty(value = "标签")
    private String label;

    @ApiModelProperty(value = "基金名称")
    private String fundName;

    @ApiModelProperty(value = "基金二级分类")
    private FinanceConfigFundSubTypeEnum financeConfigFundSubType;

    @ApiModelProperty(value = "基金类型")
    private FundFullTypeEnum fundType;

    @ApiModelProperty(value = "基金类型文本")
    private String fundFullTypeFormat;

    @ApiModelProperty(value = "AI秒懂基金")
    private String fundAi;

    @ApiModelProperty(value = "申购状态")
    private String subscribeStatus;

    @ApiModelProperty(value = "申购限额")
    private BigDecimal subscribeLimit;

    @ApiModelProperty(value = "申购限额格式化")
    private String subscribeLimitFormat;

    @ApiModelProperty(value = "备选基金")
    private List<FinanceCandidateFundResBO> candidateFunds;
}