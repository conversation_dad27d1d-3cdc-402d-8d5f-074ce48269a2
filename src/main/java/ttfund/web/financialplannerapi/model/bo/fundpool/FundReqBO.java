package ttfund.web.financialplannerapi.model.bo.fundpool;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Slf4j
@Setter
@Getter
@ToString
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "FundResBO对象", description = "基金BO")
public class FundReqBO {

    @ApiModelProperty(value = "自增Id")
    private Long id;

    @ApiModelProperty(value = "投顾类型: TG 投顾, FUND基金, SI高端理财")
    private String fundType;

    @ApiModelProperty(value = "产品代码")
    private String fundCode;

    @ApiModelProperty(value = "产品名称")
    private String fundName;

    @ApiModelProperty(value = "产品类型（大类）")
    private String fundMajorType;

    @ApiModelProperty(value = "产品类型（大类+小类）")
    private String fundFullType;

    @ApiModelProperty(value = "申购状态")
    private String subscribeStatus;

    @ApiModelProperty(value = "申购限额")
    private BigDecimal subscribeLimit;

    @ApiModelProperty(value = "今年来收益")
    private BigDecimal yearToDateReturn;

    @ApiModelProperty(value = "近1年收益")
    private BigDecimal oneYearReturn;

    @ApiModelProperty(value = "近3年收益")
    private BigDecimal threeYearReturn;

    @ApiModelProperty(value = "历史最大回撤")
    private BigDecimal historicMaxDrawdown;

    @ApiModelProperty(value = "现任经理本产品最大回撤")
    private BigDecimal currentManagerMaxDrawdown;

    @ApiModelProperty(value = "近1年夏普")
    private BigDecimal oneYearSharpeRatio;

    @ApiModelProperty(value = "近3年夏普")
    private BigDecimal threeYearSharpeRatio;

    @ApiModelProperty(value = "股票换手率")
    private BigDecimal stockTurnoverRate;

    @ApiModelProperty(value = "近1年最大回撤")
    private BigDecimal oneYearMaxDrawdown;

    @ApiModelProperty(value = "近1年净值修复天数")
    private BigDecimal oneYearNetValueFixDays;


    @ApiModelProperty(value = "近3年最大回撤")
    private BigDecimal threeYearMaxDrawdown;

    @ApiModelProperty(value = "近3年净值修复天数")
    private BigDecimal threeYearNetValueFixDays;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "编辑人")
    private String editor;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "乐观锁")
    private Integer version;

    @ApiModelProperty(value = "逻辑删除")
    private Integer deleted = 0;

}
