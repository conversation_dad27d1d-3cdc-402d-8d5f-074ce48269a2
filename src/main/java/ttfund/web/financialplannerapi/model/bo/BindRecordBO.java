package ttfund.web.financialplannerapi.model.bo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/11/15
 * @Description 绑定关系记录表实体
 */
@Data
public class BindRecordBO {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 预约id
     */
    private String appointmentId;
    /**
     * 预约时段 yyyy-MM-dd HH:mm 固定上午09:00 和下午13:00
     */
    private LocalDateTime appointment;
    /**
     * 用户通行证id
     */
    private String passportId;
    /**
     * 机构id
     */
    private String institutionId;
    /**
     * 理财师通行证id
     */
    private String financialPlannerId;
    /**
     * 绑定id
     */
    private String bindId;
    /**
     * 通话记录id
     */
    private String callId;
    /**
     * 绑定中间号
     */
    private String telX;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
