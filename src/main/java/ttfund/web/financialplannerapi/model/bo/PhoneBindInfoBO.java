package ttfund.web.financialplannerapi.model.bo;

import lombok.Data;

@Data
public class PhoneBindInfoBO {



    /**
     * 用户 A 号码
     */
    private String telA;

    /**
     * 用户 B 号码
     */
    private String telB;

    /**
     * 用于请求重试去重，每一组绑定关系该参数具有唯一性，同一组绑定关系多次重试该参数取值相同，最大不超过 20 位
     */
    private String requestId;

    /**
     * 订单产生所在地区号，区号格式不带0
     */
    private int areaCode;

    /**
     * 绑定关系过期失效时间(秒)
     */
    private int expiration;

    /**
     * 是否对通话进行录音 ‘0’：否；'1’：是；
     */
    private String record;

    public PhoneBindInfoBO() {
    }

    public PhoneBindInfoBO(String telA, String telB, String requestId, int areaCode, int expiration, String record) {
        this.telA = telA;
        this.telB = telB;
        this.requestId = requestId;
        this.areaCode = areaCode;
        this.expiration = expiration;
        this.record = record;
    }
}
