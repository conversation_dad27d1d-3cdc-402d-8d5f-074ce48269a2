package ttfund.web.financialplannerapi.model.bo.financeconfig;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import ttfund.web.financialplannerapi.model.po.financeconfig.FinanceConfigFundPO;
import ttfund.web.financialplannerapi.util.other.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "FinanceConfigFundResBO", description = "配置建议大类基金实体BO")
public class FinanceConfigFundResBO extends FinanceConfigFundPO {

    @ApiModelProperty(value = "推荐基金")
    private List<FinanceMainFundResBO> recommendFundDetails;

    public void initRecommendFundDetails() {
        if(StringUtils.isNotEmpty(this.getRecommendFunds())) {
            List<FinanceMainFundResBO> mainFunds = JSON.parseObject(this.getRecommendFunds(), new TypeReference<List<FinanceMainFundResBO>>() {});
            this.setRecommendFundDetails(mainFunds);
        }
    }
}