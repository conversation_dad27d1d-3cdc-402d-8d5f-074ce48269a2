package ttfund.web.financialplannerapi.model.bo.financeconfig;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "PortfolioResBO", description = "用户组合持仓市值BO")
public class PortfolioResBO {

    @ApiModelProperty(value = "组合编号")
    @JSONField(name = "subAccountNo")
    private String portfolioNo;

    @ApiModelProperty(value = "组合持仓市值")
    @JSONField(name = "subAsset")
    private String portfolioAmountFormat;

    @ApiModelProperty(value = "组合名称")
    private String portfolioName;
}
