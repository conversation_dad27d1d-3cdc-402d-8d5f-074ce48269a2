package ttfund.web.financialplannerapi.model.bo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/26
 * @Description 电话预约的通话记录
 */
@Data
public class AppointmentServiceBO {
    private String appointmentId;
    private String serviceId;
    private String dateDay;
    private String institutionId;
    private String userPassportId;
    private String financialPlannerId;
    private Integer callTime;
    /**
     * 预约时段 yyyy-MM-dd HH:mm 固定上午11:30 和下午18:00
     */
    private Date appointment;
}
