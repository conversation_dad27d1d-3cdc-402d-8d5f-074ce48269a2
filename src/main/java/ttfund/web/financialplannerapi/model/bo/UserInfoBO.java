package ttfund.web.financialplannerapi.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName UserInfoBO
 * @Description
 * <AUTHOR>
 * @Date 2022/6/1 8:47
 * @Vserion 1.0.0
 */
@Data
public class UserInfoBO {

    @ApiModelProperty(value = "用户性别：0保密，1男，2女")
    private String gender;

    @ApiModelProperty(value = "用户介绍")
    private String introduce;

    @ApiModelProperty(value = " 用户基金经理id")
    private String mgrId;

    @ApiModelProperty(value = "用户昵称")
    private String nickname;

    @ApiModelProperty(value = "用户通行证id")
    private String passportId;

    @ApiModelProperty(value = "用户加V：【老版：0不加V，1机构加V，2个人加V】，【新版：0不加V，1蓝V（机构），2橙V（个人），3五角星（个人）】")
    private Integer userV = 0; // 默认不加V
}
