package ttfund.web.financialplannerapi.model.bo.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import ttfund.web.financialplannerapi.mybatis.annotation.PreciseQuery;
import ttfund.web.financialplannerapi.mybatis.util.PageParam;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UserInfoPageReqBO", description = "用户信息分页参数BO")
public class UserInfoPageReqBO extends PageParam {

    @ApiModelProperty(value = "用户通行证id", required = true)
    @TableId(value = "passport_id", type = IdType.NONE)
    private List<String> passportId;

    @ApiModelProperty(value = "用户交易账号")
    @PreciseQuery
    private List<String> customerNo;

    @ApiModelProperty(value = "用户平台当前AUM")
    private List<BigDecimal> nowAum;

    @ApiModelProperty(value = "生日")
    private List<LocalDateTime> birthday;

    @ApiModelProperty(value = "最近登陆APP日期")
    private List<LocalDateTime> loginDate;

    @ApiModelProperty(value = "创建时间")
    private List<LocalDateTime> createTime;

    @ApiModelProperty(value = "更新时间")
    private List<LocalDateTime> updateTime;

    @ApiModelProperty(value = "出生日期（字符串格式）")
    private List<String> birthdate;

    @ApiModelProperty(value = "当前AUM密度（脱敏）")
    private List<String> nowAumDensi;

    @ApiModelProperty(value = "用户姓名密文")
    private List<String> userName;

    @ApiModelProperty(value = "性别")
    private List<String> gender;

    @ApiModelProperty(value = "未脱敏的年龄")
    private List<String> age;

    @ApiModelProperty(value = "年龄密度（脱敏）")
    private List<String> ageDensi;

    @ApiModelProperty(value = "风险等级")
    private List<String> riskLevel;

    @ApiModelProperty(value = "未脱敏开户时间")
    private List<String> openTime;

    @ApiModelProperty(value = "开户时间密度（脱敏）")
    private List<String> openTimeDensi;

    @ApiModelProperty(value = "标签扩展字段")
    private List<String> extraInfo;

    @ApiModelProperty(value = "标签字段")
    private List<String> tag;

    @ApiModelProperty(value = "住址")
    private List<String> address;

    @ApiModelProperty(value = "用户企微id")
    private List<String> qwId;

    @ApiModelProperty(value = "理财师对管户的备注")
    private List<String> mark;

    @ApiModelProperty(value = "理财师通行证id")
    private List<String> plannerId;

    @ApiModelProperty(value = "机构id")
    private List<String> institutionId;

    @ApiModelProperty(value = "签约记录逻辑删除")
    private List<Integer> recordDeleted;

    @ApiModelProperty(value = "签约记录状态")
    private List<Integer> contractStatus;

    @Override
    public void init(){
        super.init();
        this.setDeleted(null);
    }
}
