package ttfund.web.financialplannerapi.model.bo.financeconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "UserBaseResBO", description = "用户基础信息BO")
public class UserBaseResBO {

    @ApiModelProperty("目标组合")
    private String targetPortfolio;

    @ApiModelProperty("理财师昵称")
    private String plannerNickName;

    @ApiModelProperty("理财师半身照url")
    private String plannerImg;
}
