package ttfund.web.financialplannerapi.model.bo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/3/22
 * @Description
 */
@Data
public class EntranceBO {

    /**
     * 机构id
     */
    private String companyId;

    /**
     * 机构名称
     */
    private String companyName;

    /**
     * 基金code或投顾code
     */
    private String pcode;

    /**
     * 基金或投顾产品名称
     */
    private String pname;

    /**
     * 用户组
     */
    private String userInfo;

    /**
     * 跳转连接
     */
    private String url;

    /**
     * 页面标识，多个以逗号分隔
     */
    private String text;

    /**
     * 配置机构类型：0=非自营，1=自营
     */
    private int type;

    /**
     * 主标题文案
     */
    private String title;

    /**
     * 副标题文案
     */
    private String subtitle;
}
