package ttfund.web.financialplannerapi.model.permission;

import lombok.Data;

@Data
public class PermissionModel {

    /**
     * 通行证id
     */
    private String passportId;

    /**
     * 角色id
     */
    private Integer role;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 数据权限
     */
    private Integer dataType;

    /**
     * 机构id
     */
    private String institutionId;

    /**
     * 各个权限
     */
    private Integer chat_list;
    private Integer chat_group;
    private Integer customer_list;
    private Integer assessment_list_all;
    private Integer assessment_list_one;
    private Integer quality_list;
    private Integer quality_result;
    private Integer appeal_list;
    private Integer service_list;
    private Integer permission;
    private Integer customer_detail;

    /**
     * 删除标识 0=未删除，1=已删除
     */
    private Integer deleted;
}
