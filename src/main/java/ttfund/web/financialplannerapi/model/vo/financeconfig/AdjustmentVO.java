package ttfund.web.financialplannerapi.model.vo.financeconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "AdjustmentVO", description = "最新调仓VO")
public class AdjustmentVO {

    @ApiModelProperty(value = "调仓理由")
    private String reason;

    @ApiModelProperty(value = "调仓基金")
    private List<AdjustmentFundVO> adjustmentFunds;

}
