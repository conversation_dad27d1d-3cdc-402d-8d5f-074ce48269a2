package ttfund.web.financialplannerapi.model.vo.financeconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "FundAiVO", description = "基金AI解读VO")
public class FundAiVO {

    @ApiModelProperty(value = "基金代码")
    private String fundCode;

    @ApiModelProperty(value = "基金名称")
    private String fundName;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "一句话摘要")
    private String shortSummary;
}
