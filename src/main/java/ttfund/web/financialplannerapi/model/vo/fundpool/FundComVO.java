package ttfund.web.financialplannerapi.model.vo.fundpool;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "FundComVO对象", description = "行情获取基金的封装数据")
public class FundComVO {

    @ApiModelProperty(value = "基金代码")
    @JSONField(name = "FCODE")
    private String fundCode;

    @ApiModelProperty(value = "基金名称")
    @JSONField(name = "SHORTNAME")
    private String shortname;

    @ApiModelProperty(value = "换手率，已乘100%")
    @JSONField(name = "TURNOVER")
    private String turnover;

    @ApiModelProperty(value = "成立来最大回撤，已乘100%")
    @JSONField(name = "MAXRETRA_SE")
    private String maxretraSe;

    @ApiModelProperty(value = "二级分类，一级分类取前三位")
    @JSONField(name = "RSBTYPE")
    private String rsbType;

    @ApiModelProperty(value = "今年来收益率，已乘100%")
    @JSONField(name = "SYL_SY")
    private String sylSy;

    @ApiModelProperty(value = "近1年收益率，已乘100%")
    @JSONField(name = "SYL_Y")
    private String sylY;

    @ApiModelProperty(value = "近三年收益率，已乘100%")
    @JSONField(name = "SYL_TRY")
    private String sylTry;

    @ApiModelProperty(value = "近1年夏普，直接比值")
    @JSONField(name = "SHARP1")
    private String sharp1;

    @ApiModelProperty(value = "近3年夏普，直接比值")
    @JSONField(name = "SHARP3")
    private String sharp3;

    @ApiModelProperty(value = "上任总天数")
    @JSONField(name = "TOTALDAYS")
    private BigDecimal totalDays;

    @ApiModelProperty(value = "现任基金经理成立来最大回撤，直接比值，未乘100%")
    @JSONField(name = "MAXRETRA1")
    private String maxretra1;

    @ApiModelProperty(value = "申购状态")
    @JSONField(name = "SGZT")
    private String sgzt;

    @ApiModelProperty(value = "申购限额，单位元")
    @JSONField(name = "MAXSG")
    private String maxSg;
}
