package ttfund.web.financialplannerapi.model.vo.financeconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "FundAiDataVO", description = "基金AI解读data层VO")
public class FundAiDataVO {

    @ApiModelProperty(value = "基金代码")
    private FundAiVO data;

    @ApiModelProperty(value = "会话id")
    private String sid;

    @ApiModelProperty(value = "问答id")
    private String qid;
}
