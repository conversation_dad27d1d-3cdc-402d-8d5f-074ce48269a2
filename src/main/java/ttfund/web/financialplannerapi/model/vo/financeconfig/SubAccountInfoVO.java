package ttfund.web.financialplannerapi.model.vo.financeconfig;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "SubAccountInfoVO", description = "用户组合名称VO")
public class SubAccountInfoVO {

    @ApiModelProperty(value = "组合编号")
    @JSONField(name = "subAccountNo")
    private String portfolioNo;

    @ApiModelProperty(value = "组合名称")
    @JSONField(name = "subAccountName")
    private String portfolioName;
}
