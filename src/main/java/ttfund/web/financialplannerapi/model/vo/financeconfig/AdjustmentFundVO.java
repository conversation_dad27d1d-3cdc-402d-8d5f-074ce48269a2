package ttfund.web.financialplannerapi.model.vo.financeconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "AdjustmentFundVO", description = "调仓基金VO")
public class AdjustmentFundVO {

    @ApiModelProperty(value = "基金代码")
    private String fundCode;

    @ApiModelProperty(value = "调仓基金")
    private String fundName;

    @ApiModelProperty(value = "调仓前占比，单位%")
    private String percentBefore;

    @ApiModelProperty(value = "调仓后占比，单位%")
    private String percentAfter;
}
