package ttfund.web.financialplannerapi.model.vo.financeconfig;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "PortfolioVO", description = "用户组合响应VO")
public class PortfolioVO {

    @ApiModelProperty(value = "用户组合list")
    @JSONField(name = "SubAccountInfoList")
    private List<SubAccountInfoVO> subAccountInfoList;
}
