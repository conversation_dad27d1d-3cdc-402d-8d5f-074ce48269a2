package ttfund.web.financialplannerapi.model.vo.financeconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(value = "GroupCalResult", description = "组合回测结果")
public class GroupCalResult {

    @ApiModelProperty("组合累计收益金额")
    private String accumProfitAmount;

    @ApiModelProperty("组合累计收益率")
    private String accumProfitRate;

    @ApiModelProperty("调仓计划")
    private List<AdjustPlanEntity> adjustPlans;

    @ApiModelProperty("回测开始时间")
    private String beginDay;

    @ApiModelProperty("回测结束时间")
    private String endDay;

    @ApiModelProperty("日收益")
    private List<String> dailyProfit;

    @ApiModelProperty("友好提示")
    private String friendMsg;

    @ApiModelProperty("组合收益曲线")
    private List<LinePoint> groupProfitLine;

    @ApiModelProperty("组合权益类型")
    private String groupType;

    @ApiModelProperty("指数收益曲线")
    private List<List<LinePoint>> indexProfitLine;

    @ApiModelProperty("区间回测开始时间")
    private String intervalBeginDay;

    @ApiModelProperty("区间最大回撤")
    private String intervalDrawDown;

    @ApiModelProperty("最大回撤相对比例")
    private String drawDownPercent;

    @ApiModelProperty("区间最大回撤范围")
    private DrawDownInterval intervalDrawDownRange;

    @ApiModelProperty("区间累计收益金额")
    private String intervalProfitAmount;

    @ApiModelProperty("区间累计收益率")
    private String intervalProfitRate;

    @ApiModelProperty("组合成立以来最大回撤")
    private String maxDrawdown;

    @ApiModelProperty("收益回撤比")
    private String profitRetraceRatio;

    @ApiModelProperty("收益回撤比相对比例")
    private String profitRetraceRatioPercent;

    @ApiModelProperty("夏普比率")
    private String sharpRate;

    @ApiModelProperty("夏普比率相对比例")
    private String sharpRatePercent;

    @ApiModelProperty("波动率")
    private String volatility;

    @ApiModelProperty("区间波动率相对比例")
    private String volatilityPercent;

    @Setter
    @Getter
    @ApiModel(value = "LinePoint", description = "调仓计划")
    public static class LinePoint {

        @ApiModelProperty(value = "高度数值")
        private String height;

        @ApiModelProperty(value = "日期")
        private String workDay;

    }

    @Setter
    @Getter
    @ApiModel(value = "DrawDownInterval", description = "区间最大回撤范围")
    public static class DrawDownInterval {

        @ApiModelProperty(value = "开始日期")
        private String beginDay;

        @ApiModelProperty(value = "结束日期")
        private String endDay;

        @ApiModelProperty(value = "最高数值")
        private String maxHeight;

        @ApiModelProperty(value = "最低数值")
        private String minHeight;
    }

    @Setter
    @Getter
    @ApiModel(value = "AdjustPlanEntity", description = "调仓计划")
    public static class AdjustPlanEntity {

        @ApiModelProperty(value = "调仓日期")
        private String adjustDate;

        @ApiModelProperty(value = "基金名称")
        private String reason;

        @ApiModelProperty(value = "调仓模式 0-比例模式 1-金额模式")
        private Integer adjustMode;

        @ApiModelProperty(value = "调仓类型 0-建仓 1-主动调仓 2-再平衡调仓")
        private String adjustType;

        @ApiModelProperty(value = "调仓后金额")
        private String afterAmount;

        @ApiModelProperty(value = "调仓前金额")
        private String beforeAmount;

        @ApiModelProperty(value = "调仓无效原因（调仓无效时填写）")
        private String errMsg;

        @ApiModelProperty(value = "调仓无效原因（调仓无效时填写）")
        private List<FundAdjust> fundAdjustList;

        @ApiModelProperty(value = "调仓记录id")
        private String id;

        @ApiModelProperty(value = "调仓记录id")
        private Boolean isValid;

        @Setter
        @Getter
        @ApiModel(value = "FundAdjust", description = "调仓明细")
        public static class FundAdjust {
            @ApiModelProperty(value = "调仓前金额")
            private String beforeAmount;

            @ApiModelProperty(value = "调仓前比例")
            private String beforeRation;

            @ApiModelProperty(value = "调仓后金额")
            private String fundAmount;

            @ApiModelProperty(value = "调仓后比例")
            private String fundRation;

            @ApiModelProperty(value = "基金代码")
            private String fundCode;

            @ApiModelProperty(value = "基金名称")
            private String fundName;
        }
    }
}
