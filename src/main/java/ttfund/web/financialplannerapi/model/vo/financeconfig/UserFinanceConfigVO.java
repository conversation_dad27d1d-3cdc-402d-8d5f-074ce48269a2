package ttfund.web.financialplannerapi.model.vo.financeconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "UserFinanceConfigVO", description = "用户配置建议聚合VO")
public class UserFinanceConfigVO {

    @ApiModelProperty(value = "配置建议")
    private FinanceConfigVO config;

    @ApiModelProperty(value = "首次配置建议期")
    private String firstRecommendDate;

    @ApiModelProperty(value = "建议区间")
    private List<String> timeRange;

}
