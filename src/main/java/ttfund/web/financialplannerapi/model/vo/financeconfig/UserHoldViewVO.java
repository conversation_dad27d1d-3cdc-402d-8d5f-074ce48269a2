package ttfund.web.financialplannerapi.model.vo.financeconfig;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import ttfund.web.financialplannerapi.model.bo.financeconfig.PortfolioResBO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "UserHoldViewVO", description = "用户分析资产VO")
public class UserHoldViewVO {

    @ApiModelProperty(value = "基金持仓市值")
    @JSONField(name = "fundAsset")
    private String fundHoldAmountFormat;

    @ApiModelProperty(value = "组合持仓市值列表")
    @JSONField(name = "subAssetList")
    private List<PortfolioResBO> portfolioHolds;
}
