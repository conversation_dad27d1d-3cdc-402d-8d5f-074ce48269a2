package ttfund.web.financialplannerapi.model.vo.financeconfig;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import ttfund.web.financialplannerapi.constant.financeconfig.FinanceConfigCodeEnum;
import ttfund.web.financialplannerapi.constant.financeconfig.FinanceConfigTypeEnum;
import ttfund.web.financialplannerapi.model.bo.financeconfig.FinanceConfigFundResBO;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "FinanceConfigVO", description = "配置建议（观点）VO")
public class FinanceConfigVO {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "机构ID")
    private String institutionId;

    @ApiModelProperty(value = "观点配置类型")
    private FinanceConfigTypeEnum type;

    @ApiModelProperty(value = "用户通行证ID")
    private String userPassportId;

    @ApiModelProperty(value = "配置编号")
    private FinanceConfigCodeEnum code;

    @ApiModelProperty(value = "建议期")
    private LocalDate recommendDate;

    @ApiModelProperty(value = "建议期")
    private String recommendDateFormat;

    @ApiModelProperty(value = "调仓理由")
    private String reason;

    @ApiModelProperty(value = "策略解读")
    private String strategy;

    @ApiModelProperty(value = "策略解读纯文本")
    private String strategyText;

    @ApiModelProperty(value = "建议解读总结")
    private String summary;

    @ApiModelProperty(value = "建议解读总结纯文本")
    private String summaryText;

    @ApiModelProperty(value = "建议解读详情")
    private String details;

    @ApiModelProperty(value = "建议解读详情纯文本")
    private String detailsText;

    @ApiModelProperty(value = "货币类占比")
    private String currencyFormat;

    @ApiModelProperty(value = "债券类占比")
    private String bondFormat;

    @ApiModelProperty(value = "股票类占比")
    private String stockFormat;

    @ApiModelProperty(value = "建议类占比")
    private String commodityOtherFormat;

    @ApiModelProperty(value = "基金配置")
    @JsonIgnoreProperties({"recommendFunds","remark","creator","editor","createTime","updateTime","sort","version","deleted"})
    private List<FinanceConfigFundResBO> funds;
}
