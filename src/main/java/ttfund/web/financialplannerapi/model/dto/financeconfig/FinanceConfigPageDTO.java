package ttfund.web.financialplannerapi.model.dto.financeconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import ttfund.web.financialplannerapi.model.dto.sys.PageParamDTO;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "FinanceConfigPageDTO", description = "配置建议（观点）分页前端参数DTO")
public class FinanceConfigPageDTO extends PageParamDTO {

    @ApiModelProperty(value = "建议期（数组）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private List<LocalDate> recommendDate;

    @ApiModelProperty(value = "用户通行证id（数组）")
    private List<String> userPassportId;
}
