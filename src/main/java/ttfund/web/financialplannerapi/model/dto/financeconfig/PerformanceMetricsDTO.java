package ttfund.web.financialplannerapi.model.dto.financeconfig;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import ttfund.web.financialplannerapi.constant.ErrorCodeEnum;
import ttfund.web.financialplannerapi.constant.sys.TimeRangeEnum;
import ttfund.web.financialplannerapi.model.request.groupcal.FundDetail;
import ttfund.web.financialplannerapi.util.AssertUtils;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "PerformanceMetricsDTO", description = "业绩表现前端DTO")
public class PerformanceMetricsDTO extends AuthBaseRequest {

    @ApiModelProperty(value = "用户持仓基金及占比")
    private List<FundDetail> fundDetails;

    @ApiModelProperty(value = "配置基金及占比")
    private List<AdjustDTO> adjustList;

    @ApiModelProperty(value = "组合编号")
    private String groupNo;

    @ApiModelProperty(value = "起始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "时间区间")
    private TimeRangeEnum timeRange;

    @NotBlank(message = "软件版本【appVersion】不能为空;")
    @ApiModelProperty(value = "软件版本")
    private String appVersion;

    @NotBlank(message = "手机类型【phoneType】不能为空;")
    @ApiModelProperty(value = "手机类型")
    private String phoneType;

    @ApiModelProperty(value = "用户通行证id")
    private String userPassportId;

    public void validDate() {
        if(Objects.isNull(this.startDate)) {
            AssertUtils.notNull(this.timeRange, ErrorCodeEnum.START_DATE_OR_TIME_RANGE_NOT_FOUND);

            this.startDate = this.getStartDateByTimeRange();
        }
    }

    private LocalDate getStartDateByTimeRange() {
        switch (this.timeRange) {
            case NEAR_1_WEEK:
                return LocalDate.now().minusWeeks(1);
            case NEAR_1_MONTH:
                return LocalDate.now().minusMonths(1);
            case NEAR_3_MONTH:
                return LocalDate.now().minusMonths(3);
            case NEAR_6_MONTH:
                return LocalDate.now().minusMonths(6);
            case NEAR_1_YEAR:
                return LocalDate.now().minusYears(1);
            case NEAR_3_YEAR:
                return LocalDate.now().minusYears(3);
            case NEAR_5_YEAR:
                return LocalDate.now().minusYears(5);
            default:
                break;
        }
        return LocalDate.now();
    }
}
