package ttfund.web.financialplannerapi.model.dto.financeconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import ttfund.web.financialplannerapi.model.request.groupcal.FundDetail;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "AdjustDTO", description = "历史观点")
public class AdjustDTO {

    @ApiModelProperty(value = "建议期")
    private Long adjustDate;

    @ApiModelProperty(value = "配置基金及占比")
    private String fundPercent;

    @ApiModelProperty(value = "配置基金及占比")
    private List<FundDetail> fundDetails;
}
