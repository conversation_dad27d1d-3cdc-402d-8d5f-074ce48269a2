package ttfund.web.financialplannerapi.model.dto.sys;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "PageParamDTO", description = "聊天记录分页前端参数DTO")
public class PageParamDTO extends AuthBaseRequest {

    @ApiModelProperty(value = "页码")
    private long page = 1L;

    @ApiModelProperty(value = "条数")
    private long size = 99999999L;

}
