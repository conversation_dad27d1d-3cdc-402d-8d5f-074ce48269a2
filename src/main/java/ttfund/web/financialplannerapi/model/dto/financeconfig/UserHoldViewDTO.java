package ttfund.web.financialplannerapi.model.dto.financeconfig;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "UserHoldViewDTO", description = "用户分析资产DTO")
public class UserHoldViewDTO extends AuthBaseRequest {

    @ApiModelProperty(value = "用户分区")
    private String zone;

    @ApiModelProperty(value = "用户通行证id")
    private String userPassportId;
}
