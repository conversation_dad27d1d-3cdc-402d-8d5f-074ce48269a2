package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class ValuationPictureRequest extends AuthBaseRequest {

    @ApiModelProperty("产品编号")
    private String code;

    @ApiModelProperty("类型：pic7,pic8,pic9,pic10")
    private String type = "pic7";

    @ApiModelProperty("参数 ?后的参数")
    private String param;

    @ApiModelProperty("请求来源：0=算法服务（默认）")
    private Integer source=0;
}
