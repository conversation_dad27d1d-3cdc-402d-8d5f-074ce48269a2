package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/3/22
 * @Description 理财师浮窗请求体
 */
@Data
public class LcsWindowRequest extends AuthBaseRequest {

    @ApiModelProperty("基金code/投顾code列表/专题地址")
    private Set<String> code;

    @ApiModelProperty("页面标识 品种页4 持仓页5  专区6")
    private int pageFlag;


    @ApiModelProperty("是否需要获取自营理财师信息")
    private String forceGetLcsInfo = "false";

}
