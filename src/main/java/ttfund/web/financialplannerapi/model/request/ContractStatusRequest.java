package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ContractStatusRequest extends AuthBaseRequest {

    @ApiModelProperty("机构id")
    private String institutionId;

    @ApiModelProperty("要查询的通行证id")
    private String checkPassportid;

    @ApiModelProperty("理财师id")
    private String financialPlannerId;
}
