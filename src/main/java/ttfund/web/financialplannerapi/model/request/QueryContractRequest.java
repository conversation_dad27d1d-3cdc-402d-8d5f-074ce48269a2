package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/3/16
 * @Description 是否解约请求体
 */
@Data
public class QueryContractRequest extends AuthBaseRequest {
    @ApiModelProperty("机构id")
    private String institutionId;

    @ApiModelProperty("理财师id")
    private String financialPlannerId;
}
