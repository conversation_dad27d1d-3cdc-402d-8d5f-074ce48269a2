package ttfund.web.financialplannerapi.model.request.portfolio;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "PortfolioRequest", description = "组合名称请求参数")
public class PortfolioRequest {

    @ApiModelProperty(value = "CToken")
    @JSONField(name = "CToken")
    private String cToken;

    @ApiModelProperty(value = "UToken")
    @JSONField(name = "UToken")
    private String uToken;

    @ApiModelProperty(value = "MobileKey")
    @JSONField(name = "MobileKey")
    private String mobileKey;

    @ApiModelProperty(value = "用户交易账号")
    @JSONField(name = "UserId")
    private String customerNo;

    @ApiModelProperty(value = "组合编号,用,拼接")
    @JSONField(name = "subAccountNoList")
    private String portfolioNos;

    @ApiModelProperty(value = "手机型号")
    @JSONField(name = "phoneType")
    private String phoneType;

    @ApiModelProperty(value = "软件版本")
    @JSONField(name = "version")
    private String version;

    @ApiModelProperty(value = "App类型【例如天天基金--ttjj，东方财富--dfcf]")
    @JSONField(name = "appType")
    private String appType;
}
