package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PortfolioStyleRequest extends AuthBaseRequest {
    @ApiModelProperty("组合号")
    private String subAccountNo;
    @ApiModelProperty(value = "用户通行证id")
    private String userPassportId;
}
