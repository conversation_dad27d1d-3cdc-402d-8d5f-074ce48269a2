package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2022/8/22
 * @Description 新增预约请求
 */
@Data
public class AddAppointmentRequest extends AuthBaseRequest {
    @ApiModelProperty(value = "预约id，更新时带上")
    private String id;
    @ApiModelProperty(value = "理财师id")
    private String financialPlannerId;
    @ApiModelProperty(value = "机构id")
    private String institutionId;
    @ApiModelProperty(value = "用户预留电话，加密")
    private String phone;
    /**
     * 预约时间 格式 yyyy-MM-dd HH:mm:ss 上午固定11:30 下午固定18:00
     */
    @ApiModelProperty("预约时间 格式 yyyy-MM-dd HH:mm:ss 上午固定11:30 下午固定18:00,例如2022-09-11 11:30:00")
    private String appointment;
}
