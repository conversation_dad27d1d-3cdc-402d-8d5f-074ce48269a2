package ttfund.web.financialplannerapi.model.request.groupcal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import ttfund.web.financialplannerapi.model.dto.financeconfig.AdjustDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "AiGroup", description = "组合回测请求参数data")
public class AiGroup {

    @ApiModelProperty(value = "用户编号")
    private String customerNo;

    @ApiModelProperty(value = "回测开始时间")
    private Long startTime;

    @ApiModelProperty(value = "回测结束时间时间戳毫秒")
    private Long endTime;

    @ApiModelProperty(value = "最早配置观点基金配比明细Json格式")
    private String fundPercent;

    @ApiModelProperty(value = "回测金额,默认1w")
    private Long groupAmount;

    @ApiModelProperty(value = "组合编号")
    private String groupNo;

    @ApiModelProperty(value = "区间回测开始时间")
    private Long intervalStartTime;

    @ApiModelProperty(value = "0比例模式 1金额模式")
    private Integer modelType;

    @ApiModelProperty(value = "再平衡模式0：不平衡，1：季平衡，2：年平衡")
    private Integer rebalanceMode;

    @ApiModelProperty(value = "除最早外其余配置观点基金配比明细Json格式")
    private List<AdjustDTO> adjustList;
}
