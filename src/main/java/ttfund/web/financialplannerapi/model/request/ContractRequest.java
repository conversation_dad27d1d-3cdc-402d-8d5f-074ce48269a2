package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ContractRequest extends AuthBaseRequest {

    @ApiModelProperty("机构id")
    private String institutionId;

    @ApiModelProperty("理财师id")
    private String financialPlannerId;

    @ApiModelProperty("用户昵称")
    private String nicname;
//
//    @ApiModelProperty("入口类型 0=基金详情进入，1=直接指定理财师 默认 0")
//    private int type = 0;
}
