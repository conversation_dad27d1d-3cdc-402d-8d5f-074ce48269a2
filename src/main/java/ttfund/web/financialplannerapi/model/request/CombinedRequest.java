package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CombinedRequest extends AuthBaseRequest {
    @ApiModelProperty("理财师id")
    private String plannerid;
    @ApiModelProperty("组合号")
    private String subAccountNo;
    @ApiModelProperty("行业代码")
    private String industryCode;
    @ApiModelProperty("基金代码")
    private String fundCode;
    @ApiModelProperty(value = "指数代码", allowableValues = "000001“, ”000300“, ”399006“, ”399001“, ”399005“, ”399905“, ”H11001“, ”930950")
    private String indexCode;
    @ApiModelProperty(value = "时间范围", allowableValues = "w“, ”y“, ”3y“, ”6y“, ”n“, ”2n“, ”3n“, ”5n“, ”jn“, ”ln“, ”AT")
    private String range;
    @ApiModelProperty(value = "用户通行证id")
    private String userPassportId;
}