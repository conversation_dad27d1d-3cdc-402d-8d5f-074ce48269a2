package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2022/11/01
 * @Description 服务解约请求
 */
public class CancelContractRequest extends AuthBaseRequest {

    @ApiModelProperty("机构id")
    private String institutionId;

    public String getInstitutionId() {
        return institutionId;
    }

    public void setInstitutionId(String institutionId) {
        this.institutionId = institutionId;
    }

    @Override
    public String toString() {
        return "CancelContractRequest{" +
                "institutionId='" + institutionId + '\'' +
                '}';
    }
}
