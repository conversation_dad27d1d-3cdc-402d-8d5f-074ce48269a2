package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class FeedBackAddRequest extends AuthBaseRequest {

    @ApiModelProperty("理财师id")
    private String plannerid;
    @ApiModelProperty("机构id")
    private String institutionid;

    @ApiModelProperty("反馈意见")
    private String opinion;
    @ApiModelProperty("截图地址")
    private List<String> picture;


    public String getPlannerid() {
        return plannerid;
    }

    public void setPlannerid(String plannerid) {
        this.plannerid = plannerid;
    }

    public String getInstitutionid() {
        return institutionid;
    }

    public void setInstitutionid(String institutionid) {
        this.institutionid = institutionid;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public List<String> getPicture() {
        return picture;
    }

    public void setPicture(List<String> picture) {
        this.picture = picture;
    }
}
