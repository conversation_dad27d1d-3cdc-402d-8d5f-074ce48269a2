package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2022/8/22
 * @Description 电话预约查询请求
 */
@Data
public class AppointmentQueryRequest extends AuthBaseRequest {
    /**
     * 理财师id 理财师的passportId
     */
    @ApiModelProperty(value = "理财师id")
    private String financialPlannerId;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private String institutionId;
}
