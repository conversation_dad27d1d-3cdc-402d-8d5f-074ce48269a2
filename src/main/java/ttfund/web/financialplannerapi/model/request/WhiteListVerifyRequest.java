package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;

public class WhiteListVerifyRequest extends AuthBaseRequest {

    @ApiModelProperty(value = "白名单类型，0=算法服务(默认)")
    private int type;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
