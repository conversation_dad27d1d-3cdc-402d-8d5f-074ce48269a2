package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class EvaluateAddRequest extends AuthBaseRequest {

    @ApiModelProperty("理财师id")
    private String plannerid;
    @ApiModelProperty("机构id")
    private String institutionid;
    @ApiModelProperty("服务态度")
    private int attitudescore;
    @ApiModelProperty("专业程度")
    private int professionscore;
    @ApiModelProperty("综合评分")
    private int compositescore;

    @ApiModelProperty("电话外呼会话Id")
    private String serviceId;


    public String getPlannerid() {
        return plannerid;
    }

    public void setPlannerid(String plannerid) {
        this.plannerid = plannerid;
    }

    public String getInstitutionid() {
        return institutionid;
    }

    public void setInstitutionid(String institutionid) {
        this.institutionid = institutionid;
    }

    public int getAttitudescore() {
        return attitudescore;
    }

    public void setAttitudescore(int attitudescore) {
        this.attitudescore = attitudescore;
    }

    public int getProfessionscore() {
        return professionscore;
    }

    public void setProfessionscore(int professionscore) {
        this.professionscore = professionscore;
    }

    public int getCompositescore() {
        return compositescore;
    }

    public void setCompositescore(int compositescore) {
        this.compositescore = compositescore;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }
}
