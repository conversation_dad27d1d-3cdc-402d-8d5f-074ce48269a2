package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/3/12 16:14
 * @description
 */
@Data
public class TTFundUserContractRequest extends AuthBaseRequest {


    @ApiModelProperty("需要自动签约的用户，并返回签约的理财师")
    private Set<String> userNeedContractPassportSet;

    @ApiModelProperty("查询对应签约的理财师，非签约不返回")
    private Set<String> userPassportSet;

}
