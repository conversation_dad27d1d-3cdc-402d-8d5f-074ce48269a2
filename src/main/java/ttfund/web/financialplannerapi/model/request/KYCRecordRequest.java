package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;


@Data
public class KYCRecordRequest extends AuthBaseRequest {

    @ApiModelProperty("策略名称")
    private String investmentName;

    @ApiModelProperty("fcode")
    private String code;

    @ApiModelProperty("计划投资期限")
    private String plannedInvestmentPeriod;

    @ApiModelProperty("期望年化收益率")
    private String rateOfYear;

    @ApiModelProperty("期望最大回撤")
    private String retrace;

    @ApiModelProperty("机构id")
    private String institutionId;

    public boolean cheack(){
        return StringUtils.isNotEmpty(investmentName) && StringUtils.isNotEmpty(code) && StringUtils.isNotEmpty(plannedInvestmentPeriod)
                && StringUtils.isNotEmpty(rateOfYear) && StringUtils.isNotEmpty(retrace) && StringUtils.isNotEmpty(this.getPassportid())
                && StringUtils.isNotEmpty(this.getUserid()) && StringUtils.isNotEmpty(institutionId);
    }
}
