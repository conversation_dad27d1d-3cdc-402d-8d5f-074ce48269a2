package ttfund.web.financialplannerapi.model.request.groupcal;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value = "GroupRequest", description = "组合回测请求参数")
public class GroupRequest {

    @ApiModelProperty(value = "cToken")
    @JSONField(name = "ctoken")
    private String ctoken;

    @ApiModelProperty(value = "uToken")
    @JSONField(name = "utoken")
    private String utoken;

    @ApiModelProperty(value = "pToken")
    @JSONField(name = "pToken")
    private String ptoken;

    @ApiModelProperty(value = "设备id")
    @JSONField(name = "deviceid")
    private String deviceId;

    @ApiModelProperty(value = "用户交易账号")
    @JSONField(name = "userid")
    private String userid;

    @ApiModelProperty(value = "手机型号")
    @JSONField(name = "phoneType")
    private String phoneType;

    @ApiModelProperty(value = "软件版本")
    @JSONField(name = "appVersion")
    private String appVersion;

    @ApiModelProperty(value = "App类型【例如天天基金--ttjj，东方财富--dfcf]")
    @JSONField(name = "appType")
    private String appType;

    @ApiModelProperty(value = "参数")
    @JSONField(name = "data")
    private AiGroup data;
}
