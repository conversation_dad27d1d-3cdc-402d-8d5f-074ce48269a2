package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/12 8:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class ChangeTaskStatusReq extends AuthBaseRequest {
    private String institutionId;

    private String newPlannerId;

    private String changeType;

    private List<String> userList;
}
