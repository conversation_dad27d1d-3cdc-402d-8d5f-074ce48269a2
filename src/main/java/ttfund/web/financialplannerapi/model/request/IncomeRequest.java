package ttfund.web.financialplannerapi.model.request;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import ttfund.web.financialplannerapi.constant.IndexCode;
import ttfund.web.financialplannerapi.constant.TimeRange;

@EqualsAndHashCode(callSuper = true)
@Data
public class IncomeRequest extends AuthBaseRequest {
    @ApiModelProperty(value = "指数代码", allowableValues = "000001“, ”000300“, ”399006“, ”399001“, ”399005“, ”399905“, ”H11001“, ”930950")
    private String indexCode;
    @ApiModelProperty(value = "时间范围", allowableValues = "w“, ”y“, ”3y“, ”6y“, ”n“, ”2n“, ”3n“, ”5n“, ”jn“, ”ln“, ”AT")
    private String range;
    @ApiModelProperty("组合号")
    private String subAccountNo;
    @ApiModelProperty(value = "用户通行证id")
    private String userPassportId;
}
