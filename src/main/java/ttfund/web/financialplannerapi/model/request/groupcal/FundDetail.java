package ttfund.web.financialplannerapi.model.request.groupcal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "基金持仓明细")
public class FundDetail {

    @ApiModelProperty(value = "基金代码")
    private String fundCode;

    @ApiModelProperty(value = "持仓比例")
    private String fundRation;
}