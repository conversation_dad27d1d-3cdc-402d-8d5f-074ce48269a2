package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/12 16:18
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TTFundUserContractResponse {

    @ApiModelProperty("用户通行证id")
    private String userPassportId;

    @ApiModelProperty("理财师通行证id")
    private String plannerPassportIdId;

    @ApiModelProperty("理财师工号")
    private String plannerOaId;
}
