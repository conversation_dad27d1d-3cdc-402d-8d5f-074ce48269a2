package ttfund.web.financialplannerapi.model.reponse.financeconfig;

import com.alibaba.fastjson2.annotation.JSONField;
import com.ttfund.web.core.constant.ResultCode;
import com.ttfund.web.core.exception.ResponseException;
import com.ttfund.web.core.model.ApiResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 手机接口获取组成名称返回
 * <AUTHOR>
 */
@Setter
@Getter
public class FundAiResponse<T> implements Serializable {

    /**
     * 结果参数
     */
    @JSONField(name = "result")
    private T result;
    /**
     * 接口状态-0表示成功
     */
    @JSONField(name = "errorCode")
    private int errorCode;
    /**
     * 错误信息
     */
    @JSONField(name = "codeMessage")
    private String codeMessage;

    /**
     * 错误信息
     */
    @JSONField(name = "message")
    private String message;
    /**
     * 是否成功
     */
    @JSONField(name = "success")
    private Boolean success;



    public FundAiResponse() {
    }

    public FundAiResponse(T result, Integer errorCode, String codeMessage) {
        this.result = result;
        this.errorCode = errorCode;
        this.codeMessage = codeMessage;
    }

    //region builder
    public static <T,T2> ApiResponse<T,T2> succeed() {
        return of(null, ResultCode.errorcode0, "");
    }
    public static <T,T2> ApiResponse<T,T2> succeed(String msg) {
        return of(null, ResultCode.errorcode0, msg);
    }

    public static <T,T2> ApiResponse<T,T2> succeed(T model, String msg) {
        return of(model, ResultCode.errorcode0, msg);
    }

    public static <T,T2> ApiResponse<T,T2> succeed(T model) {
        return of(model, ResultCode.errorcode0, "");
    }

    public static <T,T2> ApiResponse<T,T2> of(T datas, Integer code, String msg) {
        return new ApiResponse(datas, code, msg);
    }

    public static <T,T2> ApiResponse<T,T2> failed(String msg) {
        return of(null, ResultCode.errorcode500, msg);
    }

    public static <T,T2> ApiResponse<T,T2> failed(T model, String msg) {
        return of(model, ResultCode.errorcode500, msg);
    }

    public static <T,T2> ApiResponse<T,T2> failed(ResponseException responseException) {
        return of(null, responseException.getErrorCode(), responseException.getMessage());
    }

    public static <T,T2> ApiResponse<T,T2> failed(ResponseException responseException, String message) {
        return of(null, responseException.getErrorCode(), message);
    }
}
