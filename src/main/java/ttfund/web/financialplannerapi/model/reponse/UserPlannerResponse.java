package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("与用户绑定")
@Data
public class UserPlannerResponse {

    @ApiModelProperty("理财师通行证id")
    private String financialPlannerId;

    @ApiModelProperty("机构id")
    private String institutionId;

    @ApiModelProperty("机构名称")
    private String institutionName;

    @ApiModelProperty("理财师真实姓名")
    private String financialPlannerName;

    @ApiModelProperty("理财师照片地址")
    private String imgUrl;

    @ApiModelProperty("展示优先级")
    private int priority;

}
