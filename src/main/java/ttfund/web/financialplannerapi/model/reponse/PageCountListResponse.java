package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class PageCountListResponse<T> {

    @ApiModelProperty("总数")
    private long totalCount;

    @ApiModelProperty("信息列表")
    private List<T> list;

    /**
     * 构建空分页返回对象
     * @return 分页返回对象
     * @param <T> 泛型
     */
    public static <T> PageCountListResponse<T> ofEmpty() {
        return PageCountListResponse.<T>builder().totalCount(0L).list(new ArrayList<>()).build();
    }

    /**
     * 构建分页返回对象
     * @param list 分页数据
     * @param totalCount 总数
     * @return 分页返回对象
     * @param <T> 泛型
     */
    public static <T> PageCountListResponse<T> of(List<T> list, long totalCount) {
        return PageCountListResponse.<T>builder().totalCount(totalCount).list(list).build();
    }
}
