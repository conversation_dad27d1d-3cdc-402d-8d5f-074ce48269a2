package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("收益表现")
@Data
public class IncomeResponse {
    @ApiModelProperty("我的收益率")
    private String profitRate;
    @ApiModelProperty("基准收益率")
    private String baseEarnings;
    @ApiModelProperty("我的收益率详情")
    private List<myEarn> myEarnList;
    @ApiModelProperty("基准收益率详情")
    private List<baseEarn> baseEarnList;

    @ApiModel("我的收益详情")
    @Data
    public static class myEarn {
        @ApiModelProperty("日期")
        private String date;
        @ApiModelProperty("收益率")
        private String yield;
    }

    @ApiModel("基准收益详情")
    @Data
    public static class baseEarn {
        @ApiModelProperty("日期")
        private String date;
        @ApiModelProperty("收益率")
        private String yield;
    }

}
