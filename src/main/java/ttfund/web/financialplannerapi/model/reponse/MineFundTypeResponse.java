package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("持仓基金分类")
@Data
public class MineFundTypeResponse {
    @ApiModelProperty("持仓基金分类列表")
    private List<MineFundType> mineFundTypes;
    @ApiModel("持仓基金分类明细")
    @Data
    public static class MineFundType {
        @ApiModelProperty("基金类型")
        private String fundTypeName;
        @ApiModelProperty("比例")
        private String percentage;
        @ApiModelProperty("类型代码")
        private String type;
    }
}
