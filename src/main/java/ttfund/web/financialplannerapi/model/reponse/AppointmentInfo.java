package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/22
 * @Description 预约信息
 */
@Data
public class AppointmentInfo {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private String institutionId;
    /**
     * 理财师id 理财师的passportId
     */
    @ApiModelProperty(value = "理财师id")
    private String financialPlannerId;
    /**
     * 用户通行证id
     */
    @ApiModelProperty(value = "用户通行证id")
    private String passportId;
    /**
     * 用户手机号
     */
    @ApiModelProperty
    private String phone;
    /**
     * 预约时段 yyyy-MM-dd HH:mm 固定上午11:30 和下午18:00
     */
    @ApiModelProperty(value = "预约时段 yyyy-MM-dd HH:mm 固定上午11:30 和下午18:00")
    private Date appointment;
    /**
     * 预约状态标识 0=未回访，1=回访已接通，2=回访未接通,3=过期未回访
     */
    @ApiModelProperty(value = "预约状态标识 0=未回访，1=回访已接通，2=回访未接通,3=过期未回访")
    private Integer status;

}
