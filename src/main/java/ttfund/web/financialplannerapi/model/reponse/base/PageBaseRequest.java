package ttfund.web.financialplannerapi.model.reponse.base;

import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Date 2021/10/27 8:58
 */
public class PageBaseRequest extends AuthBaseRequest {
    private int pagesize;
    private int pageindex;

    public PageBaseRequest() {
        pageindex=1;
        pagesize = 10;
    }

    public int getPagesize() {
        return pagesize;
    }

    public void setPagesize(int pagesize) {
        this.pagesize = pagesize;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }
}
