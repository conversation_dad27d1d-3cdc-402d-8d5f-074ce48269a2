package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import ttfund.web.financialplannerapi.model.api.IndustrySourcesResponse;

import java.util.List;

@ApiModel("行业列表")
@Data
public class ListIndustryResponse {
    @ApiModelProperty("行业列表")
    List<Industry> industrys;

    @ApiModelProperty("行业来源列表")
    List<IndustrySourcesResponse> industrySources;


    @ApiModel("行业详情")
    @Data
    public static class Industry {
        @ApiModelProperty("行业代码")
        private String industryCode;
        @ApiModelProperty("行业名称")
        private String industryName;
        @ApiModelProperty("行业占比")
        private String ratio;
        @ApiModelProperty("机会评分")
        private String chanceScore;
        @ApiModelProperty("风险评分")
        private String riskScore;
    }

}
