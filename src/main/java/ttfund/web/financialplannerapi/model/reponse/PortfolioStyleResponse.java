package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import ttfund.web.financialplannerapi.model.api.AssetDiagnosisV2Response;
import ttfund.web.financialplannerapi.model.api.InvestStyleResponse;
import ttfund.web.financialplannerapi.model.api.InvestStyleResult;

import java.util.List;

@ApiModel("股票持仓风格")
@Data
public class PortfolioStyleResponse {

    @ApiModelProperty("组合风格列表")
    private List<PortfolioStyle> portfolioStyles;

    @ApiModelProperty("投资风格结果列表")
    private List<InvestStyleResult> investStyleResults;

    @ApiModel("组合风格详情")
    @Data
    public static class PortfolioStyle {
        @ApiModelProperty("规模类型")
        private String scaleType;
        @ApiModelProperty("风格")
        private String style;
        @ApiModelProperty("占比")
        private String ratio;
    }
}
