package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import ttfund.web.financialplannerapi.model.api.IndustryAnalysisResponse;

import java.util.List;

@ApiModel("财务诊断综合响应")
@Data
public class FinanceDiagCombinedResponse {

    @ApiModelProperty("持仓基金分类响应")
    private MineFundTypeResponse mineFundTypeResponse;

    @ApiModelProperty("用户持仓基金响应")
    private UserHoldResponse userHoldResponse;

    @ApiModelProperty("收益表现响应")
    private IncomeResponse incomeResponse;

    @ApiModelProperty("持仓穿透响应")
    private HoldingPenetrationResponse holdingPenetrationResponse;

    @ApiModelProperty("股票持仓风格响应")
    private PortfolioStyleResponse portfolioStyleResponse;

    @ApiModelProperty("组合行业分析响应列表")
    private List<IndustryAnalysisResponse> portfolioIndustryResponse;

    @ApiModelProperty("行业列表响应")
    private ListIndustryResponse listIndustryResponse;

    @ApiModelProperty("债券穿透响应")
    private BondPenetrationResponse bondPenetrationResponse;
}
