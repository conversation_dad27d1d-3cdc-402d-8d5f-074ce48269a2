package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/22
 * @Description 预约可用时段响应
 */
@Data
public class AppointmentAvailableResponse {
    /**
     * key 为日期 MM-dd list存上午下午可否预约的状态 0=可预约，1=不可预约
     */
    @ApiModelProperty(value = "key 为日期 yyyy-MM-dd list存上午下午可否预约的状态 0=可预约，1=用户存在预约冲突(不可预约)，2=理财师预约已满(不可预约)，3=该时间段已过期(不可预约), 4=预约结束不足30分钟(不可预约)")
    private Map<String, List<Integer>> appointment;
}
