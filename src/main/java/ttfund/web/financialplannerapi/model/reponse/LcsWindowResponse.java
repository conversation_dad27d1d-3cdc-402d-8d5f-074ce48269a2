package ttfund.web.financialplannerapi.model.reponse;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/3/22
 * @Description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LcsWindowResponse {
    @ApiModelProperty("机构id")
    private String institutionId;

    @ApiModelProperty("机构名称")
    private String institutionName;

    @ApiModelProperty("悬浮窗显示图片url")
    private String defaultImg;

    @ApiModelProperty("跳转聊天url")
    private String url;

    @ApiModelProperty("理财师昵称")
    private String nickName;

    @ApiModelProperty("基金code/投顾code列表/专题地址")
    private String code;

    @ApiModelProperty("主标题文案")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String title;

    @ApiModelProperty("副标题文案")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String subtitle;
}
