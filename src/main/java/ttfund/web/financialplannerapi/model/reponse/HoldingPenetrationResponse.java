package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("持仓穿透")
@Data
public class HoldingPenetrationResponse {

    @ApiModelProperty("持仓穿透列表")
    private List<HoldingPenetration> holdingPenetrations;

    @ApiModel("持仓穿透详情")
    @Data
    public static class HoldingPenetration {
        @ApiModelProperty("占比")
        private String proportion;
        @ApiModelProperty("分类名")
        private String typeName;
        @ApiModelProperty("分类类型")
        private String type;
    }
}
