package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import ttfund.web.financialplannerapi.model.api.BoundSourcesResponse;

import java.util.List;

@ApiModel("债券穿透")
@Data
public class BondPenetrationResponse {
    @ApiModelProperty("债券穿透列表")
    private List<BondPenetration> bondPenetrations;

    @ApiModelProperty("债券来源列表")
    private List<BoundSourcesResponse> boundSources;

    @ApiModel("债券穿透详情")
    @Data
    public static class BondPenetration {
        @ApiModelProperty("债券类型代码")
        private String bondTypeCode;
        @ApiModelProperty("债券类型名称")
        private String bondTypeName;
        @ApiModelProperty("加权占比")
        private String weightedRatio;
    }
}
