package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2024-02-05 11:30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InstitutionConfigInfo {
    @ApiModelProperty("机构id")
    private String institutionId;

    @ApiModelProperty("logo")
    private String logoUrl;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("机构名称")
    private String institutionName;

    @ApiModelProperty("顶部黄条开关")
    private boolean topSwitch;

    @ApiModelProperty("黄条文案")
    private String topContent;

    @ApiModelProperty("弹窗开关")
    private boolean popupSwitch;

    @ApiModelProperty("弹窗标题")
    private String popupTitle;

    @ApiModelProperty("弹窗文案")
    private String popupContent;

    @ApiModelProperty("理财师服务协议")
    private String serviceUrl;

    @ApiModelProperty("风险揭示书")
    private String riskUrl;

    @ApiModelProperty("投顾服务协议")
    private String tgUrl;

    @ApiModelProperty("个人信息协议")
    private String userUrl;
}
