package ttfund.web.financialplannerapi.model.reponse;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/24
 * @Description
 */
@Data
public class UserBaseInfo {
    private String customerNo;
    private Boolean isVisitor;
    private String encryptMobileTel;
    private String maskMobileTel;
    private String md5MobileTel;
    @ApiModelProperty(value = "性别：0=男，1=女")
    private String sex;
    private String customerName;

    @ApiModelProperty("生日")
    @JSONField(name = "Birthday")
    private String birthday;

    @ApiModelProperty("用户风险等级")
    @JSONField(name = "CustRiskLevel")
    private String custRiskLevelCode;
}

