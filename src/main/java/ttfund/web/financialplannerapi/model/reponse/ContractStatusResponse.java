package ttfund.web.financialplannerapi.model.reponse;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import ttfund.web.financialplannerapi.model.po.mysqlpo.UserRolePO;

@ApiModel("签约状态结果")
@Data
public class ContractStatusResponse {

    @ApiModelProperty("是否签约")
    private Boolean status = false;

    @ApiModelProperty("是否绑定")
    private Boolean bind = false;

    @ApiModelProperty("是否需要更换理财师")
    private Boolean change = false;

    @ApiModelProperty("理财师通行证id")
    private String financialPlannerId;

    @ApiModelProperty("是否白名单")
    private Boolean whiteList = false;

    @ApiModelProperty("理财师信息")
    private UserRolePO financialPlanner;

    @ApiModelProperty("协议名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String serviceAgreementName;

    @ApiModelProperty("协议地址")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String serviceAgreementLook;

    @ApiModelProperty("风险揭示书名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String riskNotesName;

    @ApiModelProperty("风险揭示书地址")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String riskNotesLook;

    @ApiModelProperty("用户信息名字")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String userInfoName;

    @ApiModelProperty("用户信地址")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String userInfoLook;

    @ApiModelProperty("提示文字")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String userInfoNotes;

    @ApiModelProperty("投顾协议名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String investmentAgreementName;

    @ApiModelProperty("投顾协议地址")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String investmentAgreementLook;

    @ApiModelProperty("是否自营机构")
    private Boolean ownFlag;

    @ApiModelProperty("logo")
    private String logoUrl;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("机构名称")
    private String institutionName;

    @ApiModelProperty("顶部黄条开关")
    private boolean topSwitch;

    @ApiModelProperty("黄条文案")
    private String topContent;

    @ApiModelProperty("弹窗开关")
    private boolean popupSwitch;

    @ApiModelProperty("弹窗标题")
    private String popupTitle;

    @ApiModelProperty("弹窗文案")
    private String popupContent;
}
