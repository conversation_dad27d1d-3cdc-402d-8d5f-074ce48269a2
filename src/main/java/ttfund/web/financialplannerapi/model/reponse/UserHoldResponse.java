package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import ttfund.web.financialplannerapi.model.api.AssetDiagnosisV2Response;
import ttfund.web.financialplannerapi.model.api.FinancialPlannerResponse;

import java.util.List;

@ApiModel("用户持仓基金")
@Data
public class UserHoldResponse {

    @ApiModelProperty("一级基金类型列表")
    private List<FundTypeLevel1> fundTypeLevel1List;

    @Data
    public static class FundTypeLevel1 {

        @ApiModelProperty("基金类型名称 (例如: 股票类, 债券类)")
        private String fundTypeName;

        @ApiModelProperty("该类型下的资产总额")
        private String asset;

        @ApiModelProperty("该类型资产占总资产的百分比")
        private String percentage;

        @ApiModelProperty("类型代码")
        private String type;

        @ApiModelProperty("该一级分类下的二级基金类型列表")
        private List<FundTypeLevel2> fundTypeLevel2List;
    }

    @Data
    public static class FundTypeLevel2 {

        @ApiModelProperty("基金类型名称 (例如: 混合型, 指数型)")
        private String fundTypeName;

        @ApiModelProperty("该类型下的资产总额")
        private String asset;

        @ApiModelProperty("该类型资产占总资产的百分比")
        private String percentage;

        @ApiModelProperty("该二级分类下的具体基金列表")
        private List<Fund> fundList;
    }

    @Data
    public static class Fund {

        @ApiModelProperty("基金代码")
        private String fundCode;

        @ApiModelProperty("基金名称")
        private String fundName;

        @ApiModelProperty("该基金占总资产的比例")
        private String ratio;

        @ApiModelProperty("该基金的资产金额")
        private String asset;

        @ApiModelProperty("基金标签列表")
        private List<AssetDiagnosisV2Response.Label> labels;

        @ApiModelProperty("持仓收益")
        private String holdProfit;

        @ApiModelProperty("持有收益")
        private String constantProfit;

        @ApiModelProperty("盈利能力")
        private String profitability;

        @ApiModelProperty("盈利能力超出同类比例")
        private String profitabilityProportion;

        @ApiModelProperty("防守能力")
        private String defensiveAbility;

        @ApiModelProperty("防守能力超出同类比例")
        private String defensiveAbilityProportion;

        @ApiModelProperty("关联主题")
        private List<FinancialPlannerResponse.FundTopicCache> relateThemes;
        @ApiModelProperty("近一年超额收益率")
        private String excessIndexY;
        @ApiModelProperty("近一年跟踪误差")
        private String trkError1;
        @ApiModelProperty("近一年收益率")
        private String profitY;
    }
}