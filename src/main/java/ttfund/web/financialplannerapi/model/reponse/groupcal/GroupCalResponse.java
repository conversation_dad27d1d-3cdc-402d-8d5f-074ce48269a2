package ttfund.web.financialplannerapi.model.reponse.groupcal;

import com.alibaba.fastjson2.annotation.JSONField;
import com.ttfund.web.core.constant.ResultCode;
import com.ttfund.web.core.exception.ResponseException;
import com.ttfund.web.core.model.ApiResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 组合回测接口响应
 * <AUTHOR>
 */
@Setter
@Getter
public class GroupCalResponse<T> implements Serializable {

    /**
     * 结果参数
     */
    @JSONField(name = "data")
    private T data;
    /**
     * 接口状态-0表示成功
     */
    @JSONField(name = "errorCode")
    private int errorCode;
    /**
     * 秒数
     */
    @JSONField(name = "秒数")
    private String time;

    /**
     * 错误信息
     */
    @J<PERSON>NField(name = "message")
    private String message;
    /**
     * 是否成功:1-成功,0-失败
     */
    @JSONField(name = "result")
    private Integer result;



    public GroupCalResponse() {
    }

    public GroupCalResponse(T data, Integer errorCode, String message) {
        this.data = data;
        this.errorCode = errorCode;
        this.message = message;
    }

    //region builder
    public static <T,T2> ApiResponse<T,T2> succeed() {
        return of(null, ResultCode.errorcode0, "");
    }
    public static <T,T2> ApiResponse<T,T2> succeed(String msg) {
        return of(null, ResultCode.errorcode0, msg);
    }

    public static <T,T2> ApiResponse<T,T2> succeed(T model, String msg) {
        return of(model, ResultCode.errorcode0, msg);
    }

    public static <T,T2> ApiResponse<T,T2> succeed(T model) {
        return of(model, ResultCode.errorcode0, "");
    }

    public static <T,T2> ApiResponse<T,T2> of(T datas, Integer code, String msg) {
        return new ApiResponse(datas, code, msg);
    }

    public static <T,T2> ApiResponse<T,T2> failed(String msg) {
        return of(null, ResultCode.errorcode500, msg);
    }

    public static <T,T2> ApiResponse<T,T2> failed(T model, String msg) {
        return of(model, ResultCode.errorcode500, msg);
    }

    public static <T,T2> ApiResponse<T,T2> failed(ResponseException responseException) {
        return of(null, responseException.getErrorCode(), responseException.getMessage());
    }

    public static <T,T2> ApiResponse<T,T2> failed(ResponseException responseException, String message) {
        return of(null, responseException.getErrorCode(), message);
    }
}
