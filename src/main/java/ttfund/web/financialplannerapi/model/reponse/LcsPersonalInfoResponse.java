package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class LcsPersonalInfoResponse {
    /**
     * 理财师通行证id
     */
    @ApiModelProperty("理财师通行证id")
    private String financialPlannerId;

    @ApiModelProperty("理财师昵称")
    private String financialPlannerNickName;

    /**
     * 特色标签
     */
    @ApiModelProperty("特色标签")
    private String characteristics;

    /**
     * 个人简介
     */
    @ApiModelProperty("个人简介")
    private String personalProfile;

    /**
     * 从业年限
     */
    @ApiModelProperty("从业年限")
    private String workYear;

    /**
     * 从业公司
     */
    @ApiModelProperty("从业公司")
    private String institutionName;

    /**
     * 机构id
     */
    @ApiModelProperty("机构id")
    private String institutionId;

    /**
     * 职业编号
     */
    @ApiModelProperty("职业编号")
    private String workNumber;

    /**
     * 上半身头像
     */
    @ApiModelProperty("上半身头像")
    private String img;

    @ApiModelProperty("是否加V")
    private Boolean v;
}