package ttfund.web.financialplannerapi.model.reponse;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/8/24
 * @Description 预约信息查询响应
 */
@Data
public class AppointmentInfoResponse {
    /**
     * 主键
     */
    private String id;
    /**
     * 机构id
     */
    private String institutionId;
    /**
     * 理财师id 理财师的passportId
     */
    private String financialPlannerId;
    /**
     * 用户通行证id
     */
    private String passportId;
    /**
     * 用户手机号( 掩码加密
     */
    private String maskMobileTel;
    /**
     * 用户手机号 (AES加密
     */
    private String encryptMobileTel;
    /**
     * 预约时段 yyyy-MM-dd HH:mm 固定上午11:30 和下午18:00
     */
    private String appointment;
    /**
     * 预约状态标识 0=未回访，1=已回访，2=过期未回访
     */
    @ApiModelProperty(value = "0=该预约信息可用，1=该预约信息不可用")
    private Integer status;

    /**
     * 用户姓名
     */
    private String customerName;

    /**
     * 用户性别
     */
    @ApiModelProperty(value = "用户性别，0=男，1=女")
    private String sex;

    /**
     * 理财师信息
     */
    @ApiModelProperty("理财师信息")
    private LcsPersonalInfoResponse lcsPersonalInfoResponse;

    @ApiModelProperty("机构名称")
    private String institutionName;

    @ApiModelProperty("logo")
    private String logoUrl;

    @ApiModelProperty("标题")
    private String title;
}
