package ttfund.web.financialplannerapi.model.reponse;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UserPortraitApiResult <T> {
    private Boolean Succeed;
    private T Result;
    private String Message;
    private String OldMessage;
    private String CodeMessage;
    private List<String> ErrorMessages;
    private String ErrorCode;
    private String preValue;
}
