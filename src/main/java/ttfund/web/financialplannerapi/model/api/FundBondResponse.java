package ttfund.web.financialplannerapi.model.api;

import lombok.Data;

import java.util.List;

@Data
public class FundBondResponse {
    /**
     * 结果参数
     */
    private List<FundBondInvestDistriResponse> data;

    @Data
    public static class FundBondInvestDistriResponse {
        /**
         * 基金代码
         */
        private String FCODE;
        /**
         * 报告日期
         */
        private String REPORTDATE;
        /**
         * 债券新分类 信用债1 利率债2 其他4 可转债3
         */
        private String BONDTYPENEW;
        /**
         * 占净值表
         */
        private String PCTNV;
    }
}
