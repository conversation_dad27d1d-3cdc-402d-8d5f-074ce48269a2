package ttfund.web.financialplannerapi.model.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

@Data
@ApiModel("行业分析响应")
public class IndustryAnalysisResponse {

    @ApiModelProperty("行业代码")
    private String industryCode;

    @ApiModelProperty("行业名称")
    private String industryName;

    @ApiModelProperty("生成时间")
    private String generateTime;

    @ApiModelProperty("摘要")
    private String summary;

    @ApiModelProperty("近期表现")
    private String recentPerformance;

    @ApiModelProperty("投资前景")
    private String investProspect;

    @ApiModelProperty("投资参考")
    private String investReference;

    @ApiModelProperty("参考资料列表")
    private List<Reference> references;

    @ApiModelProperty("行业指标")
    private IndustryQuota industryQuota;

    @Data
    @ApiModel("参考资料")
    public static class Reference {

        @ApiModelProperty("标题")
        private String title;

        @ApiModelProperty("URL")
        private String url;

        @ApiModelProperty("内容")
        private String content;

        @ApiModelProperty("图标URL")
        private String iconUrl;

        @ApiModelProperty("评分")
        private String score;

        @ApiModelProperty("名称")
        private String name;

        @ApiModelProperty("类型")
        private String type;

        @ApiModelProperty("日期时间")
        private String datetime;
    }

    @Data
    @ApiModel("行业指标")
    public static class IndustryQuota {

        @ApiModelProperty("证券代码")
        private String secCode;

        @ApiModelProperty("证券名称")
        private String secName;

        @ApiModelProperty("周收益率")
        private Double weekSyl;

        @ApiModelProperty("月收益率")
        private Double monthSyl;

        @ApiModelProperty("季度收益率")
        private Double quarterSyl;

        @ApiModelProperty("半年收益率")
        private Double halfSyl;

        @ApiModelProperty("年收益率")
        private Double yearSyl;

        @ApiModelProperty("周排名")
        private Integer weekRank;

        @ApiModelProperty("月排名")
        private Integer monthRank;

        @ApiModelProperty("季度排名")
        private Integer quarterRank;

        @ApiModelProperty("半年排名")
        private Integer halfRank;

        @ApiModelProperty("年排名")
        private Integer yearRank;

        @ApiModelProperty("表格描述")
        private String tableDesc;
    }
}
