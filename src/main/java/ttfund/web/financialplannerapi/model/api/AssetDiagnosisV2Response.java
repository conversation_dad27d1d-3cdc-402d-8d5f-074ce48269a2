package ttfund.web.financialplannerapi.model.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("资产诊断V2版本响应体")
@Data
public class AssetDiagnosisV2Response {
    @ApiModelProperty("一级基金类型列表")
    private List<FundTypeLevel1> fundTypeLevel1List;
    @ApiModelProperty("每日收益列表")
    private List<DailyProfit> dailyProfits;
    @ApiModelProperty("饼图资产分布")
    private List<PieAsset> pieAssets;
    @ApiModelProperty("投资风格比例")
    private List<InvestStyleRatio> investStyleRatios;
    @ApiModelProperty("行业评分比例")
    private List<IndustryScoreRatio> industryScoreRatios;
    @ApiModelProperty("债券类型比例")
    private List<BondRatio> bondRatios;

    @ApiModel("一级基金类型")
    @Data
    public static class FundTypeLevel1 {
        @ApiModelProperty("基金类型名称 (例如: 股票类, 债券类)")
        private String fundTypeName;
        @ApiModelProperty("该类型下的资产总额")
        private String asset;
        @ApiModelProperty("该类型资产占总资产的百分比")
        private String percentage;
        @ApiModelProperty("该一级分类下的二级基金类型列表")
        private List<FundTypeLevel2> fundTypeLevel2List;
    }

    @ApiModel("二级基金类型")
    @Data
    public static class FundTypeLevel2 {
        @ApiModelProperty("基金类型名称 (例如: 混合型, 指数型)")
        private String fundTypeName;
        @ApiModelProperty("该类型下的资产总额")
        private String asset;
        @ApiModelProperty("该类型资产占总资产的百分比")
        private String percentage;
        @ApiModelProperty("该二级分类下的具体基金列表")
        private List<Fund> fundList;
    }

    @ApiModel("单个基金详情")
    @Data
    public static class Fund {
        @ApiModelProperty("基金代码")
        private String fundCode;
        @ApiModelProperty("基金名称")
        private String fundName;
        @ApiModelProperty("该基金占总资产的比例")
        private String ratio;
        @ApiModelProperty("该基金的资产金额")
        private String asset;
        @ApiModelProperty("基金标签列表")
        private List<Label> labels;
        @ApiModelProperty("持仓收益")
        private String holdProfit;
        @ApiModelProperty("持有收益")
        private String constantProfit;
    }

    @ApiModel("基金标签")
    @Data
    public static class Label {
        @ApiModelProperty("标签ID")
        private String labelId;
        @ApiModelProperty("标签代码")
        private String labelCode;
        @ApiModelProperty("标签名称")
        private String labelName;
        @ApiModelProperty("标签类型ID")
        private String typeId;
    }

    @ApiModel("饼图资产分布中的一项")
    @Data
    public static class PieAsset {
        @ApiModelProperty("占比")
        private String proportion;
        @ApiModelProperty("类型名称 (例如: 股票成分, 债券成分)")
        private String typeName;
    }

    @ApiModel("投资风格比例")
    @Data
    public static class InvestStyleRatio {
        @ApiModelProperty("规模类型 (例如: 大盘, 中盘, 小盘的代码)")
        private String scaleType;
        @ApiModelProperty("风格类型 (例如: 价值, 成长, 平衡的代码)")
        private String style;
        @ApiModelProperty("该风格的占比")
        private String ratio;
    }

    @ApiModel("行业评分及占比")
    @Data
    public static class IndustryScoreRatio {
        @ApiModelProperty("行业代码")
        private String industryCode;
        @ApiModelProperty("行业名称")
        private String industryName;
        @ApiModelProperty("机会得分")
        private String chanceScore;
        @ApiModelProperty("风险得分")
        private String riskScore;
        @ApiModelProperty("该行业的占比")
        private String ratio;
    }

    @ApiModel("债券类型及占比")
    @Data
    public static class BondRatio {
        @ApiModelProperty("债券类型代码")
        private String bondTypeCode;
        @ApiModelProperty("债券类型名称 (例如: 利率债, 信用债)")
        private String bondTypeName;
        @ApiModelProperty("该债券类型的占比")
        private String ratio;
    }

    @ApiModel("每日收益详情")
    @Data
    public static class DailyProfit {
        @ApiModelProperty("日期 (格式: YYYY-MM-DD)")
        private String date;
        @ApiModelProperty("收益金额")
        private String profit;
        @ApiModelProperty("收益率")
        private String profitRate;
        @ApiModelProperty("收益率【资金加权法】")
        private String profitRateWeight;
    }
}