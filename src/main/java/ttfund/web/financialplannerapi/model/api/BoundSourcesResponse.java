package ttfund.web.financialplannerapi.model.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("债券来源响应")
public class BoundSourcesResponse {

    @ApiModelProperty("债券类型代码")
    private String bondTypeCode;

    @ApiModelProperty("债券来源列表")
    private List<BoundSource> boundSources;

    @Data
    @ApiModel("债券来源详情")
    public static class BoundSource {

        @ApiModelProperty("基金代码")
        private String fundCode;

        @ApiModelProperty("基金名称")
        private String fundName;

        @ApiModelProperty("持仓占比")
        private Double percentage;

        @ApiModelProperty("券种占比")
        private Double bondPct;
    }
}
