package ttfund.web.financialplannerapi.model.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FinancialPlannerResponse {
    @ApiModelProperty("结果参数")
    private List<FinancialPlannerAssetDiagnosis> data;

    @Data
    public static class FinancialPlannerAssetDiagnosis {
        @ApiModelProperty("防守能力")
        private String defensiveAbility;
        @ApiModelProperty("防守能力超出同类比例")
        private String defensiveAbilityProportion;
        @ApiModelProperty("近一年超额收益率")
        private String excessIndexY;
        @ApiModelProperty("基金代码")
        private String fundCode;
        @ApiModelProperty("盈利能力")
        private String profitability;
        @ApiModelProperty("盈利能力超出同类比例")
        private String profitabilityProportion;
        @ApiModelProperty("近一年收益率")
        private String profitY;
        @ApiModelProperty("关联主题")
        private List<FundTopicCache> relateThemes;
        @ApiModelProperty("基金简称")
        private String shortName;
        @ApiModelProperty("近一年跟踪误差")
        private String trkError1;
    }

    @Data
    public static class FundTopicCache {
        @ApiModelProperty("近一年相关度")
        private String corr_1Y;
        @ApiModelProperty("基金代码")
        private String fcode;
        @ApiModelProperty("是否可购买")
        private String isbuy;
        @ApiModelProperty("是否代销")
        private String issales;
        @ApiModelProperty("重合股占基金重仓比例")
        private String ol2TOP;
        @ApiModelProperty("关联类型 1: 指数型 2:权益型")
        private String relatetype;
        @ApiModelProperty("成立以来收益率")
        private String se;
        @ApiModelProperty("研究板块代码")
        private String sec_CODE;
        @ApiModelProperty("研究板块名称")
        private String sec_NAME;
        @ApiModelProperty("基金简称")
        private String shortname;
        @ApiModelProperty("1年收益率")
        private String y;
        @ApiModelProperty("年总排名")
        private String ycount;
        @ApiModelProperty("年排名")
        private String yrank;
    }
}
