package ttfund.web.financialplannerapi.model.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("行业来源响应")
public class IndustrySourcesResponse {

    @ApiModelProperty("行业代码")
    private String industryCode;

    @ApiModelProperty("行业来源列表")
    private List<IndustrySource> industrySources;

    @Data
    @ApiModel("行业来源详情")
    public static class IndustrySource {

        @ApiModelProperty("基金代码")
        private String fundCode;

        @ApiModelProperty("基金名称")
        private String fundName;

        @ApiModelProperty("持仓占比")
        private Double percentage;

        @ApiModelProperty("行业占比")
        private Double industryPct;
    }
}
