package ttfund.web.financialplannerapi.model.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("投资风格响应")
public class InvestStyleResponse {

    @ApiModelProperty("投资风格数据列表")
    private List<InvestStyle> data;

    @Data
    @ApiModel("投资风格详情")
    public static class InvestStyle {

        @ApiModelProperty("基金代码")
        private String fundCode;

        @ApiModelProperty("基金名称")
        private String fundName;

        @ApiModelProperty("持仓占比")
        private Double percentage;

        @ApiModelProperty("风格占比")
        private Double stylePct;
    }
}
