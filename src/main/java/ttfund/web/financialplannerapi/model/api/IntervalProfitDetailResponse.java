package ttfund.web.financialplannerapi.model.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("区间收益详情响应")
@Data
public class IntervalProfitDetailResponse {

    @ApiModelProperty("图表点位列表")
    private List<GraphSpot> graphSpotList;

    @ApiModelProperty("最新区间信息列表")
    private List<Object> latestIntervalInfoList;

    @ApiModelProperty("指数收益代码")
    private String indexProfitCode;

    @ApiModelProperty("指数收益率")
    private Double indexProfitRate;

    @ApiModelProperty("账户收益率")
    private Double accountProfitRate;

    @ApiModel("图表点位")
    @Data
    public static class GraphSpot {

        @ApiModelProperty("净值日期")
        private String navDate;

        @ApiModelProperty("指数净值")
        private IndexNav indexNav;

        @ApiModelProperty("账户净值")
        private AccountNav accountNav;

        @ApiModelProperty("是否交易 0-否 1-是")
        private Integer isTrade;

        @ApiModelProperty("持仓总变化")
        private Double holdTotalChange;

        @ApiModelProperty("收益")
        private Double profit;

        @ApiModelProperty("总收益")
        private Double totalProfit;
    }

    @ApiModel("指数净值")
    @Data
    public static class IndexNav {

        @ApiModelProperty("净值日期")
        private String navDate;

        @ApiModelProperty("净值")
        private Double nav;

        @ApiModelProperty("收益率")
        private Double rate;

        @ApiModelProperty("总收益率")
        private Double totalRate;

        @ApiModelProperty("大额赎回标志")
        private String largeRedemptionFlag;

        @ApiModelProperty("大额赎回基金列表")
        private List<Object> largeRedemptionFundList;
    }

    @ApiModel("账户净值")
    @Data
    public static class AccountNav {

        @ApiModelProperty("净值日期")
        private String navDate;

        @ApiModelProperty("净值")
        private Double nav;

        @ApiModelProperty("收益率")
        private Double rate;

        @ApiModelProperty("总收益率")
        private Double totalRate;

        @ApiModelProperty("大额赎回标志")
        private String largeRedemptionFlag;

        @ApiModelProperty("大额赎回基金列表")
        private List<Object> largeRedemptionFundList;
    }
}
