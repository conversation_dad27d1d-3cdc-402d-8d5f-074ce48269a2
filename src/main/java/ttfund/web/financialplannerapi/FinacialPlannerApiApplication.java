package ttfund.web.financialplannerapi;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableAsync
@ServletComponentScan
//@SpringBootApplication(exclude = MongoAutoConfiguration.class)
@SpringBootApplication(exclude = {MongoAutoConfiguration.class, DruidDataSourceAutoConfigure.class})
@ComponentScan({"com.ttfund.web.core", "ttfund.web"})
@MapperScan({"ttfund.web.financialplannerapi.mapper"})
public class FinacialPlannerApiApplication {
    public static void main(String[] args) {
        SpringApplication.run(FinacialPlannerApiApplication.class, args);
    }
}
