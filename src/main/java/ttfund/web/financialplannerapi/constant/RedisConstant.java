package ttfund.web.financialplannerapi.constant;

public class RedisConstant {

    public final static String PREPLANNERKEY = "ttfund.web.financialplannerapi.pre.%s.%s";
    /**
     * 理财师当日已有预约情况 key构成，理财师passportId + 时间
     */
    public final static String APPOINT_COUNT = "ttfund.web.financialplannerapi.appointment.%s.%s";

    /**
     * 预约分布式锁, 构成 用户通行证id
     */
    public final static String APPOINT_USER_LOCK = "financialplannerapi.appointment.user.lock.%s";
    /**
     * 预约同一个理财师 同一时间段加锁，key构成：预约时间+理财师id
     */
    public final static String APPOINT_LOCK = "financialplannerapi.appointment.lock.%s.%s";

    public final static String ESTIMATE_INSERT_PREFIX = "estimate:insert:";

    public final static String CONTRACT_QUERY_PREFIX = "financialplannerapi:contract:query:";

    public final static String CONTRACT_QUERYALL_PREFIX = "financialplannerapi:contract:queryAll:";

    /**
     * 用户签约锁
     */
    public static final String CONTRACT_LOCK = "financialplannerapi:contract:lock:%s:%s";

    /**
     * 更换理财师用户锁
     */
    public final static String CHNAGE = "ttfund.web.financialplannerapi.change.%s";

    /**
     * 理财师悬浮窗缓存key 用户passportid, 页面标识pageFlag
     */
    public static final String LCS_WINDOW_KEY = "ttfund.lcs.window.%s.%s";

    public static final String LCS_INPUT_CONFIG_KEY = "ttfund.lcs.window.config";

    /**
     * 开通理财师服务的机构id
     */
    public static final String LCS_INSTITUTION_LIST_KEY = "ttfund.lcs.institutionId.list";

    /**
     * 理财师悬浮窗默认头像key
     */
    public static final String LCS_DEFAULT_IMG_KEY = "ttfund.lcs.window.default.img";

    /**
     * 预约提醒key
     */
    public static final String LCS_APPOINTMENT_MSG_KEY = "ttfund.lcs.appointment.msg.%s";
}
