package ttfund.web.financialplannerapi.constant;

import lombok.Getter;
import ttfund.web.financialplannerapi.exception.BusinessException;

/**
 * <AUTHOR>
 * @Date 2024/6/12 8:45
 */
@Getter
public enum TaskChangeTypeEnum {

    CHANGE_PLANNER("changePlanner"),
    BREAK_CONTRACT("breakContract"),
    RESUME_CONTRACT("resumeContract"),

    ;

    private final String type;

    TaskChangeTypeEnum(String type) {
        this.type = type;
    }


    public static TaskChangeTypeEnum getEnum(String type) throws BusinessException {
        for (TaskChangeTypeEnum c : TaskChangeTypeEnum.values()) {
            if (c.getType().equals(type)) {
                return c;
            }
        }
        throw new BusinessException("不支持的任务变更类型: " + type, 5278);
    }

}
