package ttfund.web.financialplannerapi.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum ErrorCodeEnum {

    // 错误码
    TermFormatERROR("期数格式不正确",52320),
    LackParameter("参数错误",52321),
    UnTalked("您今日还未咨询理财师，咨询后再来评价吧",52322),
    Evaluated("24h内已发起过评价",52323),
    CANNOT_EVALUATE("该服务不可进行评价",52325),
    LackPlanner("没有理财师可供分配",52324),
    LcsMatchingERROR("理财师与机构匹配错误",52325),
    Contracted("已签约，请直接与理财师沟通",0),
    CANNOTCONTRACTWITHSELFE("医者不能自医，换个理财师试试",5327),
    PlannerChanged("当前理财师信息已变更",5328),
    FinishCallFirst("当前有预约电话咨询，不能更换理财师，请取消后再试。",5329),
    FinishCallFirst1("当前有预约电话咨询，不能更换理财经理，请取消后再试。",53291),
    FINISH_CALL_BEFORE_CANCEL("当前有预约电话咨询，不能进行解约，请取消后再试。",5330),
    NOTContract("未签约请到首页签约",52326),
    RepeatAddAppointment("与当前机构下的理财师有未回访的预约",52421),
    AppointmentERROR("当前时间不可预约",52422),
    AppointmentNullERROR("预约记录不存在",52423),
    OperationError("操作错误", 52424),
    DateFormatERROR("日期格式错误",52420),
    AppointmentUnavailable("当前时间与已有预约冲突",52421),
    AppointmentFullERROR("当前时段预约人数已满",52431),
    AppointmentUnableOperate("当前无法修改预约",52426),
    SystemERROR("系统错误",500),
    ErrorCode501("通行证id不存在",501),
    UserBaseInfoQueryERROR("用户信息查询错误",52427),
    AppointmentRepeatERROR("预约失败，同一时段只能预约一次", 52422),
    LcsAppointmentERROR("预约失败，只能预约当前绑定的理财师",52430),
    NoCustomerNo("无交易账号",52428),
    NotEnoughTime("预约失败，当前预约时段剩余时间不足半小时", 52429),
    DecryptMobileQueryERROR("用户电话信息查询失败",52432),
    LOCKERROR("网络拥堵，请稍后重试",52433),
    NOT_FINANCIAL_PLANNER("聊天对象不是理财师",52434),
    MobileFormatError("手机号格式错误",52435),
    TradeDayApiError("交易日查询异常",52436),
    PlannerBindError("理财师绑定异常",52437),
    CannotContract("请在理财师发起签约邀请后进行签约", 52438),
    NOTFOUNDWINDOWCONFIG("该页面未配置悬浮窗",52439),
    NOTMATCHUSERGROUP("该用户不在配置用户组中",52440),
    LackFinancingManager("没有理财经理可供分配",52441),
    FinancingManagerChanged("当前理财经理信息已变更",52442),
    NoPermission("无权访问",52443),

    INNER_ACCESS_DENIED("没有内部访问权限，不允许访问",52444),
    USER_HOLD_ASSET_ERROR("获取用户持仓资产数据失败",52445),
    EXCEED_UPLOAD_FILE_LIMIT("文件大小超过上限",52446),
    START_DATE_OR_TIME_RANGE_NOT_FOUND("起始日期【startDate】或时间区间【timeRange】未提交",52447),
    PERSON_FINANCE_CONFIG_NOT_FOUND("未配置个性化建议",52448),
    USER_NOT_CONTRACT("未与该用户签约",52449),
    ;


    private String msg;
    private int code;

    ErrorCodeEnum(String msg, int code) {
        this.msg = msg;
        this.code = code;
    }

}
