package ttfund.web.financialplannerapi.constant;

/**
 * <AUTHOR>
 * @date 2022/11/15
 * @Description 移动回调接口响应
 */
public enum AXBResponseEnum {

    SUCCESS("0000","成功"),
    BIND_NOT_EXIST("1001","绑定关系不存在"),
    ORDER_EXPIRATION("1002","订单过期"),
    REQ_CONTENT_ERROR("1005","请求消息格式错误"),
    NOT_MATCH("1011","账户与中间号不匹配"),
    ERROR("1050","系统异常");

    private String code;
    private String message;

    public String getCode(){
        return code;
    }
    public String getMessage(){
        return message;
    }

    AXBResponseEnum(String code, String message){
        this.code = code;
        this.message = message;
    }
}
