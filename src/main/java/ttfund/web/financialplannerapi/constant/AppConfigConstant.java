package ttfund.web.financialplannerapi.constant;

/**
 * <AUTHOR>
 * @Date 2021/7/6 10:07
 */
public class AppConfigConstant {

    public final static String VERTICA_IM_CONNSR_READ = "vertica.im.connsr.read";
    public final static String MYSQL_LCS_CONNSR_WRITE = "mysql.financialplanner.connsr.write";
    public final static String MYSQL_LCS_CONNSR_READ = "mysql.financialplanner.connsr.read";

    public static final  String KAFKA_PRODUCER = "kafka.web.producer.config";

    public final static String USER_BASIC_INFO = "url.batch.user.basic.info";
    public final static String USER_INFO_NOTES = "user.info.notes";

    public final static String VERTICA_IM_TBNAME = "vertica.im.tbname";

    public static final String REDIS_READ = "redis.lcs.connsr.read";
    public static final String REDIS_WIRTE = "redis.lcs.connsr.write";

    public static final String MONGODB_TRADEDAY_DB_NAME = "mongodb.tradeday.db.name";
    public static final String MONGODB_TRADEDAY_CONNSR_READ = "mongodb.tradeday.connsr.read";

    /**
     * 当前环境 内测：test 线上：prop
     */
    public static final String ENV = "env";

    public static final String TEST_WHITELIST = "test.whitelist.passportId";

    public static final String API_USER_PASSPORT_URL = "api.user.passport.url";
    public static final String API_DECRYPT_URL = "api.decrypt.url";

    public static final String DECRYPT_FUND_TRADE_AUTHKEY = "decrypt.fundTrade.authKey";
    public static final String LCS_AUTH_KEY = "lcs.authKey";
    public static final String PHONE_PREFIX = "phone_prefix";

    /**
     * 社区换交易账户url
     */
    public static final String API_COMMUNITY_URL = "api.community.url";
    public static final String API_COMMUNITY_AUTHKEY = "api.community.authkey";
    /**
     * 中间号绑定地址
     */
    public static final String URL_PHONE_BIND = "url.phone.bind";

    /**
     * 中间号解绑地址
     */
    public static final String URL_PHONE_UNBIND = "url.phone.unbind";


    public static final String MOBILE_PLATFORM_ID = "mobile.platform.id";

    public static final String MOBILE_PLATFORM_SECRET = "mobile.platform.secret";

    public static final String BIND_TIME_LENGTH = "mobile.bind.length";

    // 行情api
    public static final String PRICE_API_AUTH_KEY = "price.api.authKey";

    public static final String PRICE_TRADE_DAY_LIST_API = "price.trade_day.list.api";

    /**
     * 协议配置
     */
    public static final String agreement = "agreement.config.new";

    /**
     * 查询用户组url
     */
    public static final String API_USER_GROUP_URL = "api.usergroup.url";

    /**
     * 天天基金机构Id
     */
    public static final String TTFUND_INSTITUTIONID = "100031";


    /**
     * 理财师悬浮窗头像url
     */
    public static final String LCS_DEFAULT_IMG = "lcs.window.img";

    public static final String IM_SEND_MSG_URL = "im.send.msg.url";
    public static final String IM_APP_KEY = "im.app.key";

    /**
     * 回归用测试机构Id
     */
    public static final String TEST_INSTITUTIONID = "test.self.institution";

    public static final String AGREEMENT_BASE = "base";
    public static final String INSTITUTION_PRIORITY = "institution.priority";

    public static int MAX_PLANNER = 5;

    /**
     * 跳转理财师小程序url
     */
    public static final String JUMP_LINK = "app.jump.link";

    /**
     * 自营理财师机构列表
     */
    public static final String SELF_MANAGE_INST_LIST = "self.manage.inst.list";

    /**
     * 数据库日志消息
     */
    public static final String LOG_TOPIC = "log.topic";

    /**
     * 数据库日志消息开关
     */
    public static final String LOG_KAFKA_SWITCH = "log.send.switch";
    public static final String VALUATION_PICTURE_BASEURL = "valuation.picture.base.url";

    public static final String VALUATION_PICTURE_BASEURL_PIC7 = "valuation.picture.base.pic7";
    public static final String VALUATION_PICTURE_BASEURL_PIC8 = "valuation.picture.base.pic8";
    public static final String VALUATION_PICTURE_BASEURL_PIC9 = "valuation.picture.base.pic9";
    public static final String VALUATION_PICTURE_BASEURL_PIC10 = "valuation.picture.base.pic10";

    public static final String API_INNER_ADMIN_URL = "api.inner.admin.url";
    public static final String INSTITUTION_CONFIG_AUTHKEY = "institution.config.authkey";


    /**
     *  理财师 admin端 url
     */
    public static final String ADMIN_URL = "admin.url";

    public static final String TASK_CHANGE_STATUS_URI = "/taskFlow/changeTaskStatus";
    public static final String HQ_API_URL = "hq.api.url";

    public static final String WINDOW_JUMP_LINK = "app.window.jump.link";
    public static final String GONGCE_URL = "gongce.url";
    public static final String MIDDLE_PLATFORM_URL = "middle.platform.url";
    public static final String INDUSTRY_ANALYSIS_URL = "industry.analysis.url";
    public static final String MIDDLE_PLATFORM_PORTFOLIO_URL = "middle.platform.portfolio.url";
    public static final String AI_GROUP_URL = "ai.group.url";
    public static final String GROUP_CAL_AUTH_KEY = "ai.group.key";
    public static final String FUND_COM_SERVICE_URL = "fund.com.service.url";
    public static final String INTERVAL_PROFIT_DETAIL_URL = "interval.profit.detail.url";
    /**
     * 用户核心业务
     */
    public final static String API_USER_CORE_URL = "api.user.core.url";
}
