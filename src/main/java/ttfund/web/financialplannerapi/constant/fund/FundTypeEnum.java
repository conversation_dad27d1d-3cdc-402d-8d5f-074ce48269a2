package ttfund.web.financialplannerapi.constant.fund;

import lombok.Getter;
import lombok.SneakyThrows;
import ttfund.web.financialplannerapi.exception.BusinessException;

/**
 * <AUTHOR>
 */
@Getter
public enum FundTypeEnum {

    // 产品类型
    TG("TG","投顾"),
    FUND("FUND",  "基金"),
    SI("SI",  "高端理财"),
    ;

    private final String type;
    private final String desc;

    FundTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    @SneakyThrows
    public static FundTypeEnum getType(String value) {
        for (FundTypeEnum c : FundTypeEnum.values()) {
            if (c.getType().equals(value)||c.getDesc().equals(value)) {
                return c;
            }
        }
        throw new BusinessException("产品类型不存在："+ value);
    }

    public static boolean contains(String value){
        for (FundFullTypeEnum c : FundFullTypeEnum.values()) {
            if (c.getCode().equals(value)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isValidName(String name) {
        for (FundTypeEnum fundTypeEnum : FundTypeEnum.values()) {
            if (fundTypeEnum.getType().equals(name)) {
                return true;
            }
        }
        return false;
    }
}
