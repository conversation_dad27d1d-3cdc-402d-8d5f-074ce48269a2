package ttfund.web.financialplannerapi.constant.fund;

import lombok.Getter;
import ttfund.web.financialplannerapi.util.other.QuickListUtils;
import ttfund.web.financialplannerapi.util.other.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public enum FundFullTypeEnum {

    // 基金完整分类
    INDEX_STOCK("000001","指数型-股票"),
    INDEX_FIXED_INCOME("000002","指数型-固收"),
    INDEX_OVERSEAS_STOCK("000003","指数型-海外股票"),
    INDEX_OTHER("000005","指数型-其他"),
    STOCK("001001",  "股票型"),
    HYBRID_EQUITY_BIASED("002001","混合型-偏股"),
    HYBRID_BALANCED("002002","混合型-平衡"),
    HYBRID_DEBT_BIASED("002003","混合型-偏债"),
    HYBRID_FLEXIBLE("002004","混合型-灵活"),
    HYBRID_ABSOLUTE_RETURN("002005","混合型-绝对收益"),
    BOND_LONG_TERM_DEBT("003001","债券型-长债"),
    BOND_SHORT_TO_MEDIUM_TERM_DEBT("003002","债券型-中短债"),
    BOND_HYBRID_LEVEL_ONE("003003","债券型-混合一级"),
    BOND_HYBRID_LEVEL_TWO("003004","债券型-混合二级"),
    FINANCE("003005","理财"),
    MONEY_MARKET_ORDINARY_MONEY_MARKET("005001","货币型-普通货币"),
    MONEY_MARKET_FLOATING_NET_VALUE("005002","货币型-浮动净值"),
    QDII_ORDINARY_STOCK("007001","QDII-普通股票"),
    QDII_HYBRID_EQUITY_BIASED("007002","QDII-混合偏股"),
    QDII_HYBRID_BALANCED("007003","QDII-混合平衡"),
    QDII_HYBRID_FLEXIBLE("007004","QDII-混合灵活"),
    QDII_PURE_DEBT("007005","QDII-纯债"),
    QDII_HYBRID_DEBT("007006","QDII-混合债"),
    QDII_REITS("007007","QDII-REITs"),
    QDII_COMMODITY("007008","QDII-商品"),
    QDII_FOF("007009","QDII-FOF"),
    FOF_AGGRESSIVE("009001","FOF-进取型"),
    FOF_BALANCED_TYPE("009002","FOF-均衡型"),
    FOF_CONSERVATIVE("009003","FOF-稳健型"),
    COMMODITY("010001","商品"),
    REITS("011001","REITs"),
    OTHER_OTHER("205001",""),
    OTHER_INDEX("000005","指数型其他"),
    OTHER_DEFAULT("-1","其他"),
    ;

    private final String code;
    private final String desc;

    FundFullTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FundFullTypeEnum get(String value) {
        if(StringUtils.isEmpty(value)){return OTHER_DEFAULT;}
        for (FundFullTypeEnum c : FundFullTypeEnum.values()) {
            if (c.getCode().equals(value)||c.getDesc().equals(value)) {
                return c;
            }
        }
        return OTHER_DEFAULT;
    }

    public static boolean contains(String value){
        for (FundFullTypeEnum c : FundFullTypeEnum.values()) {
            if (c.getCode().equals(value)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否是股票类
     * @param code 基金代码
     * @return 判断结果
     */
    public static boolean isStock(String code) {
        return getStockList().contains(code);
    }

    /**
     * 判断是否是债券类
     * @param code 基金代码
     * @return 判断结果
     */
    public static boolean isBond(String code) {
        return getBondList().contains(code);
    }

    /**
     * 判断是否是货币类
     * @param code 基金代码
     * @return 判断结果
     */
    public static boolean isCurrency(String code) {
        return getCurrencyList().contains(code);
    }

    /**
     * 判断是否是商品及其他类型
     * @param code 基金代码
     * @return 判断结果
     */
    public static boolean isCommodity(String code) {
        return getCommodityList().contains(code);
    }

    /**
     * 获取股票类基金类型集合
     * @return 股票类基金类型集合
     */
    public static List<String> getStockList() {
        return QuickListUtils.of(INDEX_STOCK.code, INDEX_OVERSEAS_STOCK.code, STOCK.code, HYBRID_EQUITY_BIASED.code,
                HYBRID_BALANCED.code, HYBRID_FLEXIBLE.code, HYBRID_ABSOLUTE_RETURN.code, QDII_ORDINARY_STOCK.code,
                QDII_HYBRID_EQUITY_BIASED.code, QDII_HYBRID_BALANCED.code, QDII_HYBRID_FLEXIBLE.code, QDII_FOF.code,
                FOF_AGGRESSIVE.code, FOF_BALANCED_TYPE.code, FOF_CONSERVATIVE.code);
    }

    /**
     * 获取债券类基金类型集合
     * @return 债券类基金类型集合
     */
    public static List<String> getBondList() {
        return QuickListUtils.of(INDEX_FIXED_INCOME.code, QDII_PURE_DEBT.code, BOND_LONG_TERM_DEBT.code,
                BOND_SHORT_TO_MEDIUM_TERM_DEBT.code, HYBRID_DEBT_BIASED.code, BOND_HYBRID_LEVEL_ONE.code,
                BOND_HYBRID_LEVEL_TWO.code, FINANCE.code, QDII_HYBRID_DEBT.code);
    }

    /**
     * 获取货币类基金类型集合
     * @return 货币类基金类型集合
     */
    public static List<String> getCurrencyList() {
        return QuickListUtils.of(MONEY_MARKET_ORDINARY_MONEY_MARKET.code, MONEY_MARKET_FLOATING_NET_VALUE.code);
    }

    /**
     * 获取商品及其他类型基金类型集合
     * @return 商品及其他类型基金类型集合
     */
    public static List<String> getCommodityList() {
        return QuickListUtils.of(QDII_COMMODITY.code, COMMODITY.code, QDII_REITS.code, REITS.code,
                OTHER_OTHER.code, OTHER_INDEX.code);
    }

    /**
     * 获取股票类-指数型类型基金类型集合
     * @return 股票类-指数型类型基金类型集合
     */
    public static List<String> getIndexList() {
        return QuickListUtils.of(FundFullTypeEnum.INDEX_STOCK.getCode(), FundFullTypeEnum.INDEX_OVERSEAS_STOCK.getCode());
    }

    /**
     * 获取股票类-股票型类型基金类型集合
     * @return 股票类-股票型类型基金类型集合
     */
    public static List<String> getEquityList() {
        return QuickListUtils.of(STOCK.code);
    }

    /**
     * 获取股票类-混合型类型基金类型集合
     * @return 股票类-混合型类型基金类型集合
     */
    public static List<String> getBalancedList() {
        return QuickListUtils.of( HYBRID_EQUITY_BIASED.code, HYBRID_BALANCED.code, HYBRID_FLEXIBLE.code, HYBRID_ABSOLUTE_RETURN.code);
    }

    /**
     * 获取股票类-QDII型类型基金类型集合
     * @return 股票类-QDII型类型基金类型集合
     */
    public static List<String> getQdiiList() {
        return QuickListUtils.of(QDII_ORDINARY_STOCK.code, QDII_HYBRID_EQUITY_BIASED.code, QDII_HYBRID_BALANCED.code, QDII_HYBRID_FLEXIBLE.code);
    }

    /**
     * 获取股票类-FOF型类型基金类型集合
     * @return 股票类-FOF型类型基金类型集合
     */
    public static List<String> getFofList() {
        return QuickListUtils.of(FOF_AGGRESSIVE.code, FOF_BALANCED_TYPE.code, FOF_CONSERVATIVE.code);
    }

    /**
     * 获取债券类-固收指数型类型基金类型集合
     * @return 债券类-固收指数类型基金类型集合
     */
    public static List<String> getFixedIncomeIndexList() {
        return QuickListUtils.of(INDEX_FIXED_INCOME.code);
    }

    /**
     * 获取债券类-纯债型类型基金类型集合
     * @return 债券类-纯债类型基金类型集合
     */
    public static List<String> getPureBondList() {
        return QuickListUtils.of(QDII_PURE_DEBT.code, BOND_LONG_TERM_DEBT.code, BOND_SHORT_TO_MEDIUM_TERM_DEBT.code);
    }

    /**
     * 获取债券类-混合债类型基金类型集合
     * @return 债券类-混合债类型基金类型集合
     */
    public static List<String> getHybridBondList() {
        return QuickListUtils.of(HYBRID_DEBT_BIASED.code, BOND_HYBRID_LEVEL_ONE.code, BOND_HYBRID_LEVEL_TWO.code, FINANCE.code, QDII_HYBRID_DEBT.code);
    }

    /**
     * 获取商品及其他类-商品类型基金类型集合
     * @return 商品及其他类-商品类型基金类型集合
     */
    public static List<String> getCommodityOnlyList() {
        return QuickListUtils.of(QDII_COMMODITY.code, COMMODITY.code);
    }

    /**
     * 获取商品及其他类-REITs类型基金类型集合
     * @return 商品及其他类-REITs类型基金类型集合
     */
    public static List<String> getReitsList() {
        return QuickListUtils.of(QDII_REITS.code, REITS.code);
    }

    /**
     * 获取商品及其他类-其他基金类型集合
     * @return 商品及其他类-其他类型基金类型集合
     */
    public static List<String> getOtherList() {
        return QuickListUtils.of(OTHER_OTHER.code, OTHER_INDEX.code);
    }
}
