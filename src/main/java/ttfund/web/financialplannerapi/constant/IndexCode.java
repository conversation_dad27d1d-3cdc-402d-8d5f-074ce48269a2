package ttfund.web.financialplannerapi.constant;

import lombok.Getter;

@Getter
public enum IndexCode {
    SHANGHAI_COMPOSITE("000001", "上证指数"),
    CSI_300("000300", "沪深300"),
    GEM_INDEX("399006", "创业板指"),
    SZSE_COMPONENT_INDEX("399001", "深证成指"),
    SME_100("399005", "中小100"),
    CSI_500("399905", "中证500"),
    CSI_ALL_BONDS("H11001", "中证全债"),
    EQUITY_FUND("930950", "偏股基金");

    private final String code;
    private final String name;

    IndexCode(String code, String name) {
        this.code = code;
        this.name = name;
    }

}