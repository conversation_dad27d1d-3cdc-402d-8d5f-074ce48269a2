package ttfund.web.financialplannerapi.constant;

/**
 * @Description : 数据访问权限
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @Date: 2022/10/13  14:14
 */
public class DataAuthorityConstant {

    /**
     * 无数据访问权限
     */
    public static final int NO_ACCESS = 0;

    /**
     * 仅可见自己的数据
     */
    public static final int SELF = 1;

    /**
     * 可见所属机构数据
     */
    public static final int INSTITUTION = 2;

    /**
     * 可见所有数据
     */
    public static final int ALL = 3;

    public static boolean isAll(Integer dataType){
        return ALL == dataType;
    }

    public static boolean isNotAll(Integer dataType){
        return !isAll(dataType);
    }

    public static boolean isInstitution(Integer dataType){
        return INSTITUTION == dataType;
    }
}
