package ttfund.web.financialplannerapi.constant.user;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.SneakyThrows;
import ttfund.web.financialplannerapi.exception.BusinessException;

/**
 * <AUTHOR>
 */
@Getter
public enum UserCustRiskLevelEnum {

    // 配置建议编码
    C0("0","未参与调查"),
    C1("4",  "极端保守型"),
    C2("1",  "相对保守型"),
    C3("2",  "稳健平衡型"),
    C4("3",  "相对积极型"),
    C5("5",  "积极进取型"),
    ;

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;

    UserCustRiskLevelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @SneakyThrows
    public static UserCustRiskLevelEnum get(String value) {
        for (UserCustRiskLevelEnum c : UserCustRiskLevelEnum.values()) {
            if (c.getCode().equals(value)||c.getDesc().equals(value)) {
                return c;
            }
        }
        throw new BusinessException("用户风险评测不存在："+ value);
    }

    /**
     * 是否是极端保守型
     * @param type 参数
     * @return 判断结果
     */
    public static boolean isExtremelyConservative(UserCustRiskLevelEnum type) {
        return C1.equals(type);
    }
}
