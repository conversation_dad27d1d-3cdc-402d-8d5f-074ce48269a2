package ttfund.web.financialplannerapi.constant;

/**
 * @Description : 签约状态字段枚举类
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @Date: 2023/3/17  9:39
 */
public enum ContractStatusEnum {
    NewContractOrChangePlanner(0, "全新签约/更换理财师（默认值）"),
    CancelContract(1, "解约"),
    ReContract(2, "解约后重新签约"),
    Binding(3, "已绑定未授权"),
    ContractWaiting(4, "已绑定待签约")
    ;


    private int status;
    private String info;

    public int getStatus() {
        return status;
    }

    public String getInfo() {
        return info;
    }

    ContractStatusEnum(int status, String info) {
        this.status = status;
        this.info = info;
    }
}
