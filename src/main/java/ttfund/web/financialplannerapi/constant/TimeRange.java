package ttfund.web.financialplannerapi.constant;

import lombok.Getter;

@Getter
public enum TimeRange {
    WEEK("w", "周"),
    MONTH("y", "月"),
    THREE_MONTHS("3y", "三月"),
    SIX_MONTHS("6y", "六月"),
    YEAR("n", "年"),
    TWO_YEARS("2n", "2年"),
    THREE_YEARS("3n", "3年"),
    FIVE_YEARS("5n", "5年"),
    THIS_YEAR("jn", "今年"),
    PAST_YEARS("ln", "历年"),
    REAL_TIME("AT", "实时");

    private final String code;
    private final String description;

    TimeRange(String code, String description) {
        this.code = code;
        this.description = description;
    }

}