package ttfund.web.financialplannerapi.constant.sys;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.SneakyThrows;
import ttfund.web.financialplannerapi.exception.BusinessException;

/**
 * <AUTHOR>
 */
@Getter
public enum TimeRangeEnum {

    // 日期时间周期枚举
    NEAR_1_WEEK("NEAR_1_WEEK","近1周"),
    NEAR_1_MONTH("NEAR_1_MONTH",  "近1月"),
    NEAR_3_MONTH("NEAR_3_MONTH",  "近3月"),
    NEAR_6_MONTH("NEAR_6_MONTH",  "近6月"),
    NEAR_1_YEAR("NEAR_1_YEAR",  "近1年"),
    NEAR_3_YEAR("NEAR_3_YEAR",  "近3年"),
    NEAR_5_YEAR("NEAR_5_YEAR",  "近5年"),
    ;

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;

    TimeRangeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @SneakyThrows
    public static TimeRangeEnum getType(String value) {
        for (TimeRangeEnum c : TimeRangeEnum.values()) {
            if (c.getCode().equals(value)||c.getDesc().equals(value)) {
                return c;
            }
        }
        throw new BusinessException("日期时间周期枚举不存在："+ value);
    }
}
