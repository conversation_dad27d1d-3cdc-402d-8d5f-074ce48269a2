package ttfund.web.financialplannerapi.constant;

/**
 * <AUTHOR>
 * @date 2023/1/28
 * @Description 预约状态常量
 */
public class AppointmentStateConst {
    /**
     * 预约可用
     */
    public static final int APPOINTMENT_AVAILABLE = 0;
    /**
     * 用户存在预约冲突
     */
    public static final int USER_APPOINTMENT_REPEAT = 1;
    /**
     * 理财师预约已满
     */
    public static final int F_USER_APPOINTMENT_FULL = 2;
    /**
     * 当前时间不可预约
     */
    public static final int APPOINTMENT_DATE_ERROR = 3;
    /**
     * 预约结束不足30分钟
     */
    public static final int APPOINTMENT_NOT_ENOUGH = 4;

    /**
     * 预约信息过期
     */
    public static final int IS_EXPIRED = 1;
    /**
     * 预约信息可用 属于未回访状态
     */
    public static final int IS_AVAILABLE = 0;
}
