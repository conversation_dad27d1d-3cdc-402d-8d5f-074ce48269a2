package ttfund.web.financialplannerapi.constant.financeconfig;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.SneakyThrows;
import ttfund.web.financialplannerapi.exception.BusinessException;

/**
 * <AUTHOR>
 */
@Getter
public enum FinanceConfigCodeEnum {

    // 配置建议编码
    JCJG_001("JCJG001","低风险"),
    JCJG_002("JCJG002",  "中低风险"),
    JCJG_003("JCJG003",  "中风险"),
    JCJG_004("JCJG004",  "中高风险"),
    JCJG_005("JCJG005",  "中高风险"),
    JCJG_006("JCJG006",  "高风险"),
    ;

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;

    FinanceConfigCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @SneakyThrows
    public static FinanceConfigCodeEnum getType(String value) {
        for (FinanceConfigCodeEnum c : FinanceConfigCodeEnum.values()) {
            if (c.getCode().equals(value)||c.getDesc().equals(value)) {
                return c;
            }
        }
        throw new BusinessException("配置建议编码不存在："+ value);
    }
}
