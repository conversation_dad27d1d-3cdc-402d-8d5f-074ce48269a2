package ttfund.web.financialplannerapi.constant.financeconfig;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.SneakyThrows;
import ttfund.web.financialplannerapi.constant.fund.FundFullTypeEnum;
import ttfund.web.financialplannerapi.exception.BusinessException;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum FinanceConfigFundSubTypeEnum {

    // 配置基金二类
    INDEX("INDEX", "指数型"),
    EQUITY("EQUITY", "股票型"),
    BALANCED("BALANCED", "混合型"),
    QDII("QDII", "QDII"),
    FOF("FOF", "FOF"),
    FIXED_INCOME_INDEX("FIXED_INCOME_INDEX", "固收指数"),
    PURE_BOND("PURE_BOND", "纯债"),
    HYBRID_BOND("HYBRID_BOND", "混合债"),
    COMMODITY("COMMODITY", "商品"),
    REITS("REITS", "REITs"),
    OTHER("OTHER", "其他"),
    MONEY_MARKET("MONEY_MARKET", "货币类"),
    ;

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;

    FinanceConfigFundSubTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @SneakyThrows
    public static FinanceConfigFundSubTypeEnum getType(String value) {
        for (FinanceConfigFundSubTypeEnum c : FinanceConfigFundSubTypeEnum.values()) {
            if (c.getCode().equals(value)||c.getDesc().equals(value)) {
                return c;
            }
        }
        throw new BusinessException("配置基金二类不存在："+ value);
    }

    /**
     * 根据基金全类获取配置基金二类枚举
     * @param fundFullTypeEnum 基金全类枚举
     * @return 配置基金二类枚举
     */
    public static FinanceConfigFundSubTypeEnum getByFundFullTypeEnum(FundFullTypeEnum fundFullTypeEnum) {
        if(Objects.isNull(fundFullTypeEnum)) {
            return null;
        }

        if(FundFullTypeEnum.getIndexList().contains(fundFullTypeEnum.getCode())) {
            return FinanceConfigFundSubTypeEnum.INDEX;
        }
        if(FundFullTypeEnum.getEquityList().contains(fundFullTypeEnum.getCode())) {
            return FinanceConfigFundSubTypeEnum.EQUITY;
        }
        if(FundFullTypeEnum.getBalancedList().contains(fundFullTypeEnum.getCode())) {
            return FinanceConfigFundSubTypeEnum.BALANCED;
        }
        if(FundFullTypeEnum.getQdiiList().contains(fundFullTypeEnum.getCode())) {
            return FinanceConfigFundSubTypeEnum.QDII;
        }
        if(FundFullTypeEnum.getFofList().contains(fundFullTypeEnum.getCode())) {
            return FinanceConfigFundSubTypeEnum.FOF;
        }
        if(FundFullTypeEnum.getFixedIncomeIndexList().contains(fundFullTypeEnum.getCode())) {
            return FinanceConfigFundSubTypeEnum.FIXED_INCOME_INDEX;
        }
        if(FundFullTypeEnum.getPureBondList().contains(fundFullTypeEnum.getCode())) {
            return FinanceConfigFundSubTypeEnum.PURE_BOND;
        }
        if(FundFullTypeEnum.getHybridBondList().contains(fundFullTypeEnum.getCode())) {
            return FinanceConfigFundSubTypeEnum.HYBRID_BOND;
        }
        if(FundFullTypeEnum.getCommodityList().contains(fundFullTypeEnum.getCode())) {
            return FinanceConfigFundSubTypeEnum.COMMODITY;
        }
        if(FundFullTypeEnum.getReitsList().contains(fundFullTypeEnum.getCode())) {
            return FinanceConfigFundSubTypeEnum.REITS;
        }
        if(FundFullTypeEnum.getOtherList().contains(fundFullTypeEnum.getCode())) {
            return FinanceConfigFundSubTypeEnum.OTHER;
        }
        if(FundFullTypeEnum.getCurrencyList().contains(fundFullTypeEnum.getCode())) {
            return FinanceConfigFundSubTypeEnum.MONEY_MARKET;
        }
        return null;
    }

    /**
     * 根据配置基金二类枚举获取基金全类对应集合
     * @param type 配置基金二类枚举
     * @return 基金全类对应集合
     */
    public static List<String> getFullTypeByFinanceConfigFundSubTypeEnum(FinanceConfigFundSubTypeEnum type) {
        List<String> fullFundTypes = new ArrayList<>();
        if(Objects.isNull(type)) {
            return fullFundTypes;
        }

        switch (type) {
            case INDEX:
                fullFundTypes = FundFullTypeEnum.getIndexList();
                break;
            case EQUITY:
                fullFundTypes = FundFullTypeEnum.getEquityList();
                break;
            case BALANCED:
                fullFundTypes = FundFullTypeEnum.getBalancedList();
                break;
            case QDII:
                fullFundTypes = FundFullTypeEnum.getQdiiList();
                break;
            case FOF:
                fullFundTypes = FundFullTypeEnum.getFofList();
                break;
            case FIXED_INCOME_INDEX:
                fullFundTypes = FundFullTypeEnum.getFixedIncomeIndexList();
                break;
            case PURE_BOND:
                fullFundTypes = FundFullTypeEnum.getPureBondList();
                break;
            case HYBRID_BOND:
                fullFundTypes = FundFullTypeEnum.getHybridBondList();
                break;
            case COMMODITY:
                fullFundTypes = FundFullTypeEnum.getCommodityList();
                break;
            case REITS:
                fullFundTypes = FundFullTypeEnum.getReitsList();
                break;
            case OTHER:
                fullFundTypes = FundFullTypeEnum.getOtherList();
                break;
            case MONEY_MARKET:
                fullFundTypes = FundFullTypeEnum.getCurrencyList();
                break;
            default:
                break;
        }

        return fullFundTypes;
    }
}
