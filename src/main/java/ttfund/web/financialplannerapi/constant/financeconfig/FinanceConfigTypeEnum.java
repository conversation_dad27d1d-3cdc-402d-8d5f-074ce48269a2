package ttfund.web.financialplannerapi.constant.financeconfig;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.SneakyThrows;
import ttfund.web.financialplannerapi.exception.BusinessException;

/**
 * <AUTHOR>
 */
@Getter
public enum FinanceConfigTypeEnum {

    // 配置建议方案类型
    PERSONALIZED("personalized","个性化方案"),
    COMMON("common",  "公共方案"),
    ;

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;

    FinanceConfigTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @SneakyThrows
    public static FinanceConfigTypeEnum getType(String value) {
        for (FinanceConfigTypeEnum c : FinanceConfigTypeEnum.values()) {
            if (c.getCode().equals(value)||c.getDesc().equals(value)) {
                return c;
            }
        }
        throw new BusinessException("配置建议方案类型不存在："+ value);
    }

    public static boolean isCommon(FinanceConfigTypeEnum type) {
        return COMMON.equals(type);
    }

    public static boolean isPersonalized(FinanceConfigTypeEnum type) {
        return PERSONALIZED.equals(type);
    }
}
