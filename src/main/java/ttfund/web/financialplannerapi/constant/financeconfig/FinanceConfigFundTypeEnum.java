package ttfund.web.financialplannerapi.constant.financeconfig;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.SneakyThrows;
import ttfund.web.financialplannerapi.exception.BusinessException;

/**
 * <AUTHOR>
 */
@Getter
public enum FinanceConfigFundTypeEnum {

    // 观点四大类型
    CURRENCY("currency","货币类"),
    BOND("bond",  "债券类"),
    STOCK("stock",  "股票类"),
    COMMODITY_OTHER("commodity_other",  "商品及其他"),
    ;

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;

    FinanceConfigFundTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @SneakyThrows
    public static FinanceConfigFundTypeEnum getType(String value) {
        for (FinanceConfigFundTypeEnum c : FinanceConfigFundTypeEnum.values()) {
            if (c.getCode().equals(value)||c.getDesc().equals(value)) {
                return c;
            }
        }
        throw new BusinessException("观点类型不存在："+ value);
    }
}
