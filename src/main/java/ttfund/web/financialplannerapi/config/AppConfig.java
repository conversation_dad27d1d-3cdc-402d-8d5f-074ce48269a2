package ttfund.web.financialplannerapi.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import ttfund.web.financialplannerapi.constant.AppConfigConstant;
import ttfund.web.financialplannerapi.model.AgreementConfig;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/7/13 9:12
 */
@Component
public class AppConfig {

//    @Value("${" + AppConfigConstant.VERTICA_IM_CONNSR_READ + "}")
//    public String vertica_im_connsr_read;
    @Value("${" + AppConfigConstant.MYSQL_LCS_CONNSR_WRITE + "}")
    public String mysql_lcs_connsr_write;
    @Value("${" + AppConfigConstant.MYSQL_LCS_CONNSR_READ + "}")
    public String mysql_lcs_connsr_read;
    @Value("${" + AppConfigConstant.USER_BASIC_INFO + "}")
    public String user_basic_info;
    // /**
    //  * vertica表名
    //  */
    // @Value("${" + AppConfigConstant.VERTICA_IM_TBNAME + "}")
    // public String vertica_im_tbname;

    @Value("${" + AppConfigConstant.USER_INFO_NOTES + ":null}")
    public String userInfoNotes;

    @Value("${" + AppConfigConstant.REDIS_READ + "}")
    public String redis_read;
    @Value("${" + AppConfigConstant.REDIS_WIRTE + "}")
    public String redis_wirte;

    @Value("${" + AppConfigConstant.KAFKA_PRODUCER + "}")
    public String kafka_producer;

    @Value("${" + AppConfigConstant.MONGODB_TRADEDAY_CONNSR_READ + "}")
    public String mongodb_trade_day_connsr_read;

    @Value("#{${" + AppConfigConstant.TEST_WHITELIST + ":null}}")
    public Map<String, String> whitelist_passportId;

    @ApolloJsonValue("${" + AppConfigConstant.agreement + ":{}}")
    public Map<String, AgreementConfig> agreement;

    @Value("${" + AppConfigConstant.URL_PHONE_BIND + ":null}")
    public String URL_PHONE_BIND;

    @Value("${" + AppConfigConstant.URL_PHONE_UNBIND + ":null}")
    public String URL_PHONE_UNBIND;

    @Value("${" + AppConfigConstant.MOBILE_PLATFORM_ID + ":null}")
    public String MOBILE_PLATFORM_ID;

    @Value("${" + AppConfigConstant.MOBILE_PLATFORM_SECRET + ":null}")
    public String MOBILE_PLATFORM_SECRET;

    @Value("${" + AppConfigConstant.BIND_TIME_LENGTH + ":null}")
    public Integer BIND_TIME_LENGTH;

    @Value("${" + AppConfigConstant.PRICE_API_AUTH_KEY + ":null}")
    public String PRICE_API_AUTH_KEY;

    @Value("${" + AppConfigConstant.PRICE_TRADE_DAY_LIST_API + ":null}")
    public String PRICE_TRADE_DAY_LIST_API;

    @Value("${" +AppConfigConstant.API_USER_GROUP_URL + ":null}")
    public String USER_GROUP_API;

    @Value("${" + AppConfigConstant.IM_SEND_MSG_URL + ":null}")
    public String IM_SEND_MSG_URL;

    @Value("${" + AppConfigConstant.IM_APP_KEY + ":null}")
    public String IM_APP_KEY;

    @Value("${" + AppConfigConstant.LCS_DEFAULT_IMG + ":null}")
    public String LCS_DEFAULT_IMG;

    @Value("${" + AppConfigConstant.TEST_INSTITUTIONID + ":80201857}")
    public String TEST_INSTITUTIONID;

    @Value("#{${" + AppConfigConstant.INSTITUTION_PRIORITY + ":null}}")
    public Map<String, Integer> INSTITUTION_PRIORITY;

    @Value("${" + AppConfigConstant.JUMP_LINK + ":fund://mp.1234567.com.cn/weex/fund0144d8f6c9cdf3/pages/chat-area-single/index?type=1&from=tg&institutionId=%s&product=}")
    public String JUMP_LINK;

    @Value("${" + AppConfigConstant.SELF_MANAGE_INST_LIST + ":null}")
    public List<String> self_manage_inst_list;

    @Value("${" + AppConfigConstant.ADMIN_URL + ":null}")
    public String ADMIN_URL;

    @Value("${" + AppConfigConstant.HQ_API_URL + ":http://web.zp.ttjj/fundcomapi}")
    public String HQ_API_URL;

    @Value("${" + AppConfigConstant.WINDOW_JUMP_LINK + ":fund://mp.1234567.com.cn/weex/fund0144d8f6c9cdf3/pages/chat-area-single/index?type=1&institutionId=%s&id=%s&autoReply=%s&greet=}")
    public String WINDOW_JUMP_LINK;

    @Value("${" + AppConfigConstant.ENV + ":test}")
    public String ENV;

    @Value("${" + AppConfigConstant.GONGCE_URL + ":https://dataapineice.1234567.com.cn/gongce}")
    public String GONGCE_URL;

    @Value("${" + AppConfigConstant.MIDDLE_PLATFORM_URL + ":http://10.228.141.64:4396}")
    public String MIDDLE_PLATFORM_URL;

    @Value("${" + AppConfigConstant.INDUSTRY_ANALYSIS_URL + ":http://8test.k8s.yunwei}")
    public String INDUSTRY_ANALYSIS_URL;

    @Value("${" + AppConfigConstant.MIDDLE_PLATFORM_PORTFOLIO_URL + ":}")
    public String MIDDLE_PLATFORM_PORTFOLIO_URL;

    @Value("${" + AppConfigConstant.AI_GROUP_URL + ":}")
    public String AI_GROUP_URL;

    @Value("${" + AppConfigConstant.GROUP_CAL_AUTH_KEY + ":}")
    public String GROUP_CAL_AUTH_KEY;

    @Value("${" + AppConfigConstant.FUND_COM_SERVICE_URL + ":null}")
    public String FUND_COM_SERVICE_URL;

    @Value("${" + AppConfigConstant.INTERVAL_PROFIT_DETAIL_URL + ":http://10.189.18.208/portfolio_uat}")
    public String INTERVAL_PROFIT_DETAIL_URL;

    @Value("${" + AppConfigConstant.API_USER_CORE_URL + "}")
    public String USER_CORE_BASE_URL;
}
