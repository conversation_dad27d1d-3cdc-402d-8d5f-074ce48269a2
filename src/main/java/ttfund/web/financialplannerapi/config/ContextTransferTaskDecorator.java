package ttfund.web.financialplannerapi.config;

import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;

import java.util.Map;

/**
 * @Author: LongX
 * @Date: 2024/4/11 11:21
 * @Description: ContextTransferTaskDecorator
 * @Version: 1.0
 **/
public class ContextTransferTaskDecorator implements TaskDecorator {
    @Override
    public Runnable decorate(Runnable runnable) {
        Map<String, String> context = MDC.getCopyOfContextMap();

//        //子线程调用RequestContextHolder.currentRequestAttributes()会throw new IllegalStateException
//        //需要在执行子线程之前,在主线程中设置属性子线程共享
//        //ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//        //RequestContextHolder.setRequestAttributes(servletRequestAttributes,true);
//        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
        return () -> {
            try {
                if(context != null){
                    MDC.setContextMap(context);
                }
//                RequestContextHolder.setRequestAttributes(requestAttributes);
                runnable.run();
            } finally {
                //防止线程内部MDC的内存泄漏,此处清除的是线程内部的MDC不影响主线程MDC信息
                MDC.clear();
//                RequestContextHolder.resetRequestAttributes();
            }
        };
    }
}