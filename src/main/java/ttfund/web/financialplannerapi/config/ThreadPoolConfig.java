package ttfund.web.financialplannerapi.config;

import cn.hutool.core.thread.NamedThreadFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;


/**
 * <AUTHOR>
 * @description 线程池配置
 */
@Configuration
public class ThreadPoolConfig {

    @Bean("planner_statistics_tp")
    public ExecutorService orderPool() {
        return new ThreadPoolExecutor(30, 60, 0L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(10000), new NamedThreadFactory("staPool-", false), new ThreadPoolExecutor.DiscardPolicy());
    }

    @Bean("defaultExecPool")
    public ExecutorService defaultPool() {
        return new ThreadPoolExecutor(5, 10, 0L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(10000), new NamedThreadFactory("defaultPool-", false), new ThreadPoolExecutor.DiscardPolicy());
    }

    @Bean("fundAiExecPool")
    public ExecutorService fundAiPool() {
        return new ThreadPoolExecutor(5, 20, 0L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(10000), new NamedThreadFactory("fundAiPool-", false), new ThreadPoolExecutor.DiscardPolicy());
    }

    @Bean(name="financeDiagExecPool")
    public Executor financeDiagExecPoolConfig() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(16);
        //核心线程数
        executor.setCorePoolSize(8);
        //任务队列的大小
        executor.setQueueCapacity(5000);
        //线程前缀名
        executor.setThreadNamePrefix("financeDiagExecThread-api-");
        //线程存活时间
        executor.setKeepAliveSeconds(30);
        // 等待时间 （默认为0，此时立即停止），并没等待xx秒后强制停止
        executor.setAwaitTerminationSeconds(60);
        //设置线程池增强 - 传递traceid
        executor.setTaskDecorator(new ContextTransferTaskDecorator());
        //等待任务在关机时完成--表明等待所有线程执行完
        executor.setWaitForTasksToCompleteOnShutdown(true);

        /**
         * 拒绝处理策略
         * CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
         * AbortPolicy()：直接抛出异常。
         * DiscardPolicy()：直接丢弃。
         * DiscardOldestPolicy()：丢弃队列中最老的任务。
         */

        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();

        return executor;
    }

}
