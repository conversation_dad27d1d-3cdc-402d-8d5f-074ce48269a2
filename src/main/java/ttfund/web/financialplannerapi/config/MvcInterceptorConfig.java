package ttfund.web.financialplannerapi.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import ttfund.web.financialplannerapi.interceptor.HeaderInterceptor;
import ttfund.web.financialplannerapi.interceptor.TraceInterceptor;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
public class MvcInterceptorConfig implements WebMvcConfigurer {

    private final TraceInterceptor traceInterceptor;
    private final HeaderInterceptor headerInterceptor;

    /** 不需要拦截地址 */
    public static final String[] excludeUrls = { "/uploadFTP" };

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(traceInterceptor).addPathPatterns("/**");
        registry.addInterceptor(headerInterceptor).addPathPatterns("/**").excludePathPatterns(excludeUrls);
    }
}
