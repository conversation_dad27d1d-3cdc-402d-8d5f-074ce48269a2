package ttfund.web.financialplannerapi.config;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.JSONToken;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;

import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @file : FastJsonDateTimeDeserializer.java
 * @Software: IntelliJ IDEA
 * @date: 2020/8/17 11:03
 * @description: 自定义时间格式反序列化器
 * @version: 1.0
 */
public class FastJsonDateTimeDeserializer implements ObjectDeserializer {

    private static final List<String> formatStyles =new ArrayList<>();
    static {
        formatStyles.add("yyyy-MM-dd HH:mm:ss");
        formatStyles.add("yyyy/MM/dd HH:mm:ss");
    }


    @SuppressWarnings("unchecked")
    @Override
    public Date deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        Date  date = null;

        String stringVal = parser.getLexer().stringVal();
        int indexStart = stringVal.indexOf("/Date(");
        if(indexStart>=0 )
        {
            int indexEnd =  stringVal.lastIndexOf(")/");
            if(indexEnd >=0)
            {
              String longStrVal = stringVal.replace("/Date(", "").replace("-0000","").replace(")/","").trim();
                  Long dateLongVal = Long.parseLong(longStrVal);
              if(dateLongVal!=null)
                {
                     date = new Date(dateLongVal);
                     return  date;
                }
            }
        }

        for (String formatStyle : formatStyles) {
            try {
                date =this.getDateTime(formatStyle, parser.getLexer().stringVal());
                if (date != null)
                {
                    break;
                }

            } catch (Exception ex) {
                System.out.println(ex.getMessage());
            }
        }
        return date;
    }

    /**
     * 日期格式化
     * @param sFormat
     * @param sDate
     * @return
     */
    private Date  getDateTime(String sFormat, String sDate) {
        Date  dateTime = null;
        try {
            dateTime = new SimpleDateFormat(sFormat).parse(sDate);
        } catch (Exception e) {
           // LogHelper.error(MessageFormat.format("date format error, format={0}, date={1}, error={2}", sFormat, sDate, e));
        }
        return dateTime;
    }
    @Override
    public int getFastMatchToken() {
        return JSONToken.LITERAL_INT;
    }
}
