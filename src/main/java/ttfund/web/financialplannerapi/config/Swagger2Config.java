package ttfund.web.financialplannerapi.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * Swagger2 接口配置
 * <AUTHOR>
 * @Date 2021/9/30 10:05
 */

@Configuration
@EnableKnife4j
//@EnableSwagger2
//@Profile({"dev","test"})
@ConditionalOnProperty(name = "swagger.enabled", havingValue = "true")
public class Swagger2Config {
    /**
     * 添加摘要信息(Docket)
     */
    @Bean
    public Docket controllerApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(new ApiInfoBuilder()
                        .title("理财师app服务接口")
                        .description("用于管理理财师app的服务,具体包括签约、预约、评价、理财师管理等模块")
                        .contact(new Contact("lu", null, null))
                        .version("版本号:1.0")
                        .build())
                .select()
                //.apis(RequestHandlerSelectors.basePackage("com.hehe.controller"))
                .paths(PathSelectors.any())
                .build();
    }
}