package ttfund.web.financialplannerapi.config;

import com.ttfund.web.base.helper.DruidHelper;
import com.ttfund.web.base.helper.KafkaProducerHelper;
import com.ttfund.web.base.helper.MongodbHelper;
import com.ttfund.web.core.register.AppCore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import ttfund.web.financialplannerapi.constant.AppConfigConstant;
import ttfund.web.financialplannerapi.register.IRedisEnhance;
import ttfund.web.financialplannerapi.register.RedisConnEnhanceImpl;
import ttfund.web.financialplannerapi.util.MysqlUtil;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class App {

    private static final Logger logger = LoggerFactory.getLogger(App.class);
    @Resource
    AppCore appCore;

    @Resource
    AppConfig appConfig;

    public IRedisEnhance redisread;

    public IRedisEnhance rediswrite;
    //    public IRedis cfhrediswrite;
//    public MongodbHelper cfhmongodbwrite;
//    public MongodbHelper cfhmongodbread;
//    public MongodbHelper hqmongodbread;
//    public MongodbHelper configmongodbread;
    public KafkaProducerHelper kafkaProducerHelper;
//    public DruidHelper imVerticaRead;
    public DruidHelper lcsMysqlWrite;
    public DruidHelper lcsMysqlRead;

    public MongodbHelper pricemongodbread;

    @PostConstruct
    private void init() {

//        imVerticaRead = new DruidHelper(appConfig.vertica_im_connsr_read,"com.vertica.jdbc.Driver");
//        appCore.dsreload.put(AppConfigConstant.VERTICA_IM_CONNSR_READ, imVerticaRead);

        lcsMysqlWrite = new DruidHelper(appConfig.mysql_lcs_connsr_write, "com.mysql.cj.jdbc.Driver");
        appCore.dsreload.put(AppConfigConstant.MYSQL_LCS_CONNSR_WRITE, lcsMysqlWrite);

        lcsMysqlRead = new DruidHelper(appConfig.mysql_lcs_connsr_read,"com.mysql.cj.jdbc.Driver");
        appCore.dsreload.put(AppConfigConstant.MYSQL_LCS_CONNSR_READ, lcsMysqlRead);

        rediswrite = new RedisConnEnhanceImpl(appConfig.redis_wirte);
        appCore.dsreload.put(AppConfigConstant.REDIS_WIRTE, rediswrite);

        redisread = new RedisConnEnhanceImpl(appConfig.redis_read);
        appCore.dsreload.put(AppConfigConstant.REDIS_READ, redisread);

        pricemongodbread = new MongodbHelper(appConfig.mongodb_trade_day_connsr_read);
        appCore.dsreload.put(AppConfigConstant.MONGODB_TRADEDAY_CONNSR_READ,pricemongodbread);

        kafkaProducerHelper = new KafkaProducerHelper(appConfig.kafka_producer);
        appCore.dsreload.put(AppConfigConstant.KAFKA_PRODUCER, kafkaProducerHelper);

        appCore.rediscacheread = redisread;
        appCore.rediscachewrite = rediswrite;

        MysqlUtil.enhance(lcsMysqlWrite);

    }
}
