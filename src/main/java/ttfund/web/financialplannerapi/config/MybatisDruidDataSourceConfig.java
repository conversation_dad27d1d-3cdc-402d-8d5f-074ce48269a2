package ttfund.web.financialplannerapi.config;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import ttfund.web.financialplannerapi.constant.AppConfigConstant;
import ttfund.web.financialplannerapi.mybatis.util.DataSourceDTO;
import ttfund.web.financialplannerapi.util.http.UrlDecodeUtil;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
public class MybatisDruidDataSourceConfig {

    private static final Logger logger = LoggerFactory.getLogger(MybatisDruidDataSourceConfig.class);

    private final DataSource dataSource;
    private final DefaultDataSourceCreator dataSourceCreator;
    private final Environment env;

    @Value("${" + AppConfigConstant.MYSQL_LCS_CONNSR_WRITE + "}")
    public String mysqlFinancialplannerConnsrWrite;

    @Value("${" + AppConfigConstant.MYSQL_LCS_CONNSR_READ + "}")
    public String mysqlFinancialplannerConnsrRead;

    @Value("${" + AppConfigConstant.VERTICA_IM_CONNSR_READ + "}")
    public String verticaWrite;

    @PostConstruct
    public void init() {
        String namespaces = this.env.getProperty("apollo.bootstrap.namespaces", "application");
        String[] namespacesArr = namespaces.split(",");

        // 添加配置更改监听
        for (String namespace : namespacesArr) {
            Config config = ConfigService.getConfig(namespace);
            config.addChangeListener(this::onchange);
        }

        this.initMysqlDataSource();
//        this.initVerticaDataSource();
    }

    private void onchange(ConfigChangeEvent changeEvent) {
        for (String key : changeEvent.changedKeys()) {
            ConfigChange change = changeEvent.getChange(key);
            if (change.getPropertyName() == null) {
                continue;
            }

            if (AppConfigConstant.MYSQL_LCS_CONNSR_WRITE.equals(change.getPropertyName())) {
                this.initMysqlDataSource("master", change.getNewValue());
            }

            if (AppConfigConstant.MYSQL_LCS_CONNSR_READ.equals(change.getPropertyName())) {
                this.initMysqlDataSource("read", change.getNewValue());
            }


//            if (AppConfigConstant.VERTICA_IM_CONNSR_READ.equals(change.getPropertyName())) {
//                this.initVerticaDataSource("vertica", change.getNewValue());
//            }
        }
    }

    private void initMysqlDataSource() {
        this.initMysqlDataSource("master",mysqlFinancialplannerConnsrWrite);
        this.initMysqlDataSource("read",mysqlFinancialplannerConnsrRead);
    }

    private void initVerticaDataSource() {
        this.initVerticaDataSource("vertica",verticaWrite);
    }


    private void initMysqlDataSource(String poolName,String url) {
        this.initDataSource(poolName,url,"com.mysql.cj.jdbc.Driver");
    }

    private void initClickHouseDataSource(String poolName,String url) {
        this.initDataSource(poolName,url,"com.clickhouse.jdbc.ClickHouseDriver");
    }

    private void initVerticaDataSource(String poolName,String url) {
        url = UrlDecodeUtil.decode(url);
        this.initDataSource(poolName,url,"com.vertica.jdbc.Driver");
    }

    private void initDataSource(String poolName,String url,String driver) {
        Pattern pattern = Pattern.compile("user=([^&]*)&password=([^&]*)");
        Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            String username = matcher.group(1);
            String password = matcher.group(2);

            String newJdbcUrl = url.replaceAll("(user=[^&]*&|password=[^&]*&?)", "");
            newJdbcUrl = newJdbcUrl.replaceAll("(&|\\?)$", "");

            this.add(new DataSourceDTO(poolName).setUrl(newJdbcUrl).setUsername(username)
                    .setPassword(password).setDriverClassName(driver));
            logger.info("成功添加或更新数据源 {} ： {}", poolName, newJdbcUrl);
        } else {
            logger.error("jdbc链接未配置 username 或 password ");
        }
    }

    /**
     * 获取当前所有数据源
     */
    private Set<String> now() {
        DynamicRoutingDataSource ds = (DynamicRoutingDataSource) dataSource;
        return ds.getDataSources().keySet();
    }

    /**
     * 添加数据源
     */
    private Set<String> add(DataSourceDTO dto) {
        DataSourceProperty dataSourceProperty = new DataSourceProperty();
        BeanUtils.copyProperties(dto, dataSourceProperty);
        DynamicRoutingDataSource ds = (DynamicRoutingDataSource) dataSource;
        try{
            DataSource dataSource = dataSourceCreator.createDataSource(dataSourceProperty);
            ds.addDataSource(dto.getPoolName(), dataSource);
            return ds.getDataSources().keySet();
        }catch (Exception e){
            return add(dto);
        }
    }

    /**
     * 删除数据源
     */
    private String remove(String name) {
        DynamicRoutingDataSource ds = (DynamicRoutingDataSource) dataSource;
        ds.removeDataSource(name);
        return "删除成功";
    }
}
