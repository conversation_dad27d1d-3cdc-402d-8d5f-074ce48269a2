package ttfund.web.financialplannerapi.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @ClassName FTPConfig
 * @Description
 * <AUTHOR>
 * @Date 2022/5/25 9:33
 * @Vserion 1.0.0
 */
@Component
public class FTPConfig {

    @Value("${ftp.ip}")
    public String ftp_ip;

    @Value("${ftp.port}")
    public Integer ftp_port;

    @Value("${ftp.username}")
    public String ftp_username;

    @Value("${ftp.password}")
    public String ftp_password;

    @Value("${ftp.prefixPath}")
    public String ftp_prefixPath;

    @Value("${ftp.access.url}")
    public String ftp_access_url;
}
