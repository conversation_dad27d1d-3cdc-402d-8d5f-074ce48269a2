import com.ttfund.web.core.model.ApiResponse;
import com.ttfund.web.core.model.baserequest.AuthBaseRequest;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import ttfund.web.financialplannerapi.FinacialPlannerApiApplication;
import ttfund.web.financialplannerapi.config.App;
import ttfund.web.financialplannerapi.data.impl.ContractMysqlImpl;
import ttfund.web.financialplannerapi.data.impl.PassportBindApiImpl;
import ttfund.web.financialplannerapi.data.impl.UserInfoAPIService;
import ttfund.web.financialplannerapi.model.po.mysqlpo.UserRolePO;
import ttfund.web.financialplannerapi.model.reponse.*;
import ttfund.web.financialplannerapi.model.request.*;
import ttfund.web.financialplannerapi.service.IContractService;
import ttfund.web.financialplannerapi.util.RedisLockUtil;

import java.util.*;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @date 2023/5/6
 * @Description
 */
@SpringBootTest(classes = FinacialPlannerApiApplication.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ContractTest {

    @Autowired
    private IContractService contractService;

    @Autowired
    private App app;

    @MockBean
    private UserInfoAPIService userInfoAPIService;

    @MockBean
    private PassportBindApiImpl passportBindApi;

    @MockBean
    private RedisLockUtil lockUtil;

    @SpyBean
    private ContractMysqlImpl contractMysql;

    @Order(1)
    @Test
    public void testLcsWindow() {
        //启动先清缓存
        app.rediswrite.del("ttfund.lcs.window.1510286370558492.1");
        app.rediswrite.del("ttfund.lcs.institutionId.list");
        Set<String> apiUserGroup = new HashSet<>();
        apiUserGroup.add("TG_89415b1b28d54854");
        apiUserGroup.add("TG_731ca9c2dee2a718");
        Mockito.when(userInfoAPIService.getUserGroup(Mockito.anyString(), Mockito.anySet())).thenReturn(apiUserGroup);
        Mockito.when(passportBindApi.getPassportBindInfo(Mockito.anyString())).thenReturn("test");

        LcsWindowRequest lcsWindowRequest = new LcsWindowRequest();
        Set<String> code = new LinkedHashSet<>();
        lcsWindowRequest.setCode(code);
        //无产品code情况
        assertEquals(null, contractService.getLcsWindow(lcsWindowRequest).getData());

        //code都在缓存的情况
        lcsWindowRequest.setPassportid("1510286370558492");
        lcsWindowRequest.setPageFlag(1);
        code.add("ARTNKDP");
        code.add("WWUBK0N");
        contractService.getLcsWindow(lcsWindowRequest);
        List<LcsWindowResponse> exceptResponse = new ArrayList<>();
        exceptResponse.add(new LcsWindowResponse("80001124", "易方达基金", "https://avator.eastmoney.com/qface/3293065075183751/120", "fund://mp.1234567.com.cn/weex/fund0144d8f6c9cdf3/pages/chat-area-single/index?type=1&from=tg&institutionId=80001124&product=激进投资策略", null, "ARTNKDP", null, null));
        exceptResponse.add(new LcsWindowResponse("100031", "华宸未来基金", null, "fund://mp.1234567.com.cn/weex/fund0144d8f6c9cdf3/pages/chat-area-single/index?type=1&from=tg&institutionId=100031&product=中欧现金增强", null, "WWUBK0N", "测试标题", null));
//        exceptResponse.add(new LcsWindowResponse("80000333", "中欧财富",  "https://avator.eastmoney.com/qface/8215496606123194/120", "fund://mp.1234567.com.cn/weex/fund0144d8f6c9cdf3/pages/chat-area-single/index?type=1&from=tg&institutionId=80000333&product=中欧财富+1号",  "超级无敌lxy", "GBPNYBY"));
        assertEquals(exceptResponse, contractService.getLcsWindow(lcsWindowRequest).getData());
        Mockito.when(passportBindApi.getPassportBindInfo(Mockito.anyString())).thenReturn(null);
        //存在code不在缓存的情况
        code.add("GBPNYBY");
        code.add("MRRGIL8");
        code.add("5OR1KP1");
        code.add("MRRGIL7");
        //交易账号为空情况
        lcsWindowRequest.setUserid(null);
        assertEquals(exceptResponse, contractService.getLcsWindow(lcsWindowRequest).getData());
        exceptResponse.add(new LcsWindowResponse("80001124", "易方达基金", "https://avator.eastmoney.com/qface/3293065075183751/120", "fund://mp.1234567.com.cn/weex/fund0144d8f6c9cdf3/pages/chat-area-single/index?type=1&from=tg&institutionId=80001124&product=智航易投3号", null, "5OR1KP1", null, null));
        //交易账号不为空情况
        Mockito.when(passportBindApi.getPassportBindInfo(Mockito.anyString())).thenReturn("test");
        assertEquals(exceptResponse, contractService.getLcsWindow(lcsWindowRequest).getData());
    }

    @Order(2)
    @Test
    public void testContract() {
        ContractRequest contractRequest = new ContractRequest();
        contractRequest.setPassportid("1510286370558492");
        contractRequest.setFinancialPlannerId("3293065075183751");
        contractRequest.setInstitutionId("80001124");
        ApiResponse<Boolean, Object> result = new ApiResponse<>();
        Mockito.when(contractMysql.selectByInsAndPass(Mockito.anyString(), Mockito.anyString())).thenCallRealMethod();
        Mockito.when(contractMysql.getLastContractRecord(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenCallRealMethod();
        //加锁异常的情况
        Mockito.when(lockUtil.lock(Mockito.any(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyLong())).thenReturn(false);
        assertEquals(null, contractService.contract(contractRequest, result).getData());

        //处于已签约状态
        Mockito.when(lockUtil.lock(Mockito.any(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyLong())).thenReturn(true);
        assertEquals(true, contractService.contract(contractRequest, result).getData());

        //无绑定记录
        result = new ApiResponse<>();
        contractRequest.setPassportid("8691335967152228");
        assertEquals(false, contractService.contract(contractRequest, result).getData());
        //未处于待签约状态，理财师不存在
        contractRequest.setFinancialPlannerId("3293065075183752");
        assertEquals(false, contractService.contract(contractRequest, result).getData());

        //处于待签约，有签约历史
        result = new ApiResponse<>();
        contractRequest.setPassportid("8691335967152229");
        contractRequest.setFinancialPlannerId("3293065075183751");
        assertEquals(true, contractService.contract(contractRequest, result).getData());

        //签约自营理财师
        result = new ApiResponse<>();
        contractRequest.setInstitutionId("100031");
        contractRequest.setPassportid("8691335967152229");
        contractRequest.setFinancialPlannerId("2262385746458030");
        assertEquals(true, contractService.contract(contractRequest, result).getData());

        //数据库异常，插入记录失败
        result = new ApiResponse<>();
        contractRequest.setPassportid("8691335967152230");
        contractRequest.setDeviceid("ttfund");
        Mockito.doReturn(false).when(contractMysql).contractOne(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any());
        assertEquals(false, contractService.contract(contractRequest, result).getData());

        //数据库异常，插入记录成功，删除绑定失败
        result = new ApiResponse<>();
        Mockito.doReturn(true).when(contractMysql).contractOne(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any());
        Mockito.doReturn(false).when(contractMysql).delBindRecord(Mockito.any());
        assertEquals(false, contractService.contract(contractRequest, result).getData());
    }

    @Order(3)
    @Test
    public void testStatus() {
        Mockito.when(contractMysql.selectByInsAndPass(Mockito.anyString(), Mockito.anyString())).thenCallRealMethod();
        //理财师id不存在
        ApiResponse<ContractStatusResponse, Object> result = new ApiResponse<>();
        String financialPlannerId = "2568306462015190";
        String institutionId = "100031";
        String passportId = "1510286370558492";
        assertEquals(null, contractService.status(passportId, institutionId, financialPlannerId, result).getData());

        //有绑定状态
        financialPlannerId = "1552396488740710";
        passportId = "1010285265217685";
        UserRolePO userRolePO = new UserRolePO();
        userRolePO.setRoleId("4");
        userRolePO.setId("8");
        userRolePO.setInstitutionId("100031");
        userRolePO.setInstitutionName("天天基金");
        userRolePO.setPassportId("1552396488740710");
        userRolePO.setPersonalProfile("刘子yu");

        ContractStatusResponse response = new ContractStatusResponse();
        response.setBind(true);
        response.setFinancialPlannerId(financialPlannerId);
        response.setFinancialPlanner(userRolePO);
        response.setServiceAgreementName("理财师服务协议");
        response.setServiceAgreementLook("https://img.1234567.com.cn/h5pdf/2022063010521745433.pdf");
        response.setRiskNotesName("风险揭示书");
        response.setRiskNotesLook("https://img.1234567.com.cn/h5pdf/2023040608185604176.pdf");
        response.setUserInfoName("个人信息");
        response.setUserInfoLook("https://img.1234567.com.cn/h5pdf/2022063010505967998.pdf");
        response.setUserInfoNotes("为理财师更好地了解您的投资需求以提供个性化服务，请确认是否同意天天基金向理财师披露您在天天基金留存的基本身份资料、风险测评信息、基金持仓信息等（具体可点击下方个人信息查看），并跟基金管理人签约理财师服务协议，理财师将根据法律法规规定及《理财师服务协议》约定使用您的信息，并对您的信息严格保密。");
        response.setInvestmentAgreementName("投顾协议");
        response.setInvestmentAgreementLook("https://img.1234567.com.cn/h5pdf/2022063010505967998.pdf");
        assertEquals(response, contractService.status(passportId, institutionId, financialPlannerId, result).getData());

        //无签约和绑定，也无理财师可分配
        institutionId = "100032";
        result = new ApiResponse<>();
        assertEquals(null, contractService.status(passportId, institutionId, null, result).getData());

        //无签约和绑定，分配理财师信息
        institutionId = "100030";
        passportId = "1510286370558493";
        response.setBind(false);
        response.setFinancialPlannerId("9558365522902557");
        response.setServiceAgreementName(null);
        response.setServiceAgreementLook(null);
        response.setRiskNotesName(null);
        response.setRiskNotesLook(null);
        response.setInvestmentAgreementName(null);
        response.setInvestmentAgreementLook(null);
        response.setUserInfoLook(null);
        userRolePO.setId("8c5fb535d52b4a85bb7ed8438a590ff1");
        userRolePO.setInstitutionId("100030");
        userRolePO.setInstitutionName("天天基金0");
        userRolePO.setPassportId("9558365522902557");
        userRolePO.setPersonalProfile("专业理财师");
        userRolePO.setImg("https://img.1234567.com.cn/h5pdf/2022063010505967998.pdf");

        assertEquals(response, contractService.status(passportId, institutionId, null, result).getData());
    }

    @Order(4)
    @Test
    public void testGetPlannerByUser() {
        app.rediswrite.del("financialplannerapi:contract:query:1510286370558492");
        ApiResponse<List<UserPlannerResponse>, Object> result = new ApiResponse<>();
        //无签约理财师的情况
        String passportId = "1510286370558493";
        List<UserPlannerResponse> expectedList = new ArrayList<>();
        assertEquals(null, contractService.getPlannerByUser(passportId, result).getData());

        //有签约理财师的情况
        passportId = "1510286370558492";
        UserPlannerResponse planner1 = new UserPlannerResponse();
        UserPlannerResponse planner2 = new UserPlannerResponse();
        expectedList.add(planner1);
        expectedList.add(planner2);
        planner1.setFinancialPlannerId("3293065075183751");
        planner1.setInstitutionId("80001124");
        planner1.setInstitutionName("易方达基金");
        planner1.setImgUrl("https://dataapineice.1234567.com.cn/files/images/upper_body_avatar_22d95a4785e999a9ea4aeb5775afb0cd.png?time=1666835923525");
        planner1.setPriority(100);
        planner2.setFinancialPlannerId("7175336485189618");
        planner2.setInstitutionId("80560408");
        planner2.setInstitutionName("同泰基金");
        planner2.setImgUrl("https://dataapineice.1234567.com.cn/files/images/upper_body_avatar_9998376388704008.png");
        planner2.setFinancialPlannerName("股友8k2923M582");
        planner2.setPriority(100);
        assertEquals(expectedList, contractService.getPlannerByUser(passportId, result).getData());
        //走缓存的情况
        assertEquals(expectedList, contractService.getPlannerByUser(passportId, result).getData());
    }

    @Order(5)
    @Test
    public void testGetAllPlannerByUser() {
        app.rediswrite.del("financialplannerapi:contract:queryAll:1510286370558492");
        ApiResponse<List<UserPlannerResponse>, Object> result = new ApiResponse<>();
        //无签约理财师的情况
        String passportId = "1510286370558493";
        List<UserPlannerResponse> expectedList = new ArrayList<>();
        assertEquals(null, contractService.getAllPlannerByUser(passportId, result).getData());

        //有签约理财师的情况
        passportId = "1510286370558492";
        UserPlannerResponse planner0 = new UserPlannerResponse();
        UserPlannerResponse planner1 = new UserPlannerResponse();
        UserPlannerResponse planner2 = new UserPlannerResponse();
        expectedList.add(planner0);
        expectedList.add(planner1);
        expectedList.add(planner2);
        planner0.setFinancialPlannerId("9558365522902556");
        planner0.setInstitutionId("100031");
        planner1.setFinancialPlannerId("3293065075183751");
        planner1.setInstitutionId("80001124");
        planner2.setFinancialPlannerId("7175336485189618");
        planner2.setInstitutionId("80560408");
        assertEquals(expectedList, contractService.getAllPlannerByUser(passportId, result).getData());
        //走缓存的情况
        assertEquals(expectedList, contractService.getAllPlannerByUser(passportId, result).getData());
    }

    @Order(6)
    @Test
    public void testChange() {
        ApiResponse<ChangePlannerResponse, Object> result = new ApiResponse<>();
        ChangePlannerRequest changePlannerRequest = new ChangePlannerRequest();
        changePlannerRequest.setPassportid("1185296668333018");
        changePlannerRequest.setInstitutionId("80001124");
        changePlannerRequest.setDeviceid("ttfund");
        //加锁异常的情况
        Mockito.when(lockUtil.lock(Mockito.any(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyLong())).thenReturn(false);
        assertEquals(null, contractService.change(changePlannerRequest, result).getData());

        //有未完成预约记录
        Mockito.when(lockUtil.lock(Mockito.any(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyLong())).thenReturn(true);
        assertEquals(null, contractService.change(changePlannerRequest, result).getData());

        //白名单账号，随机更换理财师，结果不可控
        changePlannerRequest.setInstitutionId("100031");
        assertEquals(false, contractService.change(changePlannerRequest, result).getData() == null);

        //数据库异常
        result = new ApiResponse<>();
        ChangePlannerResponse response = new ChangePlannerResponse();
        response.setStatus(false);
        Mockito.doReturn(false).when(contractMysql).contractOne(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any());
        assertEquals(response, contractService.change(changePlannerRequest, result).getData());

        //非白名单账号，无可用理财师
        result = new ApiResponse<>();
        changePlannerRequest.setPassportid("1510286370558492");
        changePlannerRequest.setInstitutionId("80560408");
        Mockito.when(contractMysql.contractOne(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenCallRealMethod();
        assertEquals(null, contractService.change(changePlannerRequest, result).getData());

    }

    @Order(8)
    @Test
    public void testCancelContract() {
        CancelContractRequest request = new CancelContractRequest();
        request.setInstitutionId("80001124");
        //无签约记录
        request.setPassportid("1510286370558491");
        ApiResponse<Boolean, Object> result = new ApiResponse<>();
        assertEquals(null, contractService.cancelContract(request, result).getData());

        //有未完成预约记录
        request.setPassportid("1510286370558492");
        result = new ApiResponse<>();
        assertEquals(null, contractService.cancelContract(request, result).getData());

        //可解约
        request.setInstitutionId("80560408");
        assertEquals(true, contractService.cancelContract(request, result).getData());
    }

    @Order(9)
    @Test
    public void testIsCancel() {
        QueryContractRequest queryContractRequest = new QueryContractRequest();
        ApiResponse<Boolean, Object> response = new ApiResponse<>();
        assertEquals(null, contractService.isCancel(queryContractRequest).getData());
        queryContractRequest.setInstitutionId("80560408");
        queryContractRequest.setFinancialPlannerId("7175336485189618");
        queryContractRequest.setPassportid("1510286370558492");
        assertEquals(false, contractService.isCancel(queryContractRequest).getData());
    }

    @Order(10)
    @Test
    public void testBindPlanner() {
        AuthBaseRequest request = new AuthBaseRequest();
        ApiResponse<BindStatusResponse, Object> result = new ApiResponse<>();
        BindStatusResponse bindStatus = new BindStatusResponse();
        //白名单账号无绑定情况
        request.setPassportid("1185296668333018");
        assertEquals(false, contractService.bindPlanner(request, result).getData() == null);

        //非白名单账号已绑定情况
        request.setPassportid("1510286370558492");
        assertEquals("9558365522902556", contractService.bindPlanner(request, result).getData().getFinancialPlannerId());

        //之前有过签约情况（运行前删库）
        request.setPassportid("1510286370558490");
        assertEquals("1552396488740710", contractService.bindPlanner(request, result).getData().getFinancialPlannerId());
    }
}
