import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.financialplannerapi.FinacialPlannerApiApplication;
import ttfund.web.financialplannerapi.config.App;

import java.util.List;
import java.util.Map;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = FinacialPlannerApiApplication.class)
public class TestApp {

    @Autowired
    App app;

    public static void main(String[] args) {

        Integer str = null;
        System.out.println(0 != str);
    }

    @Test
    public void test(){

        System.out.println();
    }
}
