package ttfund.web.financialplannerapi.service.impl;

import com.ttfund.web.base.helper.DateHelper;
import com.ttfund.web.base.redishelper.impl.RedisConnImpl;
import com.ttfund.web.core.constant.ResultCode;
import com.ttfund.web.core.model.ApiResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.financialplannerapi.FinacialPlannerApiApplication;
import ttfund.web.financialplannerapi.config.App;
import ttfund.web.financialplannerapi.constant.ErrorCodeEnum;
import ttfund.web.financialplannerapi.data.impl.*;
import ttfund.web.financialplannerapi.model.po.mysqlpo.UserRolePO;
import ttfund.web.financialplannerapi.model.reponse.*;
import ttfund.web.financialplannerapi.model.request.AddAppointmentRequest;
import ttfund.web.financialplannerapi.model.request.AppointmentQueryRequest;
import ttfund.web.financialplannerapi.register.RedisConnEnhanceImpl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.*;

/**
 * @Author: lvwei
 * @Description: TODO
 * @DateTime: 2023/5/6 16:03
 **/
@SpringBootTest(classes = FinacialPlannerApiApplication.class)
@RunWith(SpringRunner.class)
public class AppointmentServiceImplTest {
    @SpyBean
    private AppointmentServiceImpl appointmentService;

    @MockBean
    private AppointmentMysqlServiceImpl appointmentMysqlService;

    @MockBean
    private PassportBindApiImpl passportBindApi;

    @MockBean
    private UserInfoAPIService userInfoAPIService;

    @MockBean
    private LcsMysqlServiceImpl lcsMysqlService;

    @MockBean
    private UserRoleMysql userRoleMysql;

    @MockBean
    private ContractMysqlImpl contractMysql;

    @MockBean
    private IMApiService imApiService;

    @SpyBean
    private App app;

    @MockBean
    private RedisConnEnhanceImpl redisConn;

    private final String passportId = "123456";
    private final String financialPlannerId = "654321";
    private final String institutionId = "888888";
    private final String customerNo = "10001";
    private final String customerName = "张三";
    private final String newCustomerName = "*三";
    private final String decryptMobile = "136****1234";

    UserBaseInfoResponse userBaseInfoResponse = new UserBaseInfoResponse();
    UserBaseInfo result = new UserBaseInfo();


    @Before
    public void before(){
        userBaseInfoResponse.setSucceed(true);
        result.setCustomerName(customerName);
        result.setSex("1");
        userBaseInfoResponse.setResult(result);
        MockitoAnnotations.initMocks(this);
        app.rediswrite = redisConn;

        when(redisConn.incrBy(any(),any(),any())).thenReturn(1L);
        when(redisConn.hmset(any(),any())).thenReturn("OK");
    }

    @Test
    public void testQueryAppointmentInfoNoAppoint() throws Exception {
        // Arrange
        when(appointmentMysqlService.queryAppointmentByParams(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(null);
        when(passportBindApi.getPassportBindInfo(anyString())).thenReturn(customerNo);

        UserBaseInfoResponse userBaseInfoResponse = new UserBaseInfoResponse();
        userBaseInfoResponse.setSucceed(true);
        UserBaseInfo result = new UserBaseInfo();
        result.setCustomerName(customerName);
        result.setSex("1");
        userBaseInfoResponse.setResult(result);
        when(userInfoAPIService.getUserBaseInfo(anyString())).thenReturn(userBaseInfoResponse);

        DecryptMobileResponse de = new DecryptMobileResponse();
        de.setSuccess(true);
        de.setResult(decryptMobile);
        when(userInfoAPIService.decryptMobile(userBaseInfoResponse.getResult().getEncryptMobileTel())).thenReturn(de);

        LcsPersonalInfoResponse lcsInfo = new LcsPersonalInfoResponse();
        lcsInfo.setFinancialPlannerNickName("李四");
        when(lcsMysqlService.querySingle(anyString(), anyString())).thenReturn(lcsInfo);

        AppointmentQueryRequest params = new AppointmentQueryRequest();
        params.setPassportid(passportId);
        params.setFinancialPlannerId(financialPlannerId);
        params.setInstitutionId(institutionId);


        doReturn(1).when(appointmentService).getVByPassportId(anyString(), any());

        // Act
        ApiResponse<AppointmentInfoResponse, Object> res = appointmentService.queryAppointmentInfo(params);

        // Assert
        assertEquals(0, res.getErrorCode());
        assertEquals(newCustomerName, res.getData().getCustomerName());
        assertEquals("1", res.getData().getSex());
        assertEquals(decryptMobile, res.getData().getMaskMobileTel());
        assertEquals("7KyFbfdhIzHf3viG/XyIhw==", res.getData().getEncryptMobileTel());
        assertEquals(lcsInfo, res.getData().getLcsPersonalInfoResponse());
    }

    @Test
    public void testQueryAppointmentInfoHasAppoint() throws Exception {
        // Arrange
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("7KyFbfdhIzHf3viG/XyIhw==");
        appointmentInfo.setAppointment(new Date());
        when(appointmentMysqlService.queryAppointmentByParams(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(appointmentInfo);
        when(passportBindApi.getPassportBindInfo(anyString())).thenReturn(customerNo);


        when(userInfoAPIService.getUserBaseInfo(anyString())).thenReturn(userBaseInfoResponse);

        DecryptMobileResponse de = new DecryptMobileResponse();
        de.setSuccess(true);
        de.setResult(decryptMobile);
        when(userInfoAPIService.decryptMobile(userBaseInfoResponse.getResult().getEncryptMobileTel())).thenReturn(de);

        LcsPersonalInfoResponse lcsInfo = new LcsPersonalInfoResponse();
        lcsInfo.setFinancialPlannerNickName("李四");
        when(lcsMysqlService.querySingle(anyString(), anyString())).thenReturn(lcsInfo);

        AppointmentQueryRequest params = new AppointmentQueryRequest();
        params.setPassportid(passportId);
        params.setFinancialPlannerId(financialPlannerId);
        params.setInstitutionId(institutionId);


        doReturn(1).when(appointmentService).getVByPassportId(anyString(), any(ApiResponse.class));

        // Act
        ApiResponse<AppointmentInfoResponse, Object> res = appointmentService.queryAppointmentInfo(params);

        // Assert
        assertEquals(0, res.getErrorCode());
        assertEquals(newCustomerName, res.getData().getCustomerName());
        assertEquals("1", res.getData().getSex());
        assertEquals(decryptMobile, res.getData().getMaskMobileTel());
        assertEquals("7KyFbfdhIzHf3viG/XyIhw==", res.getData().getEncryptMobileTel());
        assertEquals(lcsInfo, res.getData().getLcsPersonalInfoResponse());
    }

    @Test
    public void testQueryAppointmentInfo_NotCustomerNo(){
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("7KyFbfdhIzHf3viG/XyIhw==");
        appointmentInfo.setAppointment(new Date());
        when(appointmentMysqlService.queryAppointmentByParams(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(appointmentInfo);
        when(passportBindApi.getPassportBindInfo(anyString())).thenReturn(null);


        when(userInfoAPIService.getUserBaseInfo(anyString())).thenReturn(userBaseInfoResponse);

        DecryptMobileResponse de = new DecryptMobileResponse();
        de.setSuccess(true);
        de.setResult(decryptMobile);
        when(userInfoAPIService.decryptMobile(userBaseInfoResponse.getResult().getEncryptMobileTel())).thenReturn(de);

        LcsPersonalInfoResponse lcsInfo = new LcsPersonalInfoResponse();
        lcsInfo.setFinancialPlannerNickName("李四");
        when(lcsMysqlService.querySingle(anyString(), anyString())).thenReturn(lcsInfo);

        AppointmentQueryRequest params = new AppointmentQueryRequest();
        params.setPassportid(passportId);
        params.setFinancialPlannerId(financialPlannerId);
        params.setInstitutionId(institutionId);


        doReturn(1).when(appointmentService).getVByPassportId(anyString(), any(ApiResponse.class));

        // Act
        ApiResponse<AppointmentInfoResponse, Object> res = appointmentService.queryAppointmentInfo(params);
        assertEquals(ErrorCodeEnum.NoCustomerNo.getCode(),res.getErrorCode());
    }

    @Test
    public void testQueryAppointmentInfo_UserBaseInfoQueryERROR(){
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("7KyFbfdhIzHf3viG/XyIhw==");
        appointmentInfo.setAppointment(new Date());
        when(appointmentMysqlService.queryAppointmentByParams(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(appointmentInfo);
        when(passportBindApi.getPassportBindInfo(anyString())).thenReturn(customerNo);


        when(userInfoAPIService.getUserBaseInfo(anyString())).thenReturn(null);

        DecryptMobileResponse de = new DecryptMobileResponse();
        de.setSuccess(true);
        de.setResult(decryptMobile);
        when(userInfoAPIService.decryptMobile(userBaseInfoResponse.getResult().getEncryptMobileTel())).thenReturn(de);

        LcsPersonalInfoResponse lcsInfo = new LcsPersonalInfoResponse();
        lcsInfo.setFinancialPlannerNickName("李四");
        when(lcsMysqlService.querySingle(anyString(), anyString())).thenReturn(lcsInfo);

        AppointmentQueryRequest params = new AppointmentQueryRequest();
        params.setPassportid(passportId);
        params.setFinancialPlannerId(financialPlannerId);
        params.setInstitutionId(institutionId);


        doReturn(1).when(appointmentService).getVByPassportId(anyString(), any(ApiResponse.class));

        // Act
        ApiResponse<AppointmentInfoResponse, Object> res = appointmentService.queryAppointmentInfo(params);
        assertEquals(ErrorCodeEnum.UserBaseInfoQueryERROR.getCode(),res.getErrorCode());
    }

    @Test
    public void testQueryAppointmentInfo_PlannerChanged(){
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("7KyFbfdhIzHf3viG/XyIhw==");
        appointmentInfo.setAppointment(new Date());
        when(appointmentMysqlService.queryAppointmentByParams(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(appointmentInfo);
        when(passportBindApi.getPassportBindInfo(anyString())).thenReturn(customerNo);


        when(userInfoAPIService.getUserBaseInfo(anyString())).thenReturn(userBaseInfoResponse);

        DecryptMobileResponse de = new DecryptMobileResponse();
        de.setSuccess(true);
        de.setResult(decryptMobile);
        when(userInfoAPIService.decryptMobile(userBaseInfoResponse.getResult().getEncryptMobileTel())).thenReturn(de);

        LcsPersonalInfoResponse lcsInfo = new LcsPersonalInfoResponse();
        lcsInfo.setFinancialPlannerNickName("李四");
        when(lcsMysqlService.querySingle(anyString(), anyString())).thenReturn(null);

        AppointmentQueryRequest params = new AppointmentQueryRequest();
        params.setPassportid(passportId);
        params.setFinancialPlannerId(financialPlannerId);
        params.setInstitutionId(institutionId);


        doReturn(1).when(appointmentService).getVByPassportId(anyString(), any(ApiResponse.class));

        // Act
        ApiResponse<AppointmentInfoResponse, Object> res = appointmentService.queryAppointmentInfo(params);
        assertEquals(ErrorCodeEnum.PlannerChanged.getCode(),res.getErrorCode());
    }

    @Test
    public void testQueryAppointmentInfo_DecryptMobileQueryERROR(){
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("7KyFbfdhIzHf3viG/XyIhw==");
        appointmentInfo.setAppointment(new Date());
        when(appointmentMysqlService.queryAppointmentByParams(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(null);
        when(passportBindApi.getPassportBindInfo(anyString())).thenReturn(customerNo);


        when(userInfoAPIService.getUserBaseInfo(anyString())).thenReturn(userBaseInfoResponse);

        DecryptMobileResponse de = new DecryptMobileResponse();
        de.setSuccess(true);
        de.setResult(decryptMobile);
        when(userInfoAPIService.decryptMobile(any())).thenReturn(null);

        LcsPersonalInfoResponse lcsInfo = new LcsPersonalInfoResponse();
        lcsInfo.setFinancialPlannerNickName("李四");
        when(lcsMysqlService.querySingle(anyString(), anyString())).thenReturn(lcsInfo);

        AppointmentQueryRequest params = new AppointmentQueryRequest();
        params.setPassportid(passportId);
        params.setFinancialPlannerId(financialPlannerId);
        params.setInstitutionId(institutionId);


        doReturn(1).when(appointmentService).getVByPassportId(anyString(), any(ApiResponse.class));

        // Act
        ApiResponse<AppointmentInfoResponse, Object> res = appointmentService.queryAppointmentInfo(params);
        assertEquals(ErrorCodeEnum.DecryptMobileQueryERROR.getCode(),res.getErrorCode());
    }

    @Test
    public void testQueryCanBeAppointment_Positive() {
        AppointmentQueryRequest request = new AppointmentQueryRequest();
        request.setFinancialPlannerId(financialPlannerId);
        request.setInstitutionId(institutionId);

        Date now = new Date();
        String key = DateHelper.dateToStr(now, DateHelper.FORMAT_YYYY_MM_DD);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        int index = calendar.get(Calendar.HOUR_OF_DAY) > 12 ? 1: 0;


        List<AppointmentInfo> lcs = new ArrayList<>();
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("7KyFbfdhIzHf3viG/XyIhw==");
        appointmentInfo.setAppointment(now);
        appointmentInfo.setStatus(0);
        lcs.add(appointmentInfo);

        when(appointmentMysqlService.queryAppointmentInfoByPassportIdAndTradeDays(any(),any(),any(),any(),any())).thenReturn(lcs);

        ApiResponse<AppointmentAvailableResponse, Object> res = appointmentService.queryCanBeAppointment(request);

        assertEquals(0,res.getErrorCode());
        assertEquals(true,res.getSuccess());
        assertNotNull(res.getData());
        assertNotNull(res.getData().getAppointment());
        assertEquals(res.getData().getAppointment().get(key).get(index),1);
    }

    @Test
    public void testAddAppointmentSuccess(){
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment(DateHelper.dateToStr(LocalDateTime.now().plusDays(14),DateHelper.FORMAT_YYYY_MM_DD + " 11:30:00"));
        request.setPassportid(passportId);
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);

        when(userRoleMysql.selectByInsAndPassId(anyString(),anyString())).thenReturn(new UserRolePO());

        Date now = new Date();
        List<AppointmentInfo> lcs = new ArrayList<>();
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        appointmentInfo.setAppointment(now);
        appointmentInfo.setStatus(0);
        lcs.add(appointmentInfo);

        when(appointmentMysqlService.queryAppointmentInfoByPassportIdAndTradeDays(any(),any(),any(),any(),any())).thenReturn(lcs);

        when(passportBindApi.getPassportBindInfo(any())).thenReturn(customerNo);

        when(userInfoAPIService.getUserBaseInfo(any())).thenReturn(userBaseInfoResponse);

        when(appointmentMysqlService.hasRepeatAppointment(any(),any(),any())).thenReturn(false);

        when(appointmentMysqlService.addOrUpdateAppointment(any(),any(),any(),any(),any(),any(),any(),any(),any())).thenReturn(true);

        when(contractMysql.getLastRecordByPassAndFpass(any(),any(),any())).thenReturn(null);
        when(contractMysql.addBindRecord(any(),any(),any(),any(),any(),any())).thenReturn(true);

        when(imApiService.sendAppointInfoMsg(any(),any(),any(),any(),any())).thenReturn(null);
        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);

        assertEquals(0,res.getErrorCode());
    }

    @Test
    public void testAddAppointment_LackParameterError(){
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment(DateHelper.dateToStr(LocalDateTime.now().plusDays(14),DateHelper.FORMAT_YYYY_MM_DD + " 11:30:00"));
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setFinancialPlannerId(financialPlannerId);
        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);
        assertEquals(ErrorCodeEnum.LackParameter.getCode(),res.getErrorCode());
    }

    @Test
    public void testAddAppointment_NoCustomerNo(){
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment(DateHelper.dateToStr(LocalDateTime.now().plusDays(14),DateHelper.FORMAT_YYYY_MM_DD + " 11:30:00"));
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);

        when(userRoleMysql.selectByInsAndPassId(any(),any())).thenReturn(new UserRolePO());

        when(appointmentMysqlService.queryAppointmentByParams(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(null);

        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);
        assertEquals(ErrorCodeEnum.NoCustomerNo.getCode(),res.getErrorCode());
    }

    @Test
    public void testAddAppointment_UserBaseInfoQueryERROR(){
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment(DateHelper.dateToStr(LocalDateTime.now().plusDays(14),DateHelper.FORMAT_YYYY_MM_DD + " 11:30:00"));
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);

        when(appointmentMysqlService.queryAppointmentByParams(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(null);
        when(passportBindApi.getPassportBindInfo(any())).thenReturn(customerNo);
        when(userRoleMysql.selectByInsAndPassId(any(),any())).thenReturn(new UserRolePO());
        when(userInfoAPIService.getUserBaseInfo(any())).thenReturn(null);

        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);
        assertEquals(ErrorCodeEnum.UserBaseInfoQueryERROR.getCode(),res.getErrorCode());
    }

    @Test
    public void LcsMatchingERROR(){
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment(DateHelper.dateToStr(LocalDateTime.now().plusDays(14),DateHelper.FORMAT_YYYY_MM_DD + " 11:30:00"));
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);

        when(appointmentMysqlService.queryAppointmentByParams(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(null);
        when(passportBindApi.getPassportBindInfo(any())).thenReturn(customerNo);
        when(userInfoAPIService.getUserBaseInfo(any())).thenReturn(userBaseInfoResponse);
        when(lcsMysqlService.querySingle(anyString(), anyString())).thenReturn(null);

        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);
        assertEquals(ErrorCodeEnum.LcsMatchingERROR.getCode(),res.getErrorCode());
    }

    @Test
    public void testUpdateAppointment() {
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment(DateHelper.dateToStr(LocalDateTime.now().plusDays(7),DateHelper.FORMAT_YYYY_MM_DD + " 11:30:00"));
        request.setPassportid(passportId);
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);
        request.setId("123");

        when(userRoleMysql.selectByInsAndPassId(anyString(),anyString())).thenReturn(new UserRolePO());

        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_MONTH,1);
        List<AppointmentInfo> lcs = new ArrayList<>();
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        appointmentInfo.setAppointment(calendar.getTime());
        appointmentInfo.setStatus(0);
        appointmentInfo.setPassportId(passportId);
        lcs.add(appointmentInfo);

        when(appointmentMysqlService.queryAppointmentInfoByPassportIdAndTradeDays(any(),any(),any(),any(),any())).thenReturn(lcs);
        when(appointmentMysqlService.queryAppointmentById(any())).thenReturn(appointmentInfo);

        when(passportBindApi.getPassportBindInfo(any())).thenReturn(customerNo);

        when(userInfoAPIService.getUserBaseInfo(any())).thenReturn(userBaseInfoResponse);

        when(appointmentMysqlService.hasRepeatAppointment(any(),any(),any())).thenReturn(false);

        when(appointmentMysqlService.addOrUpdateAppointment(any(),any(),any(),any(),any(),any(),any(),any(),any())).thenReturn(true);

        when(contractMysql.getLastRecordByPassAndFpass(any(),any(),any())).thenReturn(null);
        when(contractMysql.addBindRecord(any(),any(),any(),any(),any(),any())).thenReturn(true  );

        when(imApiService.sendAppointInfoMsg(any(),any(),any(),any(),any())).thenReturn(null);
        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);

        assertEquals(0,res.getErrorCode());
    }
    @Test
    public void testAddAppointment_RepeatAddAppointment() {
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment(DateHelper.dateToStr(LocalDateTime.now().plusDays(7), DateHelper.FORMAT_YYYY_MM_DD + " 11:30:00"));
        request.setPassportid(passportId);
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);

        when(userRoleMysql.selectByInsAndPassId(anyString(), anyString())).thenReturn(new UserRolePO());

        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        List<AppointmentInfo> lcs = new ArrayList<>();
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        appointmentInfo.setAppointment(calendar.getTime());
        appointmentInfo.setStatus(0);
        appointmentInfo.setPassportId(passportId);
        lcs.add(appointmentInfo);

        when(appointmentMysqlService.queryAppointmentInfoByPassportIdAndTradeDays(any(), any(), any(), any(), any())).thenReturn(lcs);
        when(appointmentMysqlService.queryAppointmentById(any())).thenReturn(appointmentInfo);

        when(passportBindApi.getPassportBindInfo(any())).thenReturn(customerNo);

        when(userInfoAPIService.getUserBaseInfo(any())).thenReturn(userBaseInfoResponse);

        when(appointmentMysqlService.hasRepeatAppointment(any(), any(), any())).thenReturn(true);

        when(appointmentMysqlService.addOrUpdateAppointment(any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(true);

        when(contractMysql.getLastRecordByPassAndFpass(any(), any(), any())).thenReturn(null);
        when(contractMysql.addBindRecord(any(), any(), any(), any(), any(), any())).thenReturn(true);

        when(imApiService.sendAppointInfoMsg(any(), any(), any(), any(), any())).thenReturn(null);
        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);

        assertEquals(ErrorCodeEnum.RepeatAddAppointment.getCode(), res.getErrorCode());
    }

    @Test
    public void testAddAppointment_RollBack() {
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment(DateHelper.dateToStr(LocalDateTime.now().plusDays(7), DateHelper.FORMAT_YYYY_MM_DD + " 11:30:00"));
        request.setPassportid(passportId);
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);

        when(userRoleMysql.selectByInsAndPassId(anyString(), anyString())).thenReturn(new UserRolePO());

        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        List<AppointmentInfo> lcs = new ArrayList<>();
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        appointmentInfo.setAppointment(calendar.getTime());
        appointmentInfo.setStatus(0);
        appointmentInfo.setPassportId(passportId);
        lcs.add(appointmentInfo);

        when(appointmentMysqlService.queryAppointmentInfoByPassportIdAndTradeDays(any(), any(), any(), any(), any())).thenReturn(lcs);
        when(appointmentMysqlService.queryAppointmentById(any())).thenReturn(appointmentInfo);

        when(passportBindApi.getPassportBindInfo(any())).thenReturn(customerNo);

        when(userInfoAPIService.getUserBaseInfo(any())).thenReturn(userBaseInfoResponse);

        when(appointmentMysqlService.hasRepeatAppointment(any(), any(), any())).thenReturn(false);

        when(appointmentMysqlService.addOrUpdateAppointment(any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(false);

        when(contractMysql.getLastRecordByPassAndFpass(any(), any(), any())).thenReturn(null);
        when(contractMysql.addBindRecord(any(), any(), any(), any(), any(), any())).thenReturn(true);

        when(imApiService.sendAppointInfoMsg(any(), any(), any(), any(), any())).thenReturn(null);

        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);

        assertEquals(ResultCode.errorcode501, res.getErrorCode());
    }

    @Test
    public void testAddAppointment_Afternoon() {
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment(DateHelper.dateToStr(LocalDateTime.now().plusDays(7), DateHelper.FORMAT_YYYY_MM_DD + " 18:00:00"));
        request.setPassportid(passportId);
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);

        when(userRoleMysql.selectByInsAndPassId(anyString(), anyString())).thenReturn(new UserRolePO());

        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        List<AppointmentInfo> lcs = new ArrayList<>();
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        appointmentInfo.setAppointment(calendar.getTime());
        appointmentInfo.setStatus(0);
        appointmentInfo.setPassportId(passportId);
        lcs.add(appointmentInfo);

        when(appointmentMysqlService.queryAppointmentInfoByPassportIdAndTradeDays(any(), any(), any(), any(), any())).thenReturn(lcs);
        when(appointmentMysqlService.queryAppointmentById(any())).thenReturn(appointmentInfo);

        when(passportBindApi.getPassportBindInfo(any())).thenReturn(customerNo);

        when(userInfoAPIService.getUserBaseInfo(any())).thenReturn(userBaseInfoResponse);

        when(appointmentMysqlService.hasRepeatAppointment(any(), any(), any())).thenReturn(false);

        when(appointmentMysqlService.addOrUpdateAppointment(any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(true);

        when(contractMysql.getLastRecordByPassAndFpass(any(), any(), any())).thenReturn(null);
        when(contractMysql.addBindRecord(any(), any(), any(), any(), any(), any())).thenReturn(true);

        when(imApiService.sendAppointInfoMsg(any(), any(), any(), any(), any())).thenReturn(null);

        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);

        assertEquals(0, res.getErrorCode());
    }

    @Test
    public void testCancelAppointment() {
        AppointmentQueryRequest request = new AppointmentQueryRequest();
        request.setPassportid(passportId);
        request.setId("asd");
        request.setFinancialPlannerId(financialPlannerId);

        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_MONTH,1);
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        appointmentInfo.setAppointment(calendar.getTime());
        appointmentInfo.setStatus(0);
        appointmentInfo.setPassportId(passportId);

        when(appointmentMysqlService.queryAppointmentById(any())).thenReturn(appointmentInfo);
        when(appointmentMysqlService.deleteAppointment(any())).thenReturn(true);

        ApiResponse<Boolean, Object> res = appointmentService.cancelAppointment(request);

        assertEquals(0,res.getErrorCode());
    }

    @Test
    public void testQueryCanBeAppointment_LackParameter() {
        AppointmentQueryRequest request = new AppointmentQueryRequest();
        request.setFinancialPlannerId(financialPlannerId);

        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);

        List<AppointmentInfo> lcs = new ArrayList<>();
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("7KyFbfdhIzHf3viG/XyIhw==");
        appointmentInfo.setAppointment(now);
        appointmentInfo.setStatus(0);
        lcs.add(appointmentInfo);

        when(appointmentMysqlService.queryAppointmentInfoByPassportIdAndTradeDays(any(),any(),any(),any(),any())).thenReturn(lcs);

        ApiResponse<AppointmentAvailableResponse, Object> res = appointmentService.queryCanBeAppointment(request);
        assertEquals(ErrorCodeEnum.LackParameter.getCode(),res.getErrorCode());
    }


    @Test
    public void testGetVByPassportId() {
        ApiResponse<AppointmentInfoResponse, Object> res = new ApiResponse<>();
        appointmentService.getVByPassportId("123123",res);
    }

    @Test
    public void testCheckParams_PhoneError(){
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment(DateHelper.dateToStr(LocalDateTime.now().plusDays(7),DateHelper.FORMAT_YYYY_MM_DD + " 11:30:00"));
        request.setPhone("Ha2hgPB7HDwG3SBRQ==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);
        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);
        assertEquals(ErrorCodeEnum.LackParameter.getCode(),res.getErrorCode());
    }

    @Test
    public void testCheckParams_MobileFormatError(){
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment(DateHelper.dateToStr(LocalDateTime.now().plusDays(7),DateHelper.FORMAT_YYYY_MM_DD + " 11:30:00"));
        request.setPhone("KSuJDMrDJszbwJEJzBrQ6A==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);
        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);
        assertEquals(ErrorCodeEnum.MobileFormatError.getCode(),res.getErrorCode());
    }

    @Test
    public void testCheckParams_DateFormatERROR(){
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment("2022-01-0111:30:00");
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);

        when(userRoleMysql.selectByInsAndPassId(anyString(),anyString())).thenReturn(new UserRolePO());
        appointmentService.addOrUpdateAppointment(request);
    }

    @Test
    public void testCheckParams_AppointmentNullERROR(){
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment("2022-01-01 11:30:00");
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setId("asd");
        request.setFinancialPlannerId(financialPlannerId);

        when(userRoleMysql.selectByInsAndPassId(anyString(),anyString())).thenReturn(new UserRolePO());
        when(appointmentMysqlService.queryAppointmentById(any())).thenReturn(null);
        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);
        assertEquals(ErrorCodeEnum.AppointmentNullERROR.getCode(),res.getErrorCode());
    }

    @Test
    public void testCheckParams_OperationError() {
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment("2022-01-01 11:30:00");
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setId("asd");
        request.setFinancialPlannerId(financialPlannerId);
        request.setPassportid(passportId);

        AppointmentInfo info = new AppointmentInfo();
        info.setPassportId(passportId+"12312");
        when(userRoleMysql.selectByInsAndPassId(anyString(),anyString())).thenReturn(new UserRolePO());
        when(appointmentMysqlService.queryAppointmentById(any())).thenReturn(info);
        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);

        assertEquals(ErrorCodeEnum.OperationError.getCode(),res.getErrorCode());
    }

    @Test
    public void testCheckParams_AppointmentUnableOperate() {
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment("2022-01-01 11:30:00");
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setId("asd");
        request.setFinancialPlannerId(financialPlannerId);
        request.setPassportid(passportId);

        AppointmentInfo info = new AppointmentInfo();
        info.setPassportId(passportId);
        info.setAppointment(DateHelper.stringToDate2("2022-01-01 11:30:00",DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));
        when(userRoleMysql.selectByInsAndPassId(anyString(),anyString())).thenReturn(new UserRolePO());
        when(appointmentMysqlService.queryAppointmentById(any())).thenReturn(info);
        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);

        assertEquals(ErrorCodeEnum.AppointmentUnableOperate.getCode(),res.getErrorCode());
    }

    @Test
    public void testCheckParams_AppointmentUnableOperate_morning() {
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment("2022-01-01 11:30:00");
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setId("asd");
        request.setFinancialPlannerId(financialPlannerId);
        request.setPassportid(passportId);

        AppointmentInfo info = new AppointmentInfo();
        info.setPassportId(passportId);
        info.setAppointment(new Date());
        when(userRoleMysql.selectByInsAndPassId(anyString(),anyString())).thenReturn(new UserRolePO());
        when(appointmentMysqlService.queryAppointmentById(any())).thenReturn(info);
        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);

        assertEquals(ErrorCodeEnum.AppointmentUnableOperate.getCode(),res.getErrorCode());
    }

    @Test
    public void testCheckParams_AppointmentERROR() {
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment("2022-01-01 11:30:00");
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);
        request.setPassportid(passportId);

        AppointmentInfo info = new AppointmentInfo();
        info.setPassportId(passportId);
        info.setAppointment(new Date());
        when(userRoleMysql.selectByInsAndPassId(anyString(),anyString())).thenReturn(new UserRolePO());
        when(appointmentMysqlService.queryAppointmentById(any())).thenReturn(info);
        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);

        assertEquals(ErrorCodeEnum.AppointmentERROR.getCode(),res.getErrorCode());
    }

    @Test
    public void test_LOCKERROR(){
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment("2022-01-01 11:30:00");
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);
        request.setPassportid(passportId);
        when(redisConn.incrBy(any(),any(),any())).thenReturn(-1L);
        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);
        assertEquals(ErrorCodeEnum.LOCKERROR.getCode(),res.getErrorCode());
    }
    @Test
    public void testCheckParams_afternoon(){
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment(DateHelper.dateToStr(LocalDateTime.now().plusDays(14),DateHelper.FORMAT_YYYY_MM_DD + " 18:00:00"));
        request.setPassportid(passportId);
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);

        when(userRoleMysql.selectByInsAndPassId(anyString(),anyString())).thenReturn(new UserRolePO());

        Date now = DateHelper.stringToDate2(request.getAppointment(),DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
        List<AppointmentInfo> lcs = new ArrayList<>();
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        appointmentInfo.setAppointment(now);
        appointmentInfo.setStatus(0);
        lcs.add(appointmentInfo);

        when(appointmentMysqlService.queryAppointmentInfoByPassportIdAndTradeDays(any(),any(),any(),any(),any())).thenReturn(lcs);

        when(passportBindApi.getPassportBindInfo(any())).thenReturn(customerNo);

        when(userInfoAPIService.getUserBaseInfo(any())).thenReturn(userBaseInfoResponse);

        when(appointmentMysqlService.hasRepeatAppointment(any(),any(),any())).thenReturn(false);

        when(appointmentMysqlService.addOrUpdateAppointment(any(),any(),any(),any(),any(),any(),any(),any(),any())).thenReturn(true);

        when(contractMysql.getLastRecordByPassAndFpass(any(),any(),any())).thenReturn(null);
        when(contractMysql.addBindRecord(any(),any(),any(),any(),any(),any())).thenReturn(true  );

        when(imApiService.sendAppointInfoMsg(any(),any(),any(),any(),any())).thenReturn(null);
        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);
        assertEquals(ErrorCodeEnum.AppointmentUnavailable.getCode(),res.getErrorCode());
    }

    @Test
    public void testCheckParams_AppointmentERROR_Over_twoMonth(){
        AddAppointmentRequest request = new AddAppointmentRequest();
        request.setAppointment(DateHelper.dateToStr(LocalDateTime.now().plusDays(64),DateHelper.FORMAT_YYYY_MM_DD + " 18:00:00"));
        request.setPassportid(passportId);
        request.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        request.setInstitutionId(institutionId);
        request.setFinancialPlannerId(financialPlannerId);

        when(userRoleMysql.selectByInsAndPassId(anyString(),anyString())).thenReturn(new UserRolePO());

        List<AppointmentInfo> lcs = new ArrayList<>();
        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setPhone("Ha2hgPB7HDwG3KOMl1SBRQ==");
        appointmentInfo.setAppointment(new Date());
        appointmentInfo.setStatus(0);
        lcs.add(appointmentInfo);

        when(appointmentMysqlService.queryAppointmentInfoByPassportIdAndTradeDays(any(),any(),any(),any(),any())).thenReturn(lcs);

        when(passportBindApi.getPassportBindInfo(any())).thenReturn(customerNo);

        when(userInfoAPIService.getUserBaseInfo(any())).thenReturn(userBaseInfoResponse);

        when(appointmentMysqlService.hasRepeatAppointment(any(),any(),any())).thenReturn(false);

        when(appointmentMysqlService.addOrUpdateAppointment(any(),any(),any(),any(),any(),any(),any(),any(),any())).thenReturn(true);

        when(contractMysql.getLastRecordByPassAndFpass(any(),any(),any())).thenReturn(null);
        when(contractMysql.addBindRecord(any(),any(),any(),any(),any(),any())).thenReturn(true  );

        when(imApiService.sendAppointInfoMsg(any(),any(),any(),any(),any())).thenReturn(null);
        ApiResponse<Boolean, Object> res = appointmentService.addOrUpdateAppointment(request);
        assertEquals(ErrorCodeEnum.AppointmentERROR.getCode(),res.getErrorCode());
    }
}